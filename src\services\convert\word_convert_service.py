import shutil

from .base_converter_service import BaseConverterService
from typing import Optional, List
import os
import logging

logger = logging.getLogger(__name__)


class WordConvertService(BaseConverterService):
    """修复文档关闭问题的Word转换服务"""

    def convert_to_images(self, word_path: str, output_prefix: Optional[str] = None,
                          format: str = "png") -> List[str]:
        """
        安全版本的Word转图片方法
        """
        # 处理输出路径
        if output_prefix is None:
            output_prefix = os.path.splitext(word_path)[0]

        # 确保输出目录存在
        output_dir = os.path.dirname(output_prefix)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        def save_word_as_pdf(app, source_path, pdf_path):
            """更健壮的Word保存PDF方法"""
            doc = None
            try:
                doc = app.Documents.Open(source_path)
                doc.ExportAsFixedFormat(
                    OutputFileName=pdf_path,
                    ExportFormat=17,  # PDF
                    OpenAfterExport=False
                )
            except Exception as e:
                raise RuntimeError(f"Word保存PDF失败: {str(e)}") from e
            finally:
                if doc:
                    doc.Close(SaveChanges=False)

        # 转换流程
        pdf_path, temp_dir = None, None
        try:
            pdf_path, temp_dir = self._convert_to_pdf(
                word_path,
                "Word.Application",
                save_word_as_pdf
            )
            return self._pdf_to_images(pdf_path, output_prefix, format)
        finally:
            # 确保清理临时文件
            if temp_dir and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
            # 确保PDF文件已关闭
            if pdf_path and os.path.exists(pdf_path):
                try:
                    os.remove(pdf_path)
                except:
                    pass
