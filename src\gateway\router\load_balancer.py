from typing import List, Optional
import random
from src.gateway.registry.service_registry import ServiceInfo

class LoadBalancer:
    """负载均衡器"""
    
    @staticmethod
    def round_robin(services: List[ServiceInfo], last_index: int = -1) -> Optional[ServiceInfo]:
        """轮询策略"""
        if not services:
            return None
        active_services = [s for s in services if s.status == "active"]
        if not active_services:
            return None
        next_index = (last_index + 1) % len(active_services)
        return active_services[next_index]
    
    @staticmethod
    def random(services: List[ServiceInfo]) -> Optional[ServiceInfo]:
        """随机策略"""
        active_services = [s for s in services if s.status == "active"]
        return random.choice(active_services) if active_services else None
    
    @staticmethod
    def least_connections(services: List[ServiceInfo], connection_counts: dict) -> Optional[ServiceInfo]:
        """最少连接策略"""
        active_services = [s for s in services if s.status == "active"]
        if not active_services:
            return None
        return min(active_services, key=lambda s: connection_counts.get(s.service_id, 0))

class ServiceRouter:
    """服务路由器"""
    def __init__(self, load_balancer: LoadBalancer = None):
        self.load_balancer = load_balancer or LoadBalancer()
        self._last_index = -1
        self._connection_counts = {}
    
    def route(self, service_name: str, strategy: str = "round_robin") -> Optional[ServiceInfo]:
        """路由到指定服务"""
        from .service_registry import service_registry
        
        services = service_registry.get_services_by_name(service_name)
        if not services:
            return None
        
        if strategy == "round_robin":
            service = self.load_balancer.round_robin(services, self._last_index)
            if service:
                self._last_index = services.index(service)
        elif strategy == "random":
            service = self.load_balancer.random(services)
        elif strategy == "least_connections":
            service = self.load_balancer.least_connections(services, self._connection_counts)
        else:
            service = self.load_balancer.round_robin(services)
        
        if service:
            self._connection_counts[service.service_id] = self._connection_counts.get(service.service_id, 0) + 1
        
        return service
    
    def release_connection(self, service_id: str) -> None:
        """释放连接"""
        if service_id in self._connection_counts:
            self._connection_counts[service_id] = max(0, self._connection_counts[service_id] - 1)

# 创建全局服务路由器实例
service_router = ServiceRouter() 