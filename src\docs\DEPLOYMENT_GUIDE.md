# Bibrain 部署指南

## 1. 系统要求

### 1.1 硬件要求

- CPU: 8 核以上
- 内存: 16GB 以上
- 存储: 100GB 以上 SSD
- 网络: 千兆网络

### 1.2 软件要求

- 操作系统: Ubuntu 20.04 LTS 或更高版本
- Docker: 20.10 或更高版本
- Kubernetes: 1.20 或更高版本
- Python: 3.9 或更高版本

## 2. 环境准备

### 2.1 安装依赖

```bash
# 更新系统
sudo apt update
sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git python3-pip

# 安装 Docker
curl -fsSL https://get.docker.com | sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.5.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装 kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
```

### 2.2 配置 Kubernetes

```bash
# 安装 k3s
curl -sfL https://get.k3s.io | sh -

# 配置 kubectl
mkdir -p ~/.kube
sudo cp /etc/rancher/k3s/k3s.yaml ~/.kube/config
sudo chown $USER:$USER ~/.kube/config
```

## 3. 部署步骤

### 3.1 克隆代码

```bash
git clone https://github.com/your-org/bibrain.git
cd src
```

### 3.2 配置环境变量

```bash
# 创建环境变量文件
cp .env.example .env

# 编辑环境变量
vim .env

# 主要配置项
BIBRAIN_ENV=production
BIBRAIN_DOMAIN=api.src.com
BIBRAIN_SECRET_KEY=your-secret-key
BIBRAIN_DB_HOST=postgres
BIBRAIN_DB_PORT=5432
BIBRAIN_DB_NAME=src
BIBRAIN_DB_USER=src
BIBRAIN_DB_PASSWORD=your-password
```

### 3.3 部署数据库

```bash
# 创建数据库
kubectl apply -f k8s/postgres.yaml

# 等待数据库就绪
kubectl wait --for=condition=ready pod -l app=postgres
```

### 3.4 部署 Redis

```bash
# 部署 Redis
kubectl apply -f k8s/redis.yaml

# 等待 Redis 就绪
kubectl wait --for=condition=ready pod -l app=redis
```

### 3.5 部署应用

```bash
# 构建镜像
docker build -t src:latest .

# 部署应用
kubectl apply -f k8s/src.yaml

# 等待应用就绪
kubectl wait --for=condition=ready pod -l app=src
```

## 4. 配置管理

### 4.1 配置中心

```bash
# 创建配置
kubectl create configmap src-config --from-file=config/

# 更新配置
kubectl create configmap src-config --from-file=config/ -o yaml --dry-run | kubectl replace -f -
```

### 4.2 密钥管理

```bash
# 创建密钥
kubectl create secret generic src-secrets \
  --from-literal=db-password=your-password \
  --from-literal=api-key=your-api-key

# 更新密钥
kubectl create secret generic src-secrets \
  --from-literal=db-password=new-password \
  --from-literal=api-key=new-api-key \
  -o yaml --dry-run | kubectl replace -f -
```

## 5. 监控配置

### 5.1 部署 Prometheus

```bash
# 部署 Prometheus
kubectl apply -f k8s/prometheus.yaml

# 配置监控目标
kubectl apply -f k8s/prometheus-config.yaml
```

### 5.2 部署 Grafana

```bash
# 部署 Grafana
kubectl apply -f k8s/grafana.yaml

# 配置数据源
kubectl apply -f k8s/grafana-datasource.yaml
```

### 5.3 配置告警

```bash
# 部署 AlertManager
kubectl apply -f k8s/alertmanager.yaml

# 配置告警规则
kubectl apply -f k8s/alertmanager-rules.yaml
```

## 6. 日志管理

### 6.1 部署 ELK Stack

```bash
# 部署 Elasticsearch
kubectl apply -f k8s/elasticsearch.yaml

# 部署 Logstash
kubectl apply -f k8s/logstash.yaml

# 部署 Kibana
kubectl apply -f k8s/kibana.yaml
```

### 6.2 配置日志收集

```bash
# 部署 Filebeat
kubectl apply -f k8s/filebeat.yaml

# 配置日志输出
kubectl apply -f k8s/filebeat-config.yaml
```

## 7. 高可用配置

### 7.1 负载均衡

```bash
# 部署 Nginx Ingress Controller
kubectl apply -f k8s/nginx-ingress.yaml

# 配置负载均衡规则
kubectl apply -f k8s/ingress-rules.yaml
```

### 7.2 自动扩缩容

```bash
# 配置 HPA
kubectl apply -f k8s/hpa.yaml

# 配置 VPA
kubectl apply -f k8s/vpa.yaml
```

## 8. 备份策略

### 8.1 数据库备份

```bash
# 创建备份任务
kubectl apply -f k8s/backup-job.yaml

# 配置备份计划
kubectl apply -f k8s/backup-cronjob.yaml
```

### 8.2 配置备份

```bash
# 备份配置
kubectl get configmap src-config -o yaml > config-backup.yaml

# 备份密钥
kubectl get secret src-secrets -o yaml > secrets-backup.yaml
```

## 9. 安全配置

### 9.1 网络策略

```bash
# 配置网络策略
kubectl apply -f k8s/network-policy.yaml

# 配置 Pod 安全策略
kubectl apply -f k8s/psp.yaml
```

### 9.2 证书管理

```bash
# 部署 cert-manager
kubectl apply -f k8s/cert-manager.yaml

# 配置证书
kubectl apply -f k8s/certificate.yaml
```

## 10. 运维管理

### 10.1 日常维护

```bash
# 检查系统状态
kubectl get pods
kubectl get services
kubectl get deployments

# 查看日志
kubectl logs -f deployment/src

# 检查资源使用
kubectl top pods
kubectl top nodes
```

### 10.2 故障处理

```bash
# 查看事件
kubectl get events

# 检查 Pod 状态
kubectl describe pod <pod-name>

# 检查服务状态
kubectl describe service <service-name>
```

### 10.3 更新部署

```bash
# 更新镜像
kubectl set image deployment/src src=src:new-version

# 回滚部署
kubectl rollout undo deployment/src
```

## 11. 性能优化

### 11.1 资源优化

- 配置资源限制
- 优化 Pod 调度
- 实现资源监控

### 11.2 网络优化

- 配置网络策略
- 优化负载均衡
- 实现流量控制

### 11.3 存储优化

- 配置存储类
- 优化数据访问
- 实现数据压缩

## 12. 故障恢复

### 12.1 数据恢复

```bash
# 恢复数据库
kubectl exec -it <postgres-pod> -- psql -U src -d src -f backup.sql

# 恢复配置
kubectl apply -f config-backup.yaml
kubectl apply -f secrets-backup.yaml
```

### 12.2 服务恢复

```bash
# 重启服务
kubectl rollout restart deployment/src

# 检查服务状态
kubectl rollout status deployment/src
```

## 13. 监控指标

### 13.1 系统指标

- CPU 使用率
- 内存使用率
- 磁盘使用率
- 网络流量

### 13.2 应用指标

- 请求延迟
- 错误率
- 并发数
- 资源使用

### 13.3 业务指标

- 用户数
- 请求数
- 成功率
- 响应时间

## 14. 告警配置

### 14.1 系统告警

- CPU 使用率超过 80%
- 内存使用率超过 80%
- 磁盘使用率超过 80%
- 网络错误率超过 1%

### 14.2 应用告警

- 请求延迟超过 1s
- 错误率超过 1%
- 并发数超过阈值
- 资源使用超过限制

### 14.3 业务告警

- 用户数异常
- 请求数异常
- 成功率异常
- 响应时间异常 