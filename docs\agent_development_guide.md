# Agent开发指南

## 简介

本文档提供了如何开发一个Agent并将其注册到Bibrain Gateway的详细指南。通过遵循这些步骤，开发者可以快速创建一个新的Agent并将其集成到系统中。

## Agent能力类型

Agent可以支持以下能力类型：

- `CHAT`: 对话能力
- `TASK`: 任务处理
- `TOOL`: 工具调用
- `KNOWLEDGE`: 知识库
- `VISION`: 视觉能力
- `AUDIO`: 音频能力

## 开发步骤

### 1. 定义Agent信息

首先，需要定义Agent的基本信息，包括：

```python
from gateway.models.agent import AgentInfo, AgentMetadata, AgentCapability

agent_info = AgentInfo(
    agent_id="my-agent-001",
    name="My Custom Agent",
    host="localhost",
    port=8000,
    metadata=AgentMetadata(
        description="这是一个自定义Agent",
        capabilities=[AgentCapability.CHAT, AgentCapability.TASK],
        version="1.0.0",
        author="开发者名称",
        tags=["chat", "task"],
        parameters={
            "max_tokens": 1000,
            "temperature": 0.7
        },
        examples=[
            {
                "input": "你好",
                "output": "你好！我是你的AI助手。"
            }
        ]
    ),
    endpoints={
        "chat": "/api/chat",
        "task": "/api/task"
    },
    rate_limit={
        "requests_per_minute": 60
    }
)
```

### 2. 实现Agent服务

创建一个FastAPI应用来实现Agent的功能：

```python
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI()

class ChatRequest(BaseModel):
    message: str

@app.post("/api/chat")
async def chat(request: ChatRequest):
    # 实现聊天逻辑
    return {"response": "这是回复"}

@app.post("/api/task")
async def task(request: dict):
    # 实现任务处理逻辑
    return {"result": "任务完成"}
```

### 3. 注册Agent

在Agent启动时，需要向Gateway注册：

```python
import httpx
import asyncio

async def register_agent():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://gateway-host:port/gateway/agents/register",
            json=agent_info.dict()
        )
        if response.status_code == 200:
            print("Agent注册成功")
        else:
            print(f"Agent注册失败: {response.text}")

# 启动时注册
asyncio.run(register_agent())
```

### 4. 实现心跳机制

定期向Gateway发送心跳以保持Agent的活跃状态：

```python
async def send_heartbeat():
    while True:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"http://gateway-host:port/gateway/agents/{agent_info.agent_id}/heartbeat"
                )
                if response.status_code != 200:
                    print("心跳更新失败")
        except Exception as e:
            print(f"心跳发送错误: {e}")
        await asyncio.sleep(30)  # 每30秒发送一次心跳

# 启动心跳
asyncio.create_task(send_heartbeat())
```

### 5. 优雅退出

在Agent关闭时，需要向Gateway注销：

```python
import signal

async def unregister_agent():
    async with httpx.AsyncClient() as client:
        response = await client.delete(
            f"http://gateway-host:port/gateway/agents/unregister/{agent_info.agent_id}"
        )
        if response.status_code == 200:
            print("Agent注销成功")
        else:
            print(f"Agent注销失败: {response.text}")

def signal_handler(sig, frame):
    print("正在关闭Agent...")
    asyncio.run(unregister_agent())
    exit(0)

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)
```

## 最佳实践

1. **错误处理**：
   - 实现完善的错误处理机制
   - 记录详细的错误日志
   - 优雅处理异常情况

2. **性能优化**：
   - 实现请求限流
   - 使用异步处理
   - 优化资源使用

3. **安全性**：
   - 实现认证机制
   - 加密敏感数据
   - 验证输入数据

4. **可维护性**：
   - 编写清晰的文档
   - 添加单元测试
   - 遵循代码规范

## 示例代码

完整的示例代码可以在 `examples/agent` 目录下找到。

## 常见问题

1. **Q: 如何处理Agent注册失败？**
   A: 检查网络连接和Gateway地址是否正确，确保Agent信息符合要求。

2. **Q: 心跳更新失败怎么办？**
   A: 检查网络连接，确保Gateway服务正常运行。

3. **Q: 如何实现Agent的负载均衡？**
   A: Gateway会自动处理负载均衡，开发者只需要关注Agent的功能实现。

## 支持

如有问题，请参考以下资源：

- 项目文档：`docs/`
- 示例代码：`examples/`
- 问题反馈：GitHub Issues 