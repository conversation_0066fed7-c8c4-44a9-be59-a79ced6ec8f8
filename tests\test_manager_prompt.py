import os
import sys
# 动态添加 src 文件夹到 sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../src")))

# 从 src.prompt.config 导入需要的内容
from prompt.config import WORKFLOW_TEST_PROMPT

if __name__ == "__main__":

    agents=[{'agent_name': 'llm_agent', 'description': 'Uses LLM for text generation'}, {'agent_name': 'different_analysis_agent', 'description': 'A useful tool to analyze difference between documents'}, {'agent_name': 'manger_agent', 'description': 'A orchestrator use llm to define workflow'}]
    # 处理 agents 数据，使其格式化为匹配字符串
    formatted_agents = "\n".join([
        f"{agent['agent_name']}: {agent['description']}" for agent in agents
    ])

    input_data = "Analysis this two files for me"
    mre = WORKFLOW_TEST_PROMPT.format(agentsinfo=formatted_agents, input=input_data)

    print(mre)