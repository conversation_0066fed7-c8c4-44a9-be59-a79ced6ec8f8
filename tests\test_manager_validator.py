import unittest
import sys
import os

# 添加 src 到模块路径 (仅用于测试环境)
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../src")))

from core.agents.validator.manager_validator import ListJsonValidator

class TestListJsonValidator(unittest.TestCase):
    def setUp(self):
        self.available_agents = ["agent1", "agent2", "agent3"]
        self.validator = ListJsonValidator(available_agents=self.available_agents)

    def test_valid_list_of_json(self):
        valid_data = '[{"agent_name": "agent1", "description": "process"}, {"agent_name": "agent2", "description": "analyze"}]'
        result = self.validator.validate(valid_data)
        self.assertTrue(result["valid_format"])
        self.assertIn("details", result)

    def test_invalid_json_format(self):
        invalid_data = "{invalid_json: [tools_to_use]}]"
        result = self.validator.validate(invalid_data)
        self.assertFalse(result["valid_format"])
        self.assertEqual(result["error"], "Invalid JSON format")

    def test_not_a_list(self):
        non_list_data = '{"tools_to_use": "agent1", "task": "process"}'
        result = self.validator.validate(non_list_data)
        self.assertFalse(result["valid_format"])
        self.assertEqual(result["error"], "Output is not a list")

    def test_item_not_a_dict(self):
        invalid_item_data = '[1, 2, 3]'
        result = self.validator.validate(invalid_item_data)
        self.assertFalse(result["valid_format"])
        self.assertEqual(result["error"], "Item at index 0 is not a json")


if __name__ == "__main__":
    unittest.main()
