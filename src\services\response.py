from datetime import datetime
from typing import Any, Dict, Optional
from fastapi import HTTPException
from starlette import status


def wrap_response(
        data: Any = None,
        msg: str = "操作成功",
        code: int = 200,
        success: bool = True,
        extra: Optional[Dict] = None
) -> Dict[str, Any]:
    """
    包装前端响应数据

    参数:
    - data: 主要响应数据
    - msg: 提示信息
    - code: HTTP状态码
    - success: 操作是否成功
    - extra: 额外附加数据

    返回:
    - 标准化的响应字典
    """
    response = {
        "code": code,
        "success": success,
        "msg": msg,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }

    if extra:
        response.update({"extra": extra})

    return response


def wrap_error_response(
        error: Any = None,
        msg: str = "操作失败",
        code: int = 400,
        extra: Optional[Dict] = None
) -> Dict[str, Any]:
    """
    包装错误响应

    参数:
    - error: 错误详情
    - msg: 错误信息
    - code: HTTP状态码
    - extra: 额外附加数据
    """
    return wrap_response(
        data={"error": str(error)},
        msg=msg,
        code=code,
        success=False,
        extra=extra
    )


def http_exception_handler(exc: HTTPException):
    """HTTP异常处理器"""
    return wrap_error_response(
        error=exc.detail,
        msg="请求处理失败",
        code=exc.status_code
    )
