# Bibrain 架构设计文档

## 1. 系统概述

Bibrain 是一个多 Agent 通用平台，旨在提供一个统一的框架来管理和协调多个 AI Agent 的工作。该平台支持动态扩展、负载均衡、服务发现等企业级特性。

### 1.1 核心特性

- 多 Agent 协同：支持多个 Agent 之间的协作和通信
- 动态扩展：支持 Agent 的动态注册和发现
- 负载均衡：智能分配任务到合适的 Agent
- 高可用性：支持故障转移和自动恢复
- 监控告警：全面的监控和告警机制
- 安全机制：完善的认证和授权系统

### 1.2 技术栈

- 后端：Python 3.9+
- Web 框架：FastAPI
- 数据库：PostgreSQL + Redis
- 消息队列：Kafka + RabbitMQ
- 容器化：Docker + Kubernetes
- 监控：Prometheus + Grafana
- 日志：ELK Stack

## 2. 系统架构

### 2.1 整体架构

系统分为以下几个主要层次：
- 接口层 (Interface Layer)
- 网关层 (Gateway Layer)
- 核心层 (Core Layer)
- 基础设施层 (Infrastructure Layer)

### 2.2 各层详细设计

#### 2.2.1 接口层 (Interface Layer)

##### HTTP 接口
```python
# 示例：FastAPI 路由定义
from fastapi import APIRouter, Depends
from typing import List

router = APIRouter()

@router.post("/agents")
async def register_agent(agent: AgentCreate):
    """注册新的 Agent"""
    return await agent_service.register(agent)

@router.get("/agents/{agent_id}")
async def get_agent(agent_id: str):
    """获取 Agent 信息"""
    return await agent_service.get(agent_id)
```

##### WebSocket 接口
```python
# 示例：WebSocket 处理
from fastapi import WebSocket

class WebSocketManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)
```

##### 中间件
```python
# 示例：认证中间件
from fastapi import Request
from fastapi.middleware.base import BaseHTTPMiddleware

class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 验证 API Key
        api_key = request.headers.get("Authorization")
        if not self.validate_api_key(api_key):
            return JSONResponse(
                status_code=401,
                content={"message": "Invalid API Key"}
            )
        return await call_next(request)
```

#### 2.2.2 网关层 (Gateway Layer)

##### 路由管理
```python
# 示例：意图识别
class IntentDetector:
    def __init__(self):
        self.nlu_model = self.load_nlu_model()

    async def detect(self, text: str) -> Intent:
        """识别用户意图"""
        return await self.nlu_model.predict(text)

# 示例：负载均衡
class LoadBalancer:
    def __init__(self):
        self.strategy = RoundRobinStrategy()

    async def route(self, request: Request) -> Agent:
        """路由请求到合适的 Agent"""
        return await self.strategy.select_agent(request)
```

##### 服务注册与发现
```python
# 示例：服务注册
class ServiceRegistry:
    def __init__(self):
        self.services = {}

    async def register(self, service: Service):
        """注册服务"""
        self.services[service.id] = service
        await self.notify_registry_change()

# 示例：健康检查
class HealthChecker:
    def __init__(self):
        self.check_interval = 30

    async def check_health(self, service: Service) -> HealthStatus:
        """检查服务健康状态"""
        try:
            response = await self.check_endpoint(service.health_endpoint)
            return HealthStatus.HEALTHY if response.ok else HealthStatus.UNHEALTHY
        except Exception:
            return HealthStatus.UNHEALTHY
```

#### 2.2.3 核心层 (Core Layer)

##### 任务编排
```python
# 示例：DAG 任务调度
class TaskScheduler:
    def __init__(self):
        self.dag = DAG()

    async def schedule(self, task: Task):
        """调度任务"""
        self.dag.add_task(task)
        await self.execute_dag()

# 示例：结果聚合
class ResultAggregator:
    def __init__(self):
        self.results = {}

    async def aggregate(self, task_id: str, results: List[Result]):
        """聚合多个 Worker 的结果"""
        self.results[task_id] = await self.merge_results(results)
```

##### Worker 管理
```python
# 示例：Worker 抽象类
class BaseWorker:
    async def process(self, task: Task) -> Result:
        """处理任务"""
        raise NotImplementedError

# 示例：具体 Worker 实现
class WeatherWorker(BaseWorker):
    async def process(self, task: Task) -> Result:
        """处理天气查询任务"""
        weather_data = await self.fetch_weather(task.city)
        return Result(data=weather_data)
```

#### 2.2.4 基础设施层 (Infrastructure Layer)

##### LLM 集成
```python
# 示例：统一模型接口
class LLMClient:
    def __init__(self, config: LLMConfig):
        self.config = config
        self.clients = self.initialize_clients()

    async def generate(self, prompt: str, model: str) -> str:
        """生成文本"""
        client = self.clients[model]
        return await client.generate(prompt)

# 示例：Token 管理
class TokenManager:
    def __init__(self):
        self.usage = {}

    async def count_tokens(self, text: str) -> int:
        """计算 Token 数量"""
        return len(text.split())
```

##### 消息队列
```python
# 示例：Kafka 生产者
class KafkaProducer:
    def __init__(self, config: KafkaConfig):
        self.producer = self.create_producer(config)

    async def send(self, topic: str, message: dict):
        """发送消息"""
        await self.producer.send(topic, message)

# 示例：RabbitMQ 消费者
class RabbitMQConsumer:
    def __init__(self, config: RabbitMQConfig):
        self.consumer = self.create_consumer(config)

    async def consume(self, queue: str, callback: Callable):
        """消费消息"""
        await self.consumer.consume(queue, callback)
```

## 3. 数据流

### 3.1 请求处理流程

1. 客户端发送请求到接口层
2. 接口层进行认证和参数验证
3. 网关层进行意图识别和路由
4. 核心层调度任务到合适的 Worker
5. Worker 处理任务并返回结果
6. 结果通过网关层返回给客户端

### 3.2 数据存储

#### 3.2.1 数据库设计

```sql
-- Agent 表
CREATE TABLE agents (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    version VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- 任务表
CREATE TABLE tasks (
    id VARCHAR(36) PRIMARY KEY,
    agent_id VARCHAR(36) NOT NULL,
    status VARCHAR(20) NOT NULL,
    input JSONB NOT NULL,
    output JSONB,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id)
);
```

#### 3.2.2 缓存设计

```python
# Redis 缓存配置
CACHE_CONFIG = {
    "host": "redis",
    "port": 6379,
    "db": 0,
    "ttl": 3600
}

# 缓存键设计
class CacheKeys:
    AGENT_STATUS = "agent:status:{agent_id}"
    TASK_RESULT = "task:result:{task_id}"
```

## 4. 安全设计

### 4.1 认证机制

```python
# JWT 认证
class JWTAuth:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key

    def create_token(self, user_id: str) -> str:
        """创建 JWT Token"""
        return jwt.encode(
            {"user_id": user_id},
            self.secret_key,
            algorithm="HS256"
        )

    def verify_token(self, token: str) -> dict:
        """验证 JWT Token"""
        return jwt.decode(
            token,
            self.secret_key,
            algorithms=["HS256"]
        )
```

### 4.2 权限控制

```python
# RBAC 权限控制
class RBAC:
    def __init__(self):
        self.roles = {}

    def check_permission(self, user: User, resource: str, action: str) -> bool:
        """检查权限"""
        role = self.roles.get(user.role)
        if not role:
            return False
        return action in role.permissions.get(resource, [])
```

## 5. 监控设计

### 5.1 指标收集

```python
# Prometheus 指标
class Metrics:
    def __init__(self):
        self.request_count = Counter(
            "request_count",
            "Total number of requests",
            ["endpoint", "method"]
        )
        self.request_latency = Histogram(
            "request_latency",
            "Request latency in seconds",
            ["endpoint"]
        )
```

### 5.2 日志管理

```python
# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "json"
        },
        "file": {
            "class": "logging.FileHandler",
            "filename": "app.log",
            "formatter": "json"
        }
    },
    "formatters": {
        "json": {
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter"
        }
    }
}
```

## 6. 扩展性设计

### 6.1 插件系统

```python
# 插件管理器
class PluginManager:
    def __init__(self):
        self.plugins = {}

    def register_plugin(self, name: str, plugin: Plugin):
        """注册插件"""
        self.plugins[name] = plugin

    async def execute_plugin(self, name: str, *args, **kwargs):
        """执行插件"""
        plugin = self.plugins.get(name)
        if not plugin:
            raise PluginNotFoundError(name)
        return await plugin.execute(*args, **kwargs)
```

### 6.2 配置管理

```python
# 配置管理器
class ConfigManager:
    def __init__(self):
        self.config = {}
        self.watchers = []

    def load_config(self, path: str):
        """加载配置"""
        with open(path) as f:
            self.config = yaml.safe_load(f)

    def watch_config(self, callback: Callable):
        """监听配置变更"""
        self.watchers.append(callback)
```

## 7. 性能优化

### 7.1 缓存策略

```python
# 多级缓存
class MultiLevelCache:
    def __init__(self):
        self.l1_cache = LRUCache(1000)  # 内存缓存
        self.l2_cache = RedisCache()     # Redis 缓存

    async def get(self, key: str) -> Any:
        """获取缓存数据"""
        # 先查 L1 缓存
        value = self.l1_cache.get(key)
        if value:
            return value

        # 再查 L2 缓存
        value = await self.l2_cache.get(key)
        if value:
            self.l1_cache.set(key, value)
            return value

        return None
```

### 7.2 异步处理

```python
# 异步任务处理
class AsyncTaskProcessor:
    def __init__(self):
        self.queue = asyncio.Queue()
        self.workers = []

    async def start(self, num_workers: int):
        """启动工作进程"""
        for _ in range(num_workers):
            worker = asyncio.create_task(self.worker())
            self.workers.append(worker)

    async def worker(self):
        """工作进程"""
        while True:
            task = await self.queue.get()
            try:
                await self.process_task(task)
            finally:
                self.queue.task_done()
```

## 8. 部署架构

### 8.1 容器化

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 8.2 Kubernetes 配置

```yaml
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: src
spec:
  replicas: 3
  selector:
    matchLabels:
      app: src
  template:
    metadata:
      labels:
        app: src
    spec:
      containers:
      - name: src
        image: src:latest
        ports:
        - containerPort: 8000
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## 9. 开发规范

### 9.1 代码规范

```python
# 类型注解
from typing import List, Dict, Optional

class Agent:
    def __init__(self, name: str, version: str) -> None:
        self.name: str = name
        self.version: str = version
        self.status: Optional[str] = None

    async def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据"""
        pass
```

### 9.2 测试规范

```python
# 单元测试
import pytest

def test_agent_initialization():
    agent = Agent("test", "1.0.0")
    assert agent.name == "test"
    assert agent.version == "1.0.0"

# 集成测试
@pytest.mark.integration
async def test_agent_processing():
    agent = Agent("test", "1.0.0")
    result = await agent.process({"input": "test"})
    assert result["status"] == "success"
```

## 10. 运维支持

### 10.1 监控告警

```python
# 告警规则
ALERT_RULES = {
    "high_cpu": {
        "condition": "cpu_usage > 80",
        "duration": "5m",
        "severity": "critical"
    },
    "high_memory": {
        "condition": "memory_usage > 80",
        "duration": "5m",
        "severity": "warning"
    }
}
```

### 10.2 日志管理

```python
# 日志配置
LOGGING = {
    "version": 1,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "json"
        },
        "file": {
            "class": "logging.FileHandler",
            "filename": "app.log",
            "formatter": "json"
        }
    },
    "formatters": {
        "json": {
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter"
        }
    }
}
``` 