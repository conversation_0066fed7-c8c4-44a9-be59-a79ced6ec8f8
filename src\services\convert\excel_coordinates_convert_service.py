import json
from PIL import Image, ImageDraw
import os


def batch_calculate_coordinates(diff, original_pages, compared_pages, result):
    """
    批量计算 diff 数据里 origin 和 compare 坐标的 xywh。

    :param diff: `result['report_detail']['diff']` 列表
    :param original_pages: 保存 original 的截图路径列表
    :param compared_pages: 保存 compared 的截图路径列表
    :param result: 数据结构，包含 old_sheet 和 new_sheet
    :param process_and_draw_rectangles: 用于计算新 xywh 的函数
    :return: 更新的 diff 列表
    """

    print("===这段调用了==")
    for entry in diff:
        # 处理 originRegion
        if 'originRegion' in entry and entry['originRegion']:
            for region in entry['originRegion']:
                page_name = region['page']  # 获取页面名称
                # 匹配 original_image_path，根据 pageName 查找
                original_entry = next((item for item in original_pages if item['pageName'] == page_name), None)

                if original_entry:
                    old_image_path = original_entry['url']
                    print(old_image_path)

                    # 从 result 中找到对应的表单 sheet_info
                    sheet_info = next(
                        (sheet[page_name] for sheet in result['report_detail']['old_sheet'] if page_name in sheet),
                        None)
                    if sheet_info:
                        sheet_info_2 = [sheet_info['x'], sheet_info['y']]

                        # 调用处理函数重新计算 xywh
                        x, y, w, h = region['x'], region['y'], region['width'], region['height']

                        region['x'], region['y'], region['width'], region['height'] = process_and_draw_rectangles(
                            old_image_path, [x, y, w, h], sheet_info_2
                        )

        # 处理 compareRegion
        if 'compareRegion' in entry and entry['compareRegion']:
            for region in entry['compareRegion']:
                page_name = region['page']  # 获取页面名称

                # 匹配 compare_image_path，根据 pageName 查找
                compared_entry = next((item for item in compared_pages if item['pageName'] == page_name), None)
                if compared_entry:
                    new_image_path = compared_entry['url']
                    print(new_image_path)

                    # 从 result 中找到对应的表单 sheet_info
                    sheet_info = next(
                        (sheet[page_name] for sheet in result['report_detail']['new_sheet'] if page_name in sheet),
                        None)
                    if sheet_info:
                        sheet_info_2 = [sheet_info['x'], sheet_info['y']]

                        # 调用处理函数重新计算 xywh
                        x, y, w, h = region['x'], region['y'], region['width'], region['height']
                        region['x'], region['y'], region['width'], region['height'] = process_and_draw_rectangles(
                            new_image_path, [x, y, w, h], sheet_info_2
                        )

    return diff

def process_and_draw_rectangles(old_image_path, origin_position, sheet_info):
    """
    从 JSON 文件中提取位置信息，并在指定图片上绘制矩形框，分别保存标注完成后的图片。
    注意这里的处理以一个表单为单位

    Args:
        json_file_path (str): JSON 文件的路径。
        old_image_path (str): 文件截图的路径。
        origin_position : [x,y,w,h]
        sheet_info (List[x_start,y_start]): sheet_info中的左上角相对位置坐标，每个表单只有一对
    """

    # 定义函数，找到左上角和右下角第一个非空白像素
    def find_non_blank_bounds(image: Image.Image) -> tuple:
        """
        找到图像中非白色区域的边界（以左上角为 (0,0) 坐标系开始计算）
        :param image: 输入的 RGBA 模式图像
        :return: (left, top, right, bottom) 非白色区域的边界，从左上角 (0,0) 起计算
        """
        # 获取图像宽度、高度
        Image.MAX_IMAGE_PIXELS = None
        width, height = image.size
        pixels = image.load()  # 加载像素数据

        # 初始化边界值
        top, left = height, width  # 初始顶部和左侧定义为最大值
        bottom, right = 0, 0  # 初始底部和右侧定义为最小值

        # 遍历图像的每个像素点，寻找非白色区域边界
        for y in range(height):  # 从顶部到底部
            for x in range(width):  # 从左侧到右侧
                r, g, b, a = pixels[x, y]  # 获取像素的 RGBA 值
                # 判断像素是否为白色区域，白色定义为 RGB(255,255,255)
                if not (r == 255 and g == 255 and b == 255):  # 如果不是白色
                    top = min(top, y)  # 确定顶部坐标
                    left = min(left, x)  # 确定左侧坐标
                    bottom = max(bottom, y)  # 确定底部坐标
                    right = max(right, x)  # 确定右侧坐标

        # 检查是否找到边界（避免空图像或全白图像）
        if top == height or left == width:
            raise ValueError("The image does not contain any non-blank area.")

        # 返回边界，使用左上角为 (0,0) 的坐标系
        return left, top, right, bottom

    # 返回两组 x y w h
    # 这里的新旧文件地址是要求 截图的两个文件 算上下左右边距
    def draw_rectangles_on_images(old_image_path, position_data, x_start, y_start):

        #result_position = []
        Image.MAX_IMAGE_PIXELS = None  # 取消图片大小限制
        try:
            # 打开图片
            old_image = Image.open(old_image_path).convert("RGBA")  # 将图片转换为支持透明层的 RGBA 模式

            # 获取图片宽度和高度
            old_width, old_height = old_image.size

            # 获取两个图片的非空白边界
            old_left, old_top, old_right, old_bottom = find_non_blank_bounds(old_image)

            # 捕捉最大宽度和高度, 当前页面左上角第一个非空像素
            max_width = old_right - old_left
            max_height = old_bottom - old_top

            x_0, y_0, w_0, h_0 = position_data

            x = int((x_0 - x_start) / (1 - x_start) * max_width) + old_left
            w = int(w_0 / (1 - x_start) * max_width)
            y = int((y_0 - y_start) / (1 - y_start) * max_height) + old_top
            h = int(h_0 / (1 - y_start) * max_height)

            # 将绝对坐标转换为相对坐标
            relative_x = x / old_width
            relative_y = y / old_height
            relative_w = w / old_width
            relative_h = h / old_height

            print("==========excel新相对坐标========")
            print(relative_x,relative_y,relative_w,relative_h)
            return [relative_x,relative_y,relative_w,relative_h]
            #return [x,y,w,h]
        except Exception as e:
            print(f"Error occurred: {e}")

    x_start, y_start = sheet_info[0], sheet_info[1]

    positions = origin_position

    transfer_position = draw_rectangles_on_images(old_image_path, positions,
                                                  x_start=x_start, y_start=y_start)
    if not transfer_position:
        transfer_position = positions

    return transfer_position

if __name__ == "__main__":
    result_2 = {'total_diff': 10, 'new_items': 0, 'modified_items': 9, 'deleted_items': 1, 'report_path': 'D:\\data_zh\\data_zh\\excel\\before\\output',
    'report_detail':
    {'originPage': [], 'comparePage': [],
    'diff': [
                {'type': 'update', 'contentType': 'table', 'originText': 'I-ID-001', 'originKeyword': '', 'compareText': 'I-ID-0011', 'compareKeyword': '', 'aiJudgeType': '更新', 'aiJudge': "この変更は '接口编号' (インターフェース番号) に関するもので、インターフェース識別子が変更されたことを示しています。内容は新しい情報を反映しており、数値的な変更であるため、機能変更として判断されます。",
        'originRegion': [
                        {'page': '接口定义', 'x': 0.060267536079014264, 'y': 0.21142857142857144, 'width': 0.09427290597880787, 'height': 0.13142857142857142
                        }
                    ],
        'compareRegion': [
                        {'page': '接口定义', 'x': 0.060267536079014264, 'y': 0.21142857142857144, 'width': 0.09427290597880787, 'height': 0.13142857142857142
                        }
                    ]
                },
                {'type': 'update', 'contentType': 'table', 'originText': '车道线识别精度应达到95%以上，交通标志识别精度应达到90%以上', 'originKeyword': '', 'compareText': '车道线识别精度应达到80%以上，交通标志识别精度应达到90%以上', 'compareKeyword': '', 'aiJudgeType': '更新', 'aiJudge': 'この変更はドキュメントの内容に直接影響を与えるものであり、機能の結果に関わる精度要件の変更であると判断されます。そのため、これは機能変更に該当します。', 'originRegion': [
                        {'page': '非功能需求', 'x': 0.43242537771772505, 'y': 0.19375, 'width': 0.47445031322933323, 'height': 0.14375
                        }
                    ], 'compareRegion': [
                        {'page': '非功能需求', 'x': 0.43242537771772505, 'y': 0.19375, 'width': 0.47445031322933323, 'height': 0.14375
                        }
                    ]
                },
                {'type': 'update', 'contentType': 'table', 'originText': '智能驾驶功能的响应时间应小于100ms', 'originKeyword': '', 'compareText': '智能驾驶功能的响应时间应小于50ms', 'compareKeyword': '', 'aiJudgeType': '更新', 'aiJudge': '変更は『需求描述』というセルにおいて、機能の動作としての基準値を直接変更しており、応答時間の基準が変わることで機能に影響を与える部分と見なされます。したがって、これは機能変更です。', 'originRegion': [
                        {'page': '非功能需求', 'x': 0.43242537771772505, 'y': 0.121875, 'width': 0.47445031322933323, 'height': 0.071875
                        }
                    ], 'compareRegion': [
                        {'page': '非功能需求', 'x': 0.43242537771772505, 'y': 0.121875, 'width': 0.47445031322933323, 'height': 0.071875
                        }
                    ]
                },
                {'type': 'update', 'contentType': 'text', 'originText': '*自适应巡航控制（ACC）：根据前方车辆速度自动调整本车速度，保持安全距离\n*车道保持辅助（LKA）：通过摄像头识别车道线，并在车辆偏离车道时进行纠正\n*自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动', 'originKeyword': '车道保持辅助（LKA）：通过摄像头识别车道线，并在车辆偏离车道时进行纠正\n*自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动', 'compareText': '*自适应巡航控制（ACC）：根据前方车辆速度自动调整本车速度，保持安全距离\n*自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动\n*车道保持辅助（LKA）：通过摄像头识别车道线，并在车辆偏离车道时进行纠正', 'compareKeyword': '自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动\n*车道保持辅助（LKA）：通过摄像头识别车道线，并在车辆偏离车道时进行纠正', 'aiJudgeType': '更新', 'aiJudge': '変更内容は機能の説明の順序変更を示していますが、テキストの意味や機能の結果に影響を与えるものではなく、単なる順序の変更であり、機能的変更とは言えません。', 'originRegion': [
                        {'page': '功能需求', 'x': 0.017668141816284975, 'y': 0.8388059701492537, 'width': 0.07235524743811926, 'height': 0.16119402985074627
                        }
                    ], 'compareRegion': [
                        {'page': '功能需求', 'x': 0.017668141816284975, 'y': 0.8388059701492537, 'width': 0.07235524743811926, 'height': 0.16119402985074627
                        }
                    ]
                },
                {'type': 'delete', 'contentType': 'picture', 'originText': 'id: 2\nname:Picture 1\nwidth: 99\nheight: 94\nstyle.font_style.bold:False\nstyle.font_style.italic:False\nstyle.font_style.underline:False\nstyle.font_style.strikeout:False\ncoordinate.desc:D5\ncoordinate.left: 314\ncoordinate.top: 111\ndigest:ffe7e7c701c3dbc3', 'originKeyword': '', 'compareText': '', 'compareKeyword': '', 'aiJudgeType': '削除', 'aiJudge': '削除された内容は、画像のプロパティや座標、関連データに関する具体的な情報を含むため、機能変更に該当します。これにより、画像の存在やレイアウトに影響を与える可能性があります。', 'originRegion': [
                        {'page': '非功能需求', 'x': 314.0, 'y': 111.75, 'width': 99.75, 'height': 94.5
                        }
                    ], 'compareRegion': [
                        {'page': '非功能需求', 'x': 0, 'y': 0, 'width': 0, 'height': 0
                        }
                    ]
                },
                {'type': 'update', 'contentType': 'table', 'originText': '控制车辆油门/刹车', 'originKeyword': '', 'compareText': '控制车辆油门', 'compareKeyword': '', 'aiJudgeType': '更新', 'aiJudge': '変更内容は『接口描述』列に関連し、 車両の油門/刹車に対する制御から油門に限定している設計ロジック変更を示しています。したがって、これは機能変更に該当します。', 'originRegion': [
                        {'page': '接口定义', 'x': 0.32234981061514084, 'y': 0.7371428571428571, 'width': 0.21096034904348643, 'height': 0.13142857142857142
                        }
                    ], 'compareRegion': [
                        {'page': '接口定义', 'x': 0.32234981061514084, 'y': 0.7371428571428571, 'width': 0.21096034904348643, 'height': 0.13142857142857142
                        }
                    ]
                },
                {'type': 'update', 'contentType': 'table', 'originText': '获取车辆前方摄像头图像', 'originKeyword': '', 'compareText': '获取车辆前方摄像头图像', 'compareKeyword': '', 'aiJudgeType': '更新', 'aiJudge': '内容に影響する変更であるため、機能的な変更と言えます。インターフェース番号が変更された場合、車両システムとの連携や識別に影響を与える可能性があります。', 'originRegion': [
                        {'page': '接口定义', 'x': 0.32234981061514084, 'y': 0.21142857142857144, 'width': 0.21096034904348643, 'height': 0.13142857142857142
                        }
                    ], 'compareRegion': [
                        {'page': '接口定义', 'x': 0.32234981061514084, 'y': 0.21142857142857144, 'width': 0.21096034904348643, 'height': 0.13142857142857142
                        }
                    ]
                },
                {'type': 'update', 'contentType': 'table', 'originText': '油门/刹车控制信号', 'originKeyword': '', 'compareText': '油门控制信号', 'compareKeyword': '', 'aiJudgeType': '更新', 'aiJudge': '変更内容が文脈に基づいて機能記述の具体化に関連しており、コンテキストから見ると、情報が削除されて油门に関わる制御信号だけに焦点が当てられた。この変更は機能に直接影響する可能性があるため、機能変更と判断しました。', 'originRegion': [
                        {'page': '接口定义', 'x': 0.5333101596586273, 'y': 0.7371428571428571, 'width': 0.1598384235508463, 'height': 0.13142857142857142
                        }
                    ], 'compareRegion': [
                        {'page': '接口定义', 'x': 0.5333101596586273, 'y': 0.7371428571428571, 'width': 0.1598384235508463, 'height': 0.13142857142857142
                        }
                    ]
                },
                {'type': 'update', 'contentType': 'table', 'originText': '验证ACC功能能够根据前方车辆速度自动调整本车速度', 'originKeyword': '', 'compareText': '验证ACC功能能够根据前方车辆速度自动调整本车速度', 'compareKeyword': '', 'aiJudgeType': '更新', 'aiJudge': '変更内容は背景色の変更のみであり、テキスト内容には影響がないため、機能変更ではありません', 'originRegion': [
                        {'page': '测试用例', 'x': 0.2415023605946093, 'y': 0.17370892018779344, 'width': 0.21049888235117234, 'height': 0.215962441314554
                        }
                    ], 'compareRegion': [
                        {'page': '测试用例', 'x': 0.2415023605946093, 'y': 0.17370892018779344, 'width': 0.21049888235117234, 'height': 0.215962441314554
                        }
                    ]
                },
                {'type': 'update', 'contentType': 'table', 'originText': '油门/刹车控制接口', 'originKeyword': '', 'compareText': '油门控制接口', 'compareKeyword': '', 'aiJudgeType': '更新', 'aiJudge': '内容の変更は、機能的なコンテキスト（車両油门の制御に関する情報）に影響を与えています。「刹车」の部分が削除され、説明や関連情報も編集されているため、この変更は機能的な変更と判断されます。', 'originRegion': [
                        {'page': '接口定义', 'x': 0.15454044205782214, 'y': 0.7371428571428571, 'width': 0.1678093685573187, 'height': 0.13142857142857142
                        }
                    ], 'compareRegion': [
                        {'page': '接口定义', 'x': 0.15454044205782214, 'y': 0.7371428571428571, 'width': 0.1678093685573187, 'height': 0.13142857142857142
                        }
                    ]
                }
            ],
            'new_sheet': [
                {'功能概述': {'x': 0.04046645200591769, 'y': 0.1872146118721461, 'width': 0.18910451657819133, 'height': 0.1917808219178082
                    }
                },
                {'功能需求': {'x': 0.017668141816284975, 'y': 0.05373134328358209, 'width': 0.07235524743811926, 'height': 0.05970149253731343
                    }
                },
                {'非功能需求': {'x': 0.0772018179584818, 'y': 0.059375, 'width': 0.18632539000122847, 'height': 0.0625
                    }
                },
                {'接口定义': {'x': 0.060267536079014264, 'y': 0.10285714285714286, 'width': 0.09427290597880787, 'height': 0.10857142857142857
                    }
                },
                {'测试用例': {'x': 0.05039944668865212, 'y': 0.08450704225352113, 'width': 0.08886059962110204, 'height': 0.0892018779342723
                    }
                },
                {'附录': {'x': 0.08837642592234543, 'y': 0.08144796380090498, 'width': 0.1733956725783491, 'height': 0.08597285067873303
                    }
                },
                {'History': {'x': 0.04578870364683992, 'y': 0.3050847457627119, 'width': 0.09081877788414643, 'height': 0.3050847457627119
                    }
                }
            ],
            'old_sheet': [
                {'功能概述': {'x': 0.04046645200591769, 'y': 0.1872146118721461, 'width': 0.18910451657819133, 'height': 0.1917808219178082
                    }
                },
                {'功能需求': {'x': 0.017668141816284975, 'y': 0.05373134328358209, 'width': 0.07235524743811926, 'height': 0.05970149253731343
                    }
                },
                {'非功能需求': {'x': 0.0772018179584818, 'y': 0.059375, 'width': 0.18632539000122847, 'height': 0.0625
                    }
                },
                {'接口定义': {'x': 0.060267536079014264, 'y': 0.10285714285714286, 'width': 0.09427290597880787, 'height': 0.10857142857142857
                    }
                },
                {'测试用例': {'x': 0.05039944668865212, 'y': 0.08450704225352113, 'width': 0.08886059962110204, 'height': 0.0892018779342723
                    }
                },
                {'附录': {'x': 0.08837642592234543, 'y': 0.08144796380090498, 'width': 0.1733956725783491, 'height': 0.08597285067873303
                    }
                },
                {'History': {'x': 0.04578870364683992, 'y': 0.3050847457627119, 'width': 0.09081877788414643, 'height': 0.3050847457627119
                    }
                }
            ]
        }
    }

    #result = json.loads(result_2)
    result = result_2
    print(result)
    original_pages = ["D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_b\\Excel_b_History.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_b\\Excel_b_测试用例.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_b\\Excel_b_非功能需求.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_b\\Excel_b_附录.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_b\\Excel_b_功能概述.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_b\\Excel_b_功能需求.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_b\\Excel_b_接口定义.png",
    ]
    compared_pages = ["D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_a\\Excel_a_History.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_a\\Excel_a_测试用例.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_a\\Excel_a_非功能需求.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_a\\Excel_a_附录.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_a\\Excel_a_功能概述.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_a\\Excel_a_功能需求.png",
    "D:\\bibrain\\bibrain\\src\\converted_files\\compare_Excel_b_vs_Excel_a_20250616_173855\\Excel_a\\Excel_a_接口定义.png",
    ]

    updated_diff = batch_calculate_coordinates(
        result['report_detail']['diff'],
        original_pages,  # original 截图路径列表
        compared_pages,  # compared 截图路径列表
        result
    )
    result['report_detail']['diff'] = updated_diff
    print(result)