"""通用常量定义"""
from enum import Enum
from typing import Dict, List, Any

class Environment:
    """环境相关常量"""
    DEV = "development"
    TEST = "test"
    PROD = "production"
    LOCAL = "local"
    STAGING = "staging"

    @classmethod
    def all(cls) -> List[str]:
        return [cls.DEV, cls.TEST, cls.PROD, cls.LOCAL, cls.STAGING]

class ConfigKeys:
    """配置键常量"""
    LLM_CONFIG = "llm"
    GLOBAL_CONFIG = "global"
    AGENT_CONFIG = "agent"
    DATABASE_CONFIG = "database"
    LOGGING_CONFIG = "logging"

    @classmethod
    def all(cls) -> List[str]:
        return [cls.LLM_CONFIG, cls.GLOBAL_CONFIG, cls.AGENT_CONFIG, 
                cls.DATABASE_CONFIG, cls.LOGGING_CONFIG]

class ErrorMessages:
    """通用错误消息模板"""
    CONFIG_NOT_FOUND = "配置未找到: {key}"
    INVALID_CONFIG = "无效的配置: {error}"
    UNEXPECTED_ERROR = "发生意外错误: {error}"
    RESOURCE_NOT_FOUND = "资源未找到: {resource}"
    VALIDATION_FAILED = "验证失败: {reason}"

    @staticmethod
    def format_message(template: str, **kwargs) -> str:
        return template.format(**kwargs)