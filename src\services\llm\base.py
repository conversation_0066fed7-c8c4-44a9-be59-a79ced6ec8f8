from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from pydantic import BaseModel
from loguru import logger
class LLMConfig(BaseModel):
    """Base configuration for LLM services."""
    model: str
    temperature: float = 0.7
    max_tokens: int = 2000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0

class LLMResponse(BaseModel):
    """Response from LLM service."""
    text: str
    usage: Dict[str, int]
    model: str
    metadata: Optional[Dict[str, Any]] = None

class BaseLLMService(ABC):
    """Base class for LLM services."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.logger = logger.bind(service="llm")
        self.initialized = False
    
    async def initialize(self) -> None:
        """Initialize the LLM service."""
        if not self.initialized:
            self.logger.info(f"Initializing LLM service with model: {self.config.model}")
            await self._setup()
            self.initialized = True
    
    async def cleanup(self) -> None:
        """Cleanup LLM service resources."""
        if self.initialized:
            self.logger.info("Cleaning up LLM service")
            await self._teardown()
            self.initialized = False
    
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate text using the LLM."""
        pass
    
    @abstractmethod
    async def _setup(self) -> None:
        """Setup LLM-specific resources."""
        pass
    
    @abstractmethod
    async def _teardown(self) -> None:
        """Cleanup LLM-specific resources."""
        pass 