import os
import tempfile
import win32com.client
import pythoncom
import fitz  # PyMuPDF
from typing import Optional, List, Tuple
from contextlib import contextmanager
import logging
import shutil

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BaseConverterService:
    """文件转换基础服务"""

    def __init__(self, temp_dir: Optional[str] = None):
        """初始化转换服务"""
        self.temp_dir = temp_dir if temp_dir else tempfile.mkdtemp(prefix="file_converter_")
        os.makedirs(self.temp_dir, exist_ok=True)
        logger.info(f"初始化临时目录: {self.temp_dir}")

    @contextmanager
    def _office_app(self, app_name: str):
        """Office应用上下文管理器"""
        pythoncom.CoInitialize()
        app = None
        try:
            app = win32com.client.DispatchEx(app_name)
            app.Visible = False
            app.DisplayAlerts = False
            yield app
        except Exception as e:
            raise RuntimeError(f"启动 {app_name} 失败: {str(e)}") from e
        finally:
            if app:
                try:
                    app.Quit()
                except:
                    pass
            pythoncom.CoUninitialize()

    def _pdf_to_images(self, pdf_path: str, output_prefix: str, format: str = "png") -> List[str]:
        """
        将PDF转换为多张图片（修复文档关闭问题）
        :param pdf_path: PDF文件路径
        :param output_prefix: 输出文件前缀
        :param format: 图片格式(png/jpg)
        :return: 生成的图片路径列表
        """
        output_files = []
        doc = None  # 显式声明文档变量

        try:
            # 打开PDF文档
            doc = fitz.open(pdf_path)
            if doc.is_closed:
                raise RuntimeError("PDF文档已关闭，无法读取")

            # 处理每一页
            for i, page in enumerate(doc):
                output_path = f"{output_prefix}_{i + 1}.{format.lower()}"

                # 高质量输出设置
                pix = page.get_pixmap(
                    dpi=300,
                    colorspace="rgb",
                    alpha=False,
                    matrix=fitz.Matrix(300 / 72, 300 / 72)
                )

                # 保存图片
                pix.save(output_path)
                output_files.append(output_path)
                logger.info(f"已生成第 {i + 1} 页: {output_path}")

            return output_files

        except Exception as e:
            # 清理已生成的文件
            for f in output_files:
                try:
                    os.remove(f)
                except:
                    pass
            raise RuntimeError(f"PDF转图片失败: {str(e)}") from e

        finally:
            # 确保文档正确关闭
            if doc and not doc.is_closed:
                doc.close()

    def _convert_to_pdf(self, source_path: str, app_name: str, save_method: callable) -> Tuple[str, str]:
        """
        通用文件转PDF方法
        :return: (pdf_path, temp_dir) PDF路径和临时目录
        """
        source_path = os.path.abspath(source_path)
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文件不存在: {source_path}")

        output_dir = tempfile.mkdtemp(dir=self.temp_dir)
        pdf_path = os.path.join(output_dir, "temp.pdf")

        try:
            with self._office_app(app_name) as app:
                save_method(app, source_path, pdf_path)

            if not os.path.exists(pdf_path):
                raise RuntimeError("PDF文件未生成")

            return pdf_path, output_dir
        except Exception as e:
            shutil.rmtree(output_dir, ignore_errors=True)
            raise RuntimeError(f"转换为PDF失败: {str(e)}") from e

    def cleanup(self):
        """清理临时资源"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception as e:
            logger.error(f"清理临时目录失败: {e}")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
