import asyncio
from dataclasses import dataclass
from typing import Dict, Optional, Set
import logging
from ..config.agent_config import agent_config_loader
from ..registry.agent_registry import agent_registry


@dataclass
class AgentUpdateCheckResult:
    needs_update: bool
    reason: Optional[str] = None


class AgentDiscovery:
    """Agent自动发现和注册服务，支持周期性检查和新Agent注册"""

    def __init__(self, check_interval: int = 30):
        """
        Args:
            check_interval: 检查间隔(秒)，默认30秒
        """
        self.check_interval = check_interval
        self._running = False
        self._discovery_task: Optional[asyncio.Task] = None
        self._registered_agents: Set[str] = set()  # 使用集合提高查找效率

    async def start(self) -> None:
        """启动自动发现服务"""
        if self._running:
            logging.warning("Agent discovery service is already running")
            return

        self._running = True
        logging.info("Starting agent discovery service...")

        try:
            # 立即执行一次发现
            await self._discover_agents()

            # 启动后台任务
            self._discovery_task = asyncio.create_task(self._run_periodically())
        except Exception as e:
            logging.error(f"Failed to start discovery service: {str(e)}")
            self._running = False
            raise

    async def stop(self) -> None:
        """停止自动发现服务"""
        if not self._running:
            return

        self._running = False
        logging.info("Stopping agent discovery service...")

        if self._discovery_task:
            self._discovery_task.cancel()
            try:
                await self._discovery_task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logging.error(f"Error while stopping discovery task: {str(e)}")

    async def _run_periodically(self) -> None:
        """周期性执行Agent发现"""
        while self._running:
            try:
                await asyncio.sleep(self.check_interval)
                await self._discover_agents()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Periodic discovery failed: {str(e)}")
                await asyncio.sleep(min(60, self.check_interval * 2))  # 退避策略

    async def _discover_agents(self) -> None:
        """发现和注册Agent"""
        try:
            # 原子性加载配置
            if not agent_config_loader.load_configs():
                logging.error("Failed to load agent configs")
                return

            current_configs = agent_config_loader.get_all_agent_configs()
            if not current_configs:
                logging.warning("No agent configurations found")
                return

            # 处理新增/更新的Agent
            for agent_id, config in current_configs.items():
                if agent_id in self._registered_agents:
                    await self._handle_existing_agent(agent_id, config)
                else:
                    await self._handle_new_agent(agent_id, config)

            # 检查已移除的Agent
            self._check_removed_agents(current_configs.keys())

        except Exception as e:
            logging.error(f"Discovery process failed: {str(e)}")
            raise

    async def _handle_existing_agent(self, agent_id: str, config: dict) -> None:
        """处理已注册Agent的更新检查"""
        try:
            update_result = self._check_agent_update(agent_id)
            if update_result.needs_update:
                logging.info(f"Updating agent {agent_id}: {update_result.reason}")
                await self._update_agent(agent_id)
        except Exception as e:
            logging.error(f"Failed to process existing agent {agent_id}: {str(e)}")

    async def _handle_new_agent(self, agent_id: str, config: dict) -> None:
        """处理新Agent注册"""
        try:
            if not agent_config_loader.validate_dependencies(agent_id):
                logging.warning(f"Skipping agent {agent_id}: dependency validation failed")
                return

            await self._register_agent(agent_id)
            logging.info(f"Successfully registered new agent: {agent_id}")
        except Exception as e:
            logging.error(f"Failed to register new agent {agent_id}: {str(e)}")

    def _check_agent_update(self, agent_id: str) -> AgentUpdateCheckResult:
        """检查Agent是否需要更新"""
        current_agent = agent_registry.get_agent(agent_id)
        new_agent = agent_config_loader.get_agent_info(agent_id)

        if not current_agent or not new_agent:
            return AgentUpdateCheckResult(True, "agent not found in registry/config")

        if current_agent.metadata.version != new_agent.metadata.version:
            return AgentUpdateCheckResult(True,
                                          f"version changed ({current_agent.metadata.version} -> {new_agent.metadata.version})")

        if current_agent.endpoints != new_agent.endpoints:
            return AgentUpdateCheckResult(True, "endpoints changed")

        if current_agent.rate_limit != new_agent.rate_limit:
            return AgentUpdateCheckResult(True, "rate limit changed")

        return AgentUpdateCheckResult(False)

    async def _update_agent(self, agent_id: str) -> None:
        """更新Agent注册信息"""
        try:
            agent_registry.unregister(agent_id)
            agent_info = agent_config_loader.get_agent_info(agent_id)
            if agent_info:
                agent_registry.register(agent_info)
            else:
                self._registered_agents.discard(agent_id)
                logging.warning(f"Agent {agent_id} info missing after unregister")
        except Exception as e:
            logging.error(f"Failed to update agent {agent_id}: {str(e)}")
            raise

    async def _register_agent(self, agent_id: str) -> None:
        """注册新Agent"""
        agent_info = agent_config_loader.get_agent_info(agent_id)
        if not agent_info:
            raise ValueError(f"No agent info found for {agent_id}")

        agent_registry.register(agent_info)
        self._registered_agents.add(agent_id)

    def _check_removed_agents(self, current_agent_ids: set) -> None:
        """检查并处理被移除的Agent"""
        removed_agents = self._registered_agents - set(current_agent_ids)
        for agent_id in removed_agents:
            try:
                agent_registry.unregister(agent_id)
                self._registered_agents.discard(agent_id)
                logging.info(f"Unregistered removed agent: {agent_id}")
            except Exception as e:
                logging.error(f"Failed to unregister removed agent {agent_id}: {str(e)}")


# 单例模式管理
_global_agent_discovery: Optional[AgentDiscovery] = None


def get_agent_discovery(check_interval: int = 30) -> AgentDiscovery:
    """获取全局Agent发现服务实例(单例模式)"""
    global _global_agent_discovery
    if _global_agent_discovery is None:
        _global_agent_discovery = AgentDiscovery(check_interval)
    return _global_agent_discovery
