from typing import Any, Dict, List, Optional
from ..base import BaseAgent, AgentContext, AgentConfig
from loguru import logger

class ValidatorAgent(BaseAgent):
    """Base class for validator agents that validate and verify results."""
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.validation_rules: List[Dict[str, Any]] = []
        self.threshold = config.parameters.get("validation_threshold", 0.8)
    
    async def _setup(self) -> None:
        """Setup validator-specific resources."""
        self.logger.info(f"Setting up validator agent: {self.config.name}")
        await self._initialize_rules()
    
    async def _teardown(self) -> None:
        """Cleanup validator-specific resources."""
        self.logger.info(f"Tearing down validator agent: {self.config.name}")
        self.validation_rules.clear()
    
    async def execute(self, context: AgentContext) -> Dict[str, Any]:
        """Execute validation on the input data."""
        self.logger.info(f"Starting validation for: {self.config.name}")
        try:
            validation_results = []
            for rule in self.validation_rules:
                result = await self._validate_rule(rule, context)
                validation_results.append(result)
            
            is_valid = all(result["passed"] for result in validation_results)
            confidence = sum(result["confidence"] for result in validation_results) / len(validation_results)
            
            return {
                "status": "success",
                "is_valid": is_valid,
                "confidence": confidence,
                "validation_results": validation_results,
                "validator": self.config.name
            }
        except Exception as e:
            self.logger.error(f"Error in validation: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "validator": self.config.name
            }
    
    async def _initialize_rules(self) -> None:
        """Initialize validation rules. To be implemented by specific validators."""
        raise NotImplementedError("Validator agents must implement _initialize_rules")
    
    async def _validate_rule(self, rule: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Validate a single rule. To be implemented by specific validators."""
        raise NotImplementedError("Validator agents must implement _validate_rule")
    
    def add_rule(self, rule: Dict[str, Any]) -> None:
        """Add a validation rule."""
        self.validation_rules.append(rule)
        self.logger.debug(f"Added validation rule: {rule.get('name', 'unnamed')}")
    
    def remove_rule(self, rule_name: str) -> None:
        """Remove a validation rule by name."""
        self.validation_rules = [r for r in self.validation_rules if r.get("name") != rule_name]
        self.logger.debug(f"Removed validation rule: {rule_name}")

