import os
import uuid
import re
import logging
from datetime import datetime
from typing import Any, List, Tuple, Optional

from loguru import logger
from sqlalchemy.orm import Session
from fastapi import HTTPException
from src.models.task.models import FileComparisonTask
from src.services.files_upload.file_comparison_service import check_file
from .filename_similarity import get_top_similar_pairs, clean_file_names

# 配置允许的文件类型
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'}

# 配置最大文件大小（默认10MB）
MAX_CONTENT_LENGTH = 10 * 1024 * 1024

# 配置最小文件大小（1KB）
MIN_CONTENT_LENGTH = 1 * 1024

def safe_filename(filename: str) -> str:
    """
    清理文件名，确保只包含允许的字符。
    :param filename: 原始文件名
    :return: 安全的文件名
    """
    return re.sub(r'[^\u4e00-\u9fa5\w\.-]', '', filename)

def get_files_from_directory(directory: str) -> List[str]:
    """
    读取指定目录中的所有文件名。
    :param directory: 文件夹路径
    :return: 文件名列表
    """
    try:
        file_names = []
        for _, _, filenames in os.walk(directory):
            file_names.extend(filenames)
        return file_names
    except Exception as e:
        logging.error(f"读取目录 {directory} 时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"无法读取目录 {directory}")

def validate_and_create_task(
    db: Session,
    user_id: str,
    session_id: str,
    task_type: str,
    folder_id: uuid.UUID,
    file_before_path: str,
    file_after_path: str
) -> Tuple[bool, Optional[str]]:
    """
    校验文件并创建任务。
    :param db: 数据库会话
    :param user_id: 用户ID
    :param session_id: 会话ID
    :param task_type: 任务类型
    :param folder_id: 文件夹ID
    :param file_before_path: 差分前文件路径
    :param file_after_path: 差分后文件路径
    :return: 是否成功创建任务
    """
    check_result1 = check_file(file_before_path)
    check_result2 = check_file(file_after_path)

    if check_result1.is_valid and check_result2.is_valid:
        task_id = uuid.uuid4().hex
        extra_info = {
            "file_before": file_before_path,
            "file_after": file_after_path,
        }
        task = FileComparisonTask(
            user_id=user_id,
            session_id=session_id,
            task_type=task_type,
            create_time=datetime.utcnow(),
            extra_info=extra_info,
            task_id=task_id,
            status="processing",
            relative_id=folder_id.hex
        )
        print("=======这里是task入库的相关信息=====")
        print(task_id)
        db.add(task)
        return True, task_id
    else:
        logging.warning(f"⚠️ 文件 {file_before_path} 和文件 {file_after_path} 未通过校验，已跳过")
        return False, None

class DirectoryComparisonService:
    def __init__(self, db: Session):
        self.db = db

    async def upload_directories(
        self,
        user_id: str,
        session_id: str,
        task_type: str,
        dir_before: str,
        dir_after: str
    ) -> Tuple[List[List[str]], uuid.UUID]:
        """
        上传文件夹并进行文件差分任务。
        :param user_id: 用户ID
        :param session_id: 会话ID
        :param task_type: 任务类型
        :param dir_before: 差分前文件夹路径
        :param dir_after: 差分后文件夹路径
        :return: 文件配对信息和文件夹ID
        """
        folder_id = uuid.uuid4()
        try:
            # 获取文件夹中的文件名
            file_before_names = get_files_from_directory(dir_before)
            file_after_names = get_files_from_directory(dir_after)
            
            # 添加调试日志
            logger.info(f"原始文件夹A文件列表: {file_before_names}")
            logger.info(f"原始文件夹B文件列表: {file_after_names}")
            
            # 清理文件名
            file_before_names_cleaned = clean_file_names(file_before_names)
            file_after_names_cleaned = clean_file_names(file_after_names)
            
            # 添加调试日志
            logger.info(f"清理后的文件夹A文件列表: {file_before_names_cleaned}")
            logger.info(f"清理后的文件夹B文件列表: {file_after_names_cleaned}")
            
            # 获取文件名配对
            paired_names = get_top_similar_pairs(file_before_names_cleaned, file_after_names_cleaned)
            
            # 添加调试日志
            logger.info(f"获取到的文件配对数: {len(paired_names)}")
            logger.info(f"具体的文件配对: {paired_names}")
            
            paired_nums = len(paired_names)
            failed_nums = 0
            paired_file_info = []

            # 遍历文件配对并处理任务
            async with self.db.begin():  # 使用事务上下文管理
                for pair in paired_names:
                    file_before_path = os.path.join(dir_before, pair[0])
                    file_after_path = os.path.join(dir_after, pair[1])
                    validate_flag, task_id_pair = validate_and_create_task(
                        self.db, user_id, session_id, task_type, folder_id, file_before_path, file_after_path
                    )
                    if validate_flag:
                        paired_file_info.append([file_before_path, file_after_path, task_id_pair])
                    else:
                        failed_nums += 1

            # 日志记录
            checked_nums = paired_nums - failed_nums
            logging.info(f"总计 {paired_nums} 组文件配对")
            logging.info(f"其中 {checked_nums} 组文件校验通过")
            logging.warning(f"{failed_nums} 组文件校验失败")

            return paired_file_info, folder_id

        except Exception as e:
            logging.error(f"执行文件夹上传任务时发生错误: {e}")
            raise HTTPException(status_code=500, detail="文件夹上传任务失败")
