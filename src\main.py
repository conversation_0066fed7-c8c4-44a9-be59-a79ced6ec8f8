import sys
import uvicorn
from fastapi import FastAP<PERSON>
from loguru import logger
from src.interface.http.main import create_app
from src.common.config_manager import config_manager
from src.gateway.discovery.agent_discovery import AgentDiscovery
from src.services.database.database import init_db


def configure_logging() -> None:
    """配置日志格式和级别"""
    logger.remove()  # 移除默认的日志处理器
    logger.add(
        "logs/app_{time:YYYY-MM-DD}.log",
        rotation="1 day",
        retention="7 days",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    )
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | <cyan>{message}</cyan>",
    )


async def initialize_system() -> None:
    """初始化系统组件（数据库、服务发现等）"""
    try:
        logger.info("Initializing database...")
        await init_db()
        logger.success("Database initialized successfully")

        logger.info("Starting AgentDiscovery service...")
        await AgentDiscovery().start()
        logger.success("AgentDiscovery service started successfully")
    except Exception as e:
        logger.critical(f"System initialization failed: {str(e)}")
        raise  # 重新抛出异常，确保应用不会静默失败


def create_and_configure_app() -> FastAPI:
    """创建并配置FastAPI应用"""
    app = create_app()

    @app.on_event("startup")
    async def startup_event():
        try:
            await initialize_system()
            logger.info("Application startup completed")
        except Exception as e:
            logger.critical(f"Application startup failed: {str(e)}")
            # 这里可以选择是否退出应用
            # sys.exit(1)

    return app


def get_server_config() -> tuple[str, int]:
    """获取服务器配置"""
    host: str = config_manager.get("server.host", "0.0.0.0")
    port: int = config_manager.get("server.port", 15743)
    return host, port

# test
def run_server() -> None:
    """启动Uvicorn服务器"""
    try:
        configure_logging()
        app = create_and_configure_app()
        host, port = get_server_config()

        logger.info(f"Starting server on {host}:{port}")
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=config_manager.get("server.reload", False),
            log_level="info",
        )
    except Exception as e:
        logger.critical(f"Server startup failed: {str(e)}")
        sys.exit(1)

# from req_diff.api import DiffTool
if __name__ == "__main__":
    run_server()



