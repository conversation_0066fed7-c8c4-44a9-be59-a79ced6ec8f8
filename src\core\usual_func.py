import sys
import os
import yaml

# 添加项目根目录到 sys.path
# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../")))

def list_agent():
    yaml_file = os.path.normpath(os.path.join(os.path.dirname(__file__), "../../config/agents/llm_agent.yaml"))
    print(f"Loading YAML file from path: {yaml_file}")  # 打印路径以供调试

    with open(yaml_file, "r", encoding="utf-8") as file:
        config = yaml.safe_load(file)

    # Collect all LLM agent configurations and their names
    llm_configs = {}
    agents = []
    agent_description = []
    for key, value in config.items():
        # Check if the key or parameters contain "llm"
        if "llm" in key.lower() or ("parameters" in value and "llm_provider" in value["parameters"]):
            llm_configs[key] = value
            agents.append({
                "agent_name": value["name"],            # 提取 agent 名称
                "description": value["description"]     # 提取 agent 描述
            })
    return agents
if __name__ == "__main__":
    agent_information = list_agent()
    print(agent_information)