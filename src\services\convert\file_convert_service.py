import os
import uuid
import logging
from concurrent.futures import as_completed
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor

from src.services.convert.excel_convert_service import ExcelConvertService
from src.services.convert.word_convert_service import WordConvertService


logger = logging.getLogger(__name__)


class FileConversionService:
    """通用批量文件转换服务"""

    def __init__(self, output_root: str = None, max_workers: int = 4):
        """
        :param output_root: 输出根目录
        :param max_workers: 最大并发数
        """
        self.output_root = output_root if output_root else os.path.join(os.getcwd(), "converted_files")
        self.max_workers = max_workers
        os.makedirs(self.output_root, exist_ok=True)

    def _get_converter(self, file_path: str):
        """根据文件扩展名获取对应的转换器"""
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ('.doc', '.docx'):
            return WordConvertService()
        elif ext in ('.xls', '.xlsx'):
            return ExcelConvertService()
        else:
            raise ValueError(f"Unsupported file type: {ext}")

    def _generate_job_id(self) -> str:
        """生成唯一的任务ID"""
        return str(uuid.uuid4())

    def _convert_single_file(self, file_path: str, output_dir: str) -> List[str]:
        """转换单个文件"""
        try:
            with self._get_converter(file_path) as converter:
                # 使用文件名作为前缀(不含扩展名)
                file_prefix = os.path.splitext(os.path.basename(file_path))[0]
                output_prefix = os.path.join(output_dir, file_prefix)

                return converter.convert_to_images(
                    file_path,
                    output_prefix=output_prefix,
                    format="png"
                )
        except Exception as e:
            logger.error(f"Failed to convert {file_path}: {str(e)}")
            return []

    def batch_convert(self, file_paths: List[str], job_name: str = None) -> Dict[str, str]:
        """
        批量转换文件，支持多线程并发处理

        Args:
            file_paths: 待转换的文件路径列表
            job_name: 可选的任务名称（用于创建输出目录）

        Returns:
            {
                "job_id": "唯一任务ID",
                "output_dir": "主输出目录路径",
                "converted_files": {
                    "原文件名1": {
                        "output_dir": "该文件的输出目录",
                        "files": ["生成图片1.jpg", ...],
                        "error": "错误信息（如果有）"
                    },
                    ...
                }
            }

        Raises:
            FileNotFoundError: 如果任何输入文件不存在
            OSError: 如果无法创建输出目录
        """
        # 1. 前置检查
        self._validate_input_files(file_paths)

        # 2. 初始化任务目录
        job_id, main_job_dir = self._prepare_job_directory(job_name)

        # 3. 并发处理文件
        results = self._process_files_concurrently(file_paths, main_job_dir)

        return {
            "job_id": job_id,
            "output_dir": main_job_dir,
            "converted_files": results
        }

    def _validate_input_files(self, file_paths: List[str]) -> None:
        """验证所有输入文件是否存在"""
        missing_files = [p for p in file_paths if not os.path.exists(p)]
        if missing_files:
            raise FileNotFoundError(f"Missing files: {missing_files}")

    def _prepare_job_directory(self, job_name: str = None) -> tuple:
        """准备任务输出目录结构"""
        job_id = self._generate_job_id()
        main_dir = os.path.join(
            self.output_root,
            job_name if job_name else job_id
        )
        os.makedirs(main_dir, exist_ok=True)
        return job_id, main_dir

    def _process_files_concurrently(self, file_paths: List[str], main_dir: str) -> Dict:
        """使用线程池并发处理所有文件"""
        results = {}

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 使用字典推导式提交所有任务
            future_to_key = {
                executor.submit(
                    self._convert_single_file,
                    src_path,
                    self._get_file_output_dir(src_path, main_dir)
                ): os.path.splitext(os.path.basename(src_path))[0]
                for src_path in file_paths
            }

            # 处理任务结果
            for future in as_completed(future_to_key):
                file_basename = future_to_key[future]
                src_path = next(p for p in file_paths if file_basename in p)
                output_dir = self._get_file_output_dir(src_path, main_dir)

                try:
                    converted_files = future.result()
                    results[file_basename] = {
                        "output_dir": output_dir,
                        "files": [os.path.basename(f) for f in converted_files]
                    }
                except Exception as e:
                    logger.exception(f"Conversion failed for {src_path}")
                    results[file_basename] = {
                        "output_dir": output_dir,
                        "files": [],
                        "error": str(e)
                    }

        return results

    def _get_file_output_dir(self, file_path: str, main_dir: str) -> str:
        """获取单个文件的专属输出目录"""
        file_basename = os.path.splitext(os.path.basename(file_path))[0]
        output_dir = os.path.join(main_dir, file_basename)
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    def cleanup_job(self, job_id: str):
        """清理任务输出目录"""
        job_dir = os.path.join(self.output_root, job_id)
        if os.path.exists(job_dir):
            try:
                import shutil
                shutil.rmtree(job_dir)
                logger.info(f"Cleaned up job directory: {job_dir}")
            except Exception as e:
                logger.error(f"Failed to clean up {job_dir}: {str(e)}")
                raise
