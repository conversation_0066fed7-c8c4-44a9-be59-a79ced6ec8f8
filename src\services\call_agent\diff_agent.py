import asyncio
import uuid
import json
import os
import logging
from typing import List
from fastapi import FastAPI
# from flask import Flask, jsonify
import subprocess
from pydantic import BaseModel
from collections import Counter
from src.core.agents.orchestrator.diff_orchestrator import DiffOrchestrator, DiffWorker
from src.core.agents.base import AgentConfig, AgentContext
from src.core.agents.orchestrator.diff_orchestrator import prompt_ai_judge
from src.services.llm.general_llm_connect import model_configs
from req_diff.api import DiffTool

# ================= 日志配置 =================
logging.basicConfig(
    level=logging.DEBUG,  # 设置日志级别
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # 日志格式
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler("diff_orchestrator.log", mode="a", encoding="utf-8")  # 输出到日志文件
    ]
)
logger = logging.getLogger("DiffOrchestrator")

# ================= 全局变量 =================
AGENT_NAME = "diff_orchestrator"
AGENT_TYPE = "orchestrator"
AGENT_DESCRIPTION = "An orchestrator that manages a text processing pipeline."
AGENT_PRIORITY = 1
WORKFLOW_FROM_ROUTER = {
    "name": "diff_worker",
    "worker": "diff_worker",
    "description": "Analyze the difference between worker"
}

# ================= 工具函数 =================

def calculate_statistics(input_data):
    """
    统计输入数据的差异信息，包括新增、修改、删除项的数量。
    """
    logger.debug("开始计算差异统计信息...")
    total_diff = 0
    new_items = 0
    modified_items = 0
    deleted_items = 0

    for item in input_data:
        change_type = item["data"].get("type", "")
        total_diff += 1

        if change_type == "new" or change_type == "add":
            new_items += 1
        elif change_type == "update":
            modified_items += 1
        elif change_type == "delete":
            deleted_items += 1

    stats = {
        "total_diff": total_diff,
        "new_items": new_items,
        "modified_items": modified_items,
        "deleted_items": deleted_items
    }
    return stats

def transform_data(input_data):
    """
    转换原始数据为统一的差异格式，便于后续处理。
    """
    logger.debug("开始转换数据...")
    result = {"originPage": [], "comparePage": [], "diff": []}

    for item in input_data:
        old_data = item["data"].get("old", {})
        new_data = item["data"].get("new", {})
        ai_judge_data = item["data"].get("ai_judge", {})

        diff_entry = {
            "type": item["data"].get("type", ""),
            "contentType": item["data_type"],
            "originText": old_data.get("content", ""),
            "originKeyword": old_data.get("diff_context", ""),
            "compareText": new_data.get("content", ""),
            "compareKeyword": new_data.get("diff_context", ""),
            "aiJudgeType": ai_judge_data.get("judge_result"),
            "aiJudge": ai_judge_data.get("judge_reason", ""),
            "originRegion": [],
            "compareRegion": []
        }

        # 填充 originRegion 和 compareRegion
        diff_entry["originRegion"].append(_build_region(old_data, item.get("block_name", "")))
        diff_entry["compareRegion"].append(_build_region(new_data, item.get("block_name", "")))

        result["diff"].append(diff_entry)

    logger.info(f"数据转换完成，共生成 {len(result['diff'])} 条差异记录")
    return json.loads(json.dumps(result))

def _build_region(data, block_name):
    """
    构建区域信息，用于 originRegion 和 compareRegion。
    """
    return {
        "page": data.get("page_num", block_name),
        "x": data.get("position", {}).get("x", 0),
        "y": data.get("position", {}).get("y", 0),
        "width": data.get("position", {}).get("width", 0),
        "height": data.get("position", {}).get("height", 0)
    }

async def setup_and_execute_orchestrator(file_pathA: str = None, file_pathB: str = None, user_input: str = None, llm_config: dict = None, ai_judge: bool = True):
    """
    设置并执行 DiffOrchestrator 的主流程。
    """
    logger.info("开始设置并执行 Orchestrator...")
    orchestrator_config = AgentConfig(
        name=AGENT_NAME,
        type=AGENT_TYPE,
        description=AGENT_DESCRIPTION,
        priority=AGENT_PRIORITY,
        parameters={}
    )
    orchestrator = DiffOrchestrator(orchestrator_config)

    if not user_input:
        user_input = "中华人民共和国核心价值观是什么？"
    if not file_pathA and not file_pathB:
        file_paths = ["mock/path/to/before_file1.docx", "mock/path/to/after_file1.docx"]
    else:
        file_paths = [file_pathA, file_pathB]
    if not llm_config:
        llm_config = model_configs

    logger.debug(f"输入文件路径: {file_paths}")
    context = AgentContext(
        session_id=str(uuid.uuid4()),
        input_data={
            "user_input": user_input,
            "temperature": 0.7,
            "max_tokens": 1500
        },
        metadata={
            "file_path_list": file_paths,
            "batch_size": 10,
            "ai_judge": ai_judge,
            "prompt": prompt_ai_judge,
            "llm_config": llm_config
        }
    )

    worker_config = AgentConfig(
        name="diff_worker",
        type="worker",
        description="A worker agent for analyzing diffs.",
        priority=1,
        parameters={"llm_provider": "openai"}
    )
    worker = DiffWorker(worker_config)
    orchestrator.workers = {"diff_worker": worker}

    # result,sheet_info = await orchestrator._execute_step(WORKFLOW_FROM_ROUTER, context)
    result,new_sheet,old_sheet = await orchestrator._execute_step(WORKFLOW_FROM_ROUTER, context)

    detail = transform_data(result)
    detail["new_sheet"] = new_sheet
    detail["old_sheet"] = old_sheet
    stats = calculate_statistics(result)

    base_dir = os.path.dirname(file_pathA)
    parent_dir = os.path.dirname(base_dir)  # 回退一级目录
    report_path = os.path.join(parent_dir, "output")  # 在回退的目录中连接 'output'


    formatted_result = {
        "total_diff": stats.get("total_diff", 0),
        "new_items": stats.get("new_items", 0),
        "modified_items": stats.get("modified_items", 0),
        "deleted_items": stats.get("deleted_items", 0),
        "report_path": report_path,
        "report_detail": detail
    }
    logger.info(f"Orchestrator 执行完成，结果: {formatted_result}")
    return formatted_result

# ================= 主流程入口 =================

if __name__ == "__main__":
    old_path = r"D:\data_zh\data_zh\excel\before\Excel_b.xlsx"
    new_path = r"D:\data_zh\data_zh\excel\after\Excel_a.xlsx"

    logger.info("程序启动...")
    #diff_obj = DiffTool(old_path, new_path)
    #diff_result = diff_obj.diff()
    #logger.debug(f"DiffTool 结果: {diff_result}")

    asyncio.run(setup_and_execute_orchestrator(old_path, new_path))
    logger.info("程序结束")
