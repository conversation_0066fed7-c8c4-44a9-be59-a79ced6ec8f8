from typing import Dict, List, Optional
import time
import logging
from dataclasses import dataclass
from src.common.exceptions import NotFoundError

logger = logging.getLogger(__name__)

@dataclass
class ServiceInfo:
    """服务信息"""
    service_id: str
    name: str
    host: str
    port: int
    status: str = "active"
    last_heartbeat: float = time.time()
    metadata: Dict = None

class ServiceRegistry:
    """服务注册中心"""
    def __init__(self):
        self._services: Dict[str, ServiceInfo] = {}
        self._service_groups: Dict[str, List[str]] = {}  # 服务组 -> 服务ID列表
    
    def register(self, service_info: ServiceInfo) -> None:
        """注册服务"""
        self._services[service_info.service_id] = service_info
        
        # 添加到服务组
        if service_info.name not in self._service_groups:
            self._service_groups[service_info.name] = []
        if service_info.service_id not in self._service_groups[service_info.name]:
            self._service_groups[service_info.name].append(service_info.service_id)
        
        logger.info(f"Service registered: {service_info.name} ({service_info.service_id})")
    
    def unregister(self, service_id: str) -> None:
        """注销服务"""
        if service_id not in self._services:
            raise NotFoundError(f"Service not found: {service_id}")
        
        service = self._services[service_id]
        self._service_groups[service.name].remove(service_id)
        del self._services[service_id]
        
        logger.info(f"Service unregistered: {service.name} ({service_id})")
    
    def update_heartbeat(self, service_id: str) -> None:
        """更新服务心跳"""
        if service_id not in self._services:
            raise NotFoundError(f"Service not found: {service_id}")
        
        self._services[service_id].last_heartbeat = time.time()
        logger.info(f"Service heartbeat updated: {service_id}")
    
    def get_service(self, service_id: str) -> ServiceInfo:
        """获取服务信息"""
        if service_id not in self._services:
            raise NotFoundError(f"Service not found: {service_id}")
        return self._services[service_id]
    
    def get_services_by_name(self, service_name: str) -> List[ServiceInfo]:
        """获取指定名称的所有服务"""
        if service_name not in self._service_groups:
            return []
        return [self._services[service_id] for service_id in self._service_groups[service_name]]
    
    def get_all_services(self) -> List[ServiceInfo]:
        """获取所有服务"""
        return list(self._services.values())
    
    def check_health(self, timeout: int = 30) -> None:
        """检查服务健康状态"""
        current_time = time.time()
        for service_id, service in list(self._services.items()):
            if current_time - service.last_heartbeat > timeout:
                logger.warning(f"Service heartbeat timeout: {service.name} ({service_id})")
                service.status = "inactive"
            else:
                service.status = "active"

# 创建全局服务注册中心实例
service_registry = ServiceRegistry() 