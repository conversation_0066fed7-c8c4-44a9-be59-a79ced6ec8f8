from fastapi import FastAP<PERSON>, HTTPException, Request
import asyncio
from src.services.llm.connect_model import query_model
app = FastAPI()

prompt_intent = """
        # role：
        You are a helpful user agent great at generating workflows based on users message. However users question may use all tools, use several tools or do not use any tools. You should decide a workflow but DO NOT TRY TO USE ANY TOOLS. Also, whenever you judge no tools should be used, you have to asnwer with natural words in format.

        # task 1：
        According to users words, if the user wants to build a system, you should think about a process that use avaliable tools.

        # task 2:
        If users question do not need to use any tools, you should explain you do can it by yourself and do not rely on expert tool

        # task 3:
        If user want to do precise work, just use the correct tool, however if the user want to do difference analyze, we must use document_parse tool first 

        #Tools list: 
        document_parse: can be used for parse documents, which can be used for parsing excel, word, txt and pdf
        diff_handle: tools be used to analyze differences between documents, need output from document_parse tool

        #example:

        example1 for task1:

        #input:
        help me to analyze the differences between two files

        #output:
        ##COT## (chain of thoughts to show reason why you use document_parse tool and diff_handle tool, you could contain tools function and workflow)

        example2 for task2:

        #input:
        how to translate one in chinese

        #output:
        ##COT## (chain of thoughts to show how you guess users intent, may be the answer do not need professional tools we offer)
        #Answer# (Your natural word to asnwer user's question if the question do not need tools)

        # requirement:
        1. only output the logical  and natural words
        2. you should only return thinking process but any other words or answer
        3. The output should follow the special sign such as ##COT## and #Answe# it is not markdown title
        4. You should asnwer user's question when it do not need to use tools. If you judge user's work need tools we have, you just need to return COT but not answer.
        """
@app.post("/analyze-intent/")
async def analyze_intent(request: Request):
    try:
        data = await request.json()
        user_input = data.get("user_input", "")

        # 如果检测到输入过短的话，就使用默认输入。
        if len(user_input) < 2:
            user_input = "Help me to analyze the files I upload, thank you."

        # 调用模型查询，获取返回结果
        intentToFront = await query_model(user_input, prompt_intent)
        result_sendToFront = {
            "chain_of_thoughts": "",
            "answer": None  # 如果没有答案部分，则为 None
        }

        # 找到 COT 部分
        cot_start_idx = intentToFront.find("##COT##")
        if cot_start_idx == -1:
            print("字符串中缺少 ##COT## 部分！")
            result_sendToFront = {
                "chain_of_thoughts": intentToFront,
                "answer": None  # 如果没有答案部分，则为 None
            }
            return result_sendToFront

        cot_end_idx = intentToFront.find("#Answer#")  # 定位 Answer 部分的开始
        if cot_end_idx == -1:
            # 没有 Answer 部分时，COT 部分占据整个剩余内容
            result_sendToFront["chain_of_thoughts"] = intentToFront[cot_start_idx + len("##COT##"):].strip()
        else:
            # 分割出 COT 和 Answer 部分
            result_sendToFront["chain_of_thoughts"] = intentToFront[cot_start_idx + len("##COT##"):cot_end_idx].strip()
            result_sendToFront["answer"] = intentToFront[cot_end_idx + len("#Answer#"):].strip()

        # 返回解析结果给前端
        print("================给前端的COT部分对应右上角================")
        return result_sendToFront

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")