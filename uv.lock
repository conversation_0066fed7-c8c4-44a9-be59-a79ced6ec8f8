version = 1
revision = 2
requires-python = ">=3.8"
resolution-markers = [
    "python_full_version >= '3.12' and sys_platform == 'darwin'",
    "python_full_version >= '3.12' and platform_machine == 'aarch64' and sys_platform == 'linux'",
    "(python_full_version >= '3.12' and platform_machine != 'aarch64' and sys_platform == 'linux') or (python_full_version >= '3.12' and sys_platform != 'darwin' and sys_platform != 'linux')",
    "python_full_version == '3.11.*' and sys_platform == 'darwin'",
    "python_full_version == '3.11.*' and platform_machine == 'aarch64' and sys_platform == 'linux'",
    "(python_full_version == '3.11.*' and platform_machine != 'aarch64' and sys_platform == 'linux') or (python_full_version == '3.11.*' and sys_platform != 'darwin' and sys_platform != 'linux')",
    "python_full_version == '3.10.*' and sys_platform == 'darwin'",
    "python_full_version == '3.10.*' and platform_machine == 'aarch64' and sys_platform == 'linux'",
    "(python_full_version == '3.10.*' and platform_machine != 'aarch64' and sys_platform == 'linux') or (python_full_version == '3.10.*' and sys_platform != 'darwin' and sys_platform != 'linux')",
    "python_full_version == '3.9.*' and platform_machine == 'arm64' and sys_platform == 'darwin'",
    "python_full_version == '3.9.*' and platform_machine == 'aarch64' and sys_platform == 'linux'",
    "(python_full_version == '3.9.*' and platform_machine != 'arm64' and sys_platform == 'darwin') or (python_full_version == '3.9.*' and platform_machine != 'aarch64' and sys_platform == 'linux') or (python_full_version == '3.9.*' and sys_platform != 'darwin' and sys_platform != 'linux')",
    "python_full_version < '3.9' and platform_machine == 'arm64' and sys_platform == 'darwin'",
    "python_full_version < '3.9' and platform_machine == 'aarch64' and sys_platform == 'linux'",
    "(python_full_version < '3.9' and platform_machine != 'arm64' and sys_platform == 'darwin') or (python_full_version < '3.9' and platform_machine != 'aarch64' and sys_platform == 'linux') or (python_full_version < '3.9' and sys_platform != 'darwin' and sys_platform != 'linux')",
]

[[package]]
name = "bibrain"
version = "1.0.0"
source = { editable = "." }
