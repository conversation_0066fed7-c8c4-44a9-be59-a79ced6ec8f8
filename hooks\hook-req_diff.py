# -*- coding: utf-8 -*-
"""
@File    : hook-req_diff.py.py
<AUTHOR> zhenp
@Date    : 2025-06-09 13:43
@Desc    : Description of the file
"""
import glob
import importlib.util
import os

# AI generation start

from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# print("==== Hook req_diff loaded! ====")
#
# # Dynamically locate req_diff's package path
# req_diff_path = os.path.dirname(importlib.util.find_spec("req_diff").origin)
# print(f"Found req_diff package path: {req_diff_path}")
#
# # Collect all Python files in req_diff/settings
# datas = [
#     (file, os.path.join('req_diff/settings', os.path.basename(file)))
#     for file in glob.glob(os.path.join(req_diff_path, 'settings/*.py'))
# ]
datas = collect_data_files('req_diff')



# AI generation end