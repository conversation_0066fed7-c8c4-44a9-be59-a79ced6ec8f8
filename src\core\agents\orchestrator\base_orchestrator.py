from typing import Any, Dict, List, Optional
from ..base import BaseAgent, AgentContext, AgentConfig
from loguru import logger

class OrchestratorAgent(BaseAgent):
    """Base class for orchestrator agents that coordinate multiple workers."""

    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.workers: Dict[str, BaseAgent] = {}
        self.workflow_steps: List[Dict[str, Any]] = []

    async def _setup(self) -> None:
        """Setup orchestrator-specific resources."""
        self.logger.info(f"Setting up orchestrator agent: {self.config.name}")
        await self._initialize_workflow()

    async def _teardown(self) -> None:
        """Cleanup orchestrator-specific resources."""
        self.logger.info(f"Tearing down orchestrator agent: {self.config.name}")
        self.workers.clear()
        self.workflow_steps.clear()

    async def register_worker(self, worker: BaseAgent) -> None:
        """Register a worker agent with the orchestrator."""
        self.workers[worker.config.name] = worker
        self.logger.info(f"Registered worker: {worker.config.name}")

    async def unregister_worker(self, worker_name: str) -> None:
        """Unregister a worker agent."""
        if worker_name in self.workers:
            del self.workers[worker_name]
            self.logger.info(f"Unregistered worker: {worker_name}")

    async def execute(self, context: AgentContext) -> Dict[str, Any]:
        """Execute the orchestration workflow."""
        self.logger.info(f"Starting workflow execution for: {self.config.name}")
        try:
            results = []
            for step in self.workflow_steps:
                step_result = await self._execute_step(step, context)
                results.append(step_result)

            return {
                "status": "success",
                "results": results,
                "orchestrator": self.config.name
            }
        except Exception as e:
            self.logger.error(f"Error in workflow execution: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "orchestrator": self.config.name
            }

    async def _initialize_workflow(self) -> None:
        """Initialize the workflow steps. To be implemented by specific orchestrators."""
        raise NotImplementedError("Orchestrator agents must implement _initialize_workflow")

    async def _execute_step(self, step: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Execute a single workflow step."""
        worker_name = step.get("worker")
        if worker_name not in self.workers:
            raise ValueError(f"Worker not found: {worker_name}")

        worker = self.workers[worker_name]
        return await worker.execute(context)