import importlib
import logging
import os
import sys
from importlib.util import find_spec

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.interface.http.middleware.logging import LoggingMiddleware
from src.interface.http.middleware.exception_handler import handle_exceptions

app = FastAPI()
app.middleware("http")(handle_exceptions)
from src.interface.http.routers.task import router as file_upload_router
from src.interface.http.routers.config import router as config_router
from src.interface.http.routers.gateway import router as gateway_router

# 注册异常处理器
app.middleware("http")(handle_exceptions)

def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title="BiBrain Gateway",
        version="1.0.0",
        description="BiBrain - A powerful AI agent system",
        debug=True
    )

    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 添加日志中间件
    app.add_middleware(LoggingMiddleware)

    # 注册路由
    app.include_router(config_router)
    app.include_router(file_upload_router)
    app.include_router(gateway_router)

    return app