data_list={
    "old_path": "D:\\data_zh\\data_zh\\excel\\before\\Excel_b.xlsx",
    "new_path": "D:\\data_zh\\data_zh\\excel\\after\\Excel_a.xlsx",
    "old_file_name": "Excel_b.xlsx",
    "new_file_name": "Excel_a.xlsx",
    "total_diff_count": 53,
    "text_diff_count": 1,
    "table_diff_count": 51,
    "graphic_diff_count": 0,
    "picture_diff_count": 1,
    "extends": [],
    "data": [
        {
            "id": "96bc1e57-ab0e-4a8d-afba-5f987af93464",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "需求名称\t自适应巡航控制（ACC）\t车道保持辅助（LKA）\t自动紧急制动（AEB）\t交通标志识别（TSR）\t自动泊车（APA）",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.47115044843426684,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "942decbf-8553-4dd3-a543-0954e083ebfc",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "需求名称\t自适应巡航控制（ACC）\t车道保持辅助（LKA）\t自动紧急制动（AEB）\t交通标志识别（TSR）\t自动泊车（APA）",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.47115044843426684,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "5962582d-cb19-473b-b000-24e0b7f42363",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "需求描述\t根据前方车辆速度自动调整本车速度，保持安全距离\t通过摄像头识别车道线，并在车辆偏离车道时进行纠正\t在检测到前方碰撞风险时，自动进行制动\t识别道路上的交通标志，并提醒驾驶员\t自动控制车辆进行泊车操作",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.7178734287973892,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "7fa39c93-1e53-43d3-bc75-12004952aa7e",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "需求描述\t根据前方车辆速度自动调整本车速度，保持安全距离\t通过摄像头识别车道线，并在车辆偏离车道时进行纠正\t在检测到前方碰撞风险时，自动进行制动\t识别道路上的交通标志，并提醒驾驶员\t自动控制车辆进行泊车操作",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.7178734287973892,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "4e6d073d-95a8-4569-9132-784f07d11006",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "优先级\t高\t高\t高\t中\t低",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.21151289774352589,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "b31d84c0-d1f5-49f5-9266-33811b465ef8",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "优先级\t高\t高\t高\t中\t低",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.21151289774352589,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "943cdfd7-a69a-41e0-b9e9-918a8f64191e",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "输入\t前方车辆速度、距离、本车速度、驾驶员操作\t摄像头图像、车辆方向盘角度、驾驶员操作\t前方车辆速度、距离、本车速度、驾驶员操作\t摄像头图像\t摄像头图像、超声波传感器数据、驾驶员操作",
                "index": "F2",
                "id": 1,
                "row_index": 0,
                "col_index": 4,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.7094600279324899,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "b0234f6b-247a-46eb-af41-d3e491798e47",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "输入\t前方车辆速度、距离、本车速度、驾驶员操作\t摄像头图像、车辆方向盘角度、驾驶员操作\t前方车辆速度、距离、本车速度、驾驶员操作\t摄像头图像\t摄像头图像、超声波传感器数据、驾驶员操作",
                "index": "F2",
                "id": 1,
                "row_index": 0,
                "col_index": 4,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.7094600279324899,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "d9941edf-5141-4b2c-a716-fa794110c534",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "输出\t油门/刹车控制信号\t方向盘控制信号\t刹车控制信号\t交通标志信息\t方向盘、油门、刹车控制信号",
                "index": "G2",
                "id": 1,
                "row_index": 0,
                "col_index": 5,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.6282707095862297,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "40163ac2-c06e-4b98-86f4-b7b11ea25d4f",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "输出\t油门/刹车控制信号\t方向盘控制信号\t刹车控制信号\t交通标志信息\t方向盘、油门、刹车控制信号",
                "index": "G2",
                "id": 1,
                "row_index": 0,
                "col_index": 5,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.6282707095862297,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "0102ce14-80c5-4f93-8c48-0f24446e2d0e",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "处理逻辑\t根据前方车辆速度和距离计算目标速度，并控制油门/刹车\t识别车道线，计算车辆偏离角度，并控制方向盘进行纠正\t根据前方车辆速度和距离判断碰撞风险，并控制刹车\t识别交通标志，并提取相关信息\t识别停车位，规划泊车路径，并控制车辆进行泊车",
                "index": "H2",
                "id": 1,
                "row_index": 0,
                "col_index": 6,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.7935940365814677,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "c8c3855a-c38c-4cc3-992a-a52cee719e30",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "处理逻辑\t根据前方车辆速度和距离计算目标速度，并控制油门/刹车\t识别车道线，计算车辆偏离角度，并控制方向盘进行纠正\t根据前方车辆速度和距离判断碰撞风险，并控制刹车\t识别交通标志，并提取相关信息\t识别停车位，规划泊车路径，并控制车辆进行泊车",
                "index": "H2",
                "id": 1,
                "row_index": 0,
                "col_index": 6,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.7935940365814677,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "3d5f47e7-df68-4a98-88f9-684e1fee164a",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "界面要求\t仪表盘显示当前巡航速度和与前车距离\t仪表盘显示车道线识别状态和方向盘纠正提示\t仪表盘显示碰撞风险提示和制动状态\t仪表盘显示识别到的交通标志\t仪表盘显示泊车路径和操作提示",
                "index": "I2",
                "id": 1,
                "row_index": 0,
                "col_index": 7,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.0180215046526098,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "8f6ef68f-2aeb-4cb3-9181-6ad48c995fed",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "界面要求\t仪表盘显示当前巡航速度和与前车距离\t仪表盘显示车道线识别状态和方向盘纠正提示\t仪表盘显示碰撞风险提示和制动状态\t仪表盘显示识别到的交通标志\t仪表盘显示泊车路径和操作提示",
                "index": "I2",
                "id": 1,
                "row_index": 0,
                "col_index": 7,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.0180215046526098,
                    "height": 0.6179104477611939
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "af4c5f36-b3a8-4833-b819-9550f2ece57a",
            "type": "update",
            "data_type": "text",
            "content_type": "text",
            "sub_type": "text",
            "head_type": "",
            "title": "",
            "belong_to": "block",
            "diff_point": "text",
            "diff_point_str": "text",
            "rule": "",
            "block_name": "功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "*自适应巡航控制（ACC）：根据前方车辆速度自动调整本车速度，保持安全距离\n*自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动\n*车道保持辅助（LKA）：通过摄像头识别车道线，并在车辆偏离车道时进行纠正",
                "index": "B10",
                "id": 2,
                "row_index": "",
                "col_index": "",
                "diff_context": "自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动\n*车道保持辅助（LKA）：通过摄像头识别车道线，并在车辆偏离车道时进行纠正",
                "diff_content": "自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动\n*|",
                "position": {
                    "x": 0.017668141816284975,
                    "y": 0.8388059701492537,
                    "width": 0.07235524743811926,
                    "height": 0.16119402985074627
                },
                "context": {
                    "header_list": "",
                    "current_row": "",
                    "cell_header": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "highlight": [
                    "39-70"
                ]
            },
            "chapter": "",
            "old": {
                "content": "*自适应巡航控制（ACC）：根据前方车辆速度自动调整本车速度，保持安全距离\n*车道保持辅助（LKA）：通过摄像头识别车道线，并在车辆偏离车道时进行纠正\n*自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动",
                "index": "B10",
                "id": 2,
                "row_index": "",
                "col_index": "",
                "diff_context": "车道保持辅助（LKA）：通过摄像头识别车道线，并在车辆偏离车道时进行纠正\n*自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动",
                "diff_content": "|\n*自动紧急制动（AEB）：在检测到前方碰撞风险时，自动进行制动",
                "position": {
                    "x": 0.017668141816284975,
                    "y": 0.8388059701492537,
                    "width": 0.07235524743811926,
                    "height": 0.16119402985074627
                },
                "context": {
                    "header_list": "",
                    "current_row": "",
                    "cell_header": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "highlight": [
                    "75-106"
                ]
            },
            "raw": {}
        },
        {
            "id": "9a5e55ca-ea79-455d-901b-06b7800b5d34",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "非功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "需求名称\t响应时间\t识别精度\t系统可靠性\t数据安全性",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.6755926790320591,
                    "height": 0.8781249999999999
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "ce0fdc51-ae96-445e-9972-774d1446440f",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "非功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "需求名称\t响应时间\t识别精度\t系统可靠性\t数据安全性",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.6755926790320591,
                    "height": 0.8781249999999999
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "8fb6e0ca-6a63-4180-911a-15ccea096760",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "非功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "需求描述\t智能驾驶功能的响应时间应小于50ms\t车道线识别精度应达到80%以上，交通标志识别精度应达到90%以上\t智能驾驶功能应具备高可靠性，确保在各种工况下稳定运行\t智能驾驶功能应保护用户隐私和数据安全",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.897801252917333,
                    "height": 0.8781249999999999
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "3282493c-b50f-4465-bc97-cee8f6d35007",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "非功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "需求描述\t智能驾驶功能的响应时间应小于100ms\t车道线识别精度应达到95%以上，交通标志识别精度应达到90%以上\t智能驾驶功能应具备高可靠性，确保在各种工况下稳定运行\t智能驾驶功能应保护用户隐私和数据安全",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.897801252917333,
                    "height": 0.8781249999999999
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "e2843558-1e64-4f3d-a75d-620f02c191ea",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "非功能需求",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "优先级\t高\t高\t高\t中",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.37249723621176656,
                    "height": 0.8781249999999999
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "b1777ff4-9d9f-4445-8c68-cb2930c86f63",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "非功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "优先级\t高\t高\t高\t中",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.37249723621176656,
                    "height": 0.8781249999999999
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "a55c8dac-28b8-44e4-84a5-b06b27548fa8",
            "type": "delete",
            "data_type": "picture",
            "content_type": "picture",
            "sub_type": "picture",
            "head_type": "",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "非功能需求",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "id:2\nname:Picture 1\nwidth:99\nheight:94\nstyle.font_style.bold:False\nstyle.font_style.italic:False\nstyle.font_style.underline:False\nstyle.font_style.strikeout:False\ncoordinate.desc:D5\ncoordinate.left:314\ncoordinate.top:111\ndigest:ffe7e7c701c3dbc3",
                "index": "D5",
                "id": 2,
                "row_index": "",
                "col_index": "",
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 314.0,
                    "y": 111.75,
                    "width": 99.75,
                    "height": 94.5
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "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********************************+FAiXR7e7tMJsMwLJ/POxwOv9//uhMYjDGo0SqXy0KhECrnmMlDVookSWb+TH+b82Tt3mBfMdYeE4yq/5f5sJ5nr7srRA8rlYrZbL5586ZOp6MoqlwuEwQBVcxbW1vBYLBUKl22ygKsiVwu7+7utlqtJpNJoVAAtoEkyUqlEgwGAVBxxggwyAkbpD7IfWI/wcq8Ue7P4geCIKCpYIlpmubxeBDwYEq+URQlSRKsEZlMVq1W8/l8qVSCA//EM9RqNZqm+Xw+NLCC+SmVSrVa3draarFY5ubmXC4XE65gXQ5xOJzm5uZbt251d3dDhRVTkwB1iAiCQJnoaaIoKpfLlUolLpcLRY7QwJExJqGGCPgESwcV8ucsezjrRwDuB1UI/G9qaqIoKp1Oww9EIhGfz08mk9VqFcMwmUyG43gmk4lGo2az+ZVjwilCEAQjRmBxApNkMhlBEFtbW++PgXsdIQiiVqs//fTT69evi0QisLOZuZXL5VAoBHGdV15eKpW8Xi+CIAqFQiAQQBSOoihAPsIIhUKBz+fDjoQAPoIg58wKn+V/YBiG4zh5TLBAer2+Wq1Go1FYTaVSKZFIAoEANJUUCARtbW2JRGJ3d/d1C1qr1cLh8Pb2NlzCiDBYJu3t7b29vZfavw1F0ZaWlq6uLgiQ1NuB1Wo1GAxCxuJ19T7pdHplZQXHcYArVCqVWCxGUZRerweBIEkyk8mIxWKRSMTYqGAEXQA/wOvOZDJgLTQ3N4PbAQlanU6nUCigJx6Y54ODg6VSye12RyKR17GkUCi8ePHC7/eD+mI+Zx8T1GeyLo2gZve09qjVakdHR4uLi2C5nr4Q1nptbe3w8FCv13d2doKJHwqF2Gx2S0sLhLbAB5BKpSKRCHRJuVwWCAQymYwpFzpjemc9OYIgoIKi0ShoGI1G09TUlE6n/X4/m81WKBRgJq2urtZqNRRFOzs729ratra2lpaW8vn860be29v75ZdfIpHIiQdOp9Ner/dSE+nVavXw8JDpmlG/8ZeXl51OJ5yOp+WjXC5vb29PTU0JBIKhoaGmpqZSqeT3+6PRqEKhgOAQSZLgMkO1dblcZjw2MMPeOL2z+MFms8EcDAQCAFPj8Xjd3d0EQQD8AMOw7u5uHMcXFhbC4TCLxVKpVOPj43w+f25uDiTgxICw98vl8vLy8k8//bSzs5PNZqvVaqFQCIVCMzMzm5ubl8oPQKVOT09vb29DiqxQKAQCgZmZmenp6UQiwRgyJ64C8EM8Hrfb7UNDQ5B62NzcJAjCZrPJZDJg6u7urlwuNxgMHA6nWCzu7++jKNra2nox+UHg/NLSUiQSMRqNHA5neHh4cXFxe3s7kUio1WqLxdLR0bG0tDQ5OfnNN9/weLyenp5sNuvxeCBJXj8a47uy2WyCIObm5pLJpM1mUygUhULB7/dvbGwcHR3VG9Dwx0VZwIyKdzgc+XzeZrMplUqCIPb397e2tmKxGJwcKIrK5fLTrhiXyx0YGLh3755MJisWi16vd3t7Wy6X9/T08Pn8crns8/kSiUR/fz9Y86lUKhgM8ng8i8Vyzhm+gR8SiaSlpcXhcHg8nmvXronF4vb29tbWVsgzQ2JybGxsZ2dnfn6+ubl5dHRUKpWOjo62tbVB1JohsEmampoYT5UgCLfbHQwGMQyDck3y2HAQCAQ6nQ7HcYCgsy6CYEEJgojH49lsNp/Pr6+vBwIBHo8HNjooZPgll8uFxNoJVXHv3j0ejwcqOhgMLiwsEAQxMTFhNBoRBEmlUouLi7D6MpmsVCrt7+9nMhmTyVSf7n0vfnC53La2NrVavba2dvfuXYDdjY2N/eMf/3j58qXVatXpdFar9d69e999993jx495PN7169cVCoVUKmWaTdYbmkajMRAIMBIA3V2ZJUMQxGKx9Pb2arVaJrxYL2Tv7JTAhQiCEATh9XrX19dJkozH46/8sVAo7Ovrq/8E+nG0tbWBsx0Oh589e7a/v2+xWAYGBkQiUaFQePnypd/vt1gs3d3dKIoeHR1tbm5SFNXb28tENN6XH2w222AwNDc3z8zMOJ1OUFnQV8rj8UA0VyAQjI+PQ/psbm7ObrdD37UTOwJsPqvVCm13Tq8s5zjNdfv2bYjbEwRxWuO9M0GwTyQS6fX669evy2SyFy9evBLXgmFYR0fHCf8JTnhobw39y10ul16vHx8f12q1FEXt7+9PTU1hGDY8PKzRaCqVyv7+PrR36u7uPv883+w0ikSi3t5ep9P5/Pnz0dFRMLFu3boVCASmpqag7ZdcLv/8888lEgljSr7SROHxeL29vS6Xy+FwnD7qNRrN+Ph4LpdbWFg4ODgAa/ii8rjgMEHAqr+/v7e3NxKJrK2tnYjusNlsuVx++/ZtiI2fkG+YDMR0796929bWZrVaURSNx+O//PJLOBweHh4eHByET9bW1nK53O3bt1UqVf1QZz/Lmy19Nptts9msVmsymXz8+DFFUVwut6ur68aNG4lE4tGjR8FgkMViNTU1ffbZZ7du3TrRxuLEUGq1+s6dO2Ad1u99DofT1tYmFouXlpZ8Pl+5XIZgJXVM1fcmGKRYLEYikZWVlUwmY7fbT0uwTCa7ffv2645fWFYURQ0GwyeffALIxFQqNT09vbGxodPpPv30U6lUWigUto9JqVT29vYy6JnzZBPOxQ+JRHLr1i2lUvnixQvoHI3j+Pj4eE9Pz87Ozs8//wwVNGKx+I3ALw6H09XVdf/+fZ1OxzQIAxIKheFwGJK4rMshJtuxu7t7omU4OFvXr18fHR09W90DIkAqlfJ4vGKxuLy8vLCwIJPJvvzyy9bW1kqlEg6HV1dXi8XiwMAAHPXnn+G5glwIgthstvHx8R9++OH777+XyWRmsxn2SLVaDYfDxWLxRDzgDBEBd2liYsLpdAYCATB/KYra3t6GCOsJ0bkM2O7m5iYDbYX11Wg0NpsNOie/sWgKHCmIViUSCdidAwMDYGUtLy/v7u62trba7XaRSPRWyva8/BAIBBMTE36/3+l0Pnr06KuvvtLr9dDONpVKicXiE2HnMwiA8YDgMhqNfr8/kUhks9lAIFDvc1weSwDEDTEesVisUCj0ej2YpB6Pp7m5+ZXJ5npivhIKhYODg93d3R0dHUKhMJfLOZ3O1dVVHMdv3LgBOuCtkp7nrccBmPCDBw9yuZzD4RCJRJ999plKpbJYLBB4PyczYDkCgcDR0VFbW5tOpxscHCQIIpfLwftuGFV7Irp1IXRCQUHrcT6fz2az0+n03t4eSZJ37tw5j4aBSUJGHSIXxWJxbW1tZmaGoihoPg8jX7x8AHE4HIvF8uWXX37//feLi4sajeb+/ftMA53z3xVSAtFoNJfLHR4earVaiUSC47hSqZTL5Wq1GiyCy5APmGS1WoXmZcwJn0gkwE/EcfytTi82m81ketLp9JMnT7LZ7I0bNwYHBwEh9bYzRN/23r29vRRFORwOZh5gMr7tvQGVHAwG4/E4j8cTiURisXh4eNhsNjPa7y2f5bz3LZfLgNiHl1YAIA/yqcxvzrnDYJ5gl0PcsKura2xsTKVSnUitXgo/IB5ut9sNBgPk0c6vpk6PBrqrcEzZbBbDsLa2NjjwLxVoAjeFIGm9Vnzn8lRgCY7jDx484PP5SqXynduQv3X9OahdMOMucNWYN93QH6rMAG53OiTzbgRnidlsfucN+u79Gd6TE/Wg2BPEORVluQw6oxbrfR7qQkq8kPdZ0PPbuPXXcrlctVoNL25kCLLo6uP3ErEumaCRCUQ86wnDMIVCcRrm+l/e3wfDsNHRUfCT61ENarXaarV+gJefcTgcvV5/8+bN7e3t+lIuDMOsVuuJzMd/f38fSMlBxVQ9oEsgEEBdyHuq4HPOIZVKQdKT+RDePYXjOBgUrN8JP85O67PrShQulV7pZ8D++A1bkv5m/a/Ovi/7w6Kqf5O7fwT9yK6ocftZ/j7pih+NRVf8aCy64sfvjB//ffYC/Z/G+sU+4MX755BUgPgzA1qF6Dr0UYOISLVahToPBEEwDDtRHMUQvLgZkqzEMaEoKhaLmdpIePd2fagc7svkUwF9ih5TtVqFWgJ4yef/3+bXa0mSzOfztVoN4v/MIK/sh4ggCBTzXaCJfMH8qFQqHo/H5XLRNC2VSm/evAl9IpLJ5KNHj1gsltVqtdvtKIoGAoGlpaVarabT6drb25eXl+H9nPXbDUXRtrY2u90ej8c3NjYODw8JguByuXK5vOOYBAJBJpOZn59Pp9MMHofL5RoMBpvNJhKJXC6Xz+cDZFdra+vW1pbH4+HxeGNjY/XNUQGp7j2mVCoFKCcAscvl8q1jOu0/SiSSnp6e5ubmC/RXLpgfgJ6am5uD1DR0MOBwOJlM5smTJxC+7enp4XA4kUjk+fPnAN+TSqXT09One+4CmLOpqWlyctLlcqXTaeh2yePxNjY2bt26NTIyks/n5+bmgsEgxAQpioJQWKFQ6O3t9Xg8s7OzGIYJBAKDweD1eh8/fgydRaBQHJiRzWaXl5enp6cjkQhIKiC1Dg4ORkZG9vb2nj17BuC8+umpjwlqMBqRH7VaDd7dCAhdkiRdLld7ezsURmSzWRRFGb0EhSpQcSSRSCwWSy6XSyaTUEesUCgsFgvUXDmdzvn5+Xw+bzKZ+vv7M5nMzMyMx+NhsVgajUYgEOTzeWiP8Ne//hU6HgcCga2tLbPZXCwWIdMF5T/wL2g/ZtoURQWDwenp6d3dXSiJB8EKBoPFYpHP56tUqq6urkKhsL+/DwX5BoNBo9HI5XKZTHaxzvxF8qNSqezu7sZiMchWRqNRh8Px5ZdfwoHBoJWZbtFM6F6r1X799dflcnlpaQnKjYxG4zfffAMx4IcPH2YyGWgVaLPZoDnV48eP/X5/IBDo6OiAbcvlcsfGxgKBgNPpzB8THBVM/WN9GWq9VqxUKqFQyOv1Anr/k08+EYlEJpPpb3/7WzqdDgaDdru9o6Mjm83+8MMPmUwG6j9GRka4XO6FN+i/SPuqUCisrq5SFGUymeAlwIlEYmtr6/QOOlEWJRAIAHEj+3W7wSc6nQ5FUaj3UiqVra2tUqlUrVZD09xSqQTVRzBaqVT67rvvfvrpp6OjI+iez8DaXlfvxKTToZ8FwNqhEw68kpWiKMCDGQwGvV4PiX1AzhmNRq1We36g9IfmB3Rv8Hg8KIpKJBKdTgfljvPz8/WVSK8sB2ai6+y6qmGmJBckqf6F58w76OvzuxRFud3uUCgEvdkGBgagA9F5Zg63ANsMUm1MDr8e28DcjskIXGzS+iL1Va1WW1paIkkSRVGv1xuPx6H6aGtrKxKJQPYNzJh0Oo3jOHRZBdPzBDNYdQ8MJTxQyhaLxaRSKUEQUKyPYZhYLAbMOWAMb9++zefzoacINKc4HdiHJYY+cHC7crkM40ApWzqdFgqF0WgUmqzABOpxJCdmyLpQujB+ZDKZpaUlUDUIguTzeYFAUCgUwOIaHR0FKKLP53v27BmO406ns1ar8fl8qISsf0IgpkTBbDZvbm4eHR1NTk5GIpFcLreysgK9dYxGI4qiYFlxudzx8XEoYWYK4185VaiWCwQCjPKBHirhcNjhcIBluL29TZKkWCzW6/WgRZlZAZ34t7H4UavVXC5XLBbDMKyrq2toaAi6xzx8+BBMyfFjyufz0Wh0cnKSy+WCudXX19fT0/M6BBf7GKPd398fiURcLtfLly93d3er1Woul9PpdKOjo2azGRqHgB0slUoBEvg6/xlQLMVi0el0AuqOw+G0t7d/++23Y2Nj09PToVAIjh+SJEUikd1u7+/vhy4IrA9CFyYfbDYbyrl6enqYWmBYOx6Ph2HYyMgIj8eDVhTQ+UOn08ErNJkRDAbDxMREuVxmnCwohnzw4EFLSwsIB4fDaWpqgoaqcEKMjIy0t7fX48EY4DbMhMPhtLS08Hi8jo6O+/fvM30uwMeGA3xsbEyhUOzv70N7A6lUajQarVar2WyGo4vL5UKBAZ/P1+v1l9Rg8GLyUdD1DbrNiMVi6EUA1ddg6QNEDAIeBEFQFAWQfejHwnSXyOfzuVyuVqvxeLympiZG3ZfLZTBhC4UCKDGo/YEmaNACFGDz9YihWq0GfaggMS4QCODfE8qHx+MpFArwTnK5XD6fpyhKIBDgOC4SiZhwSLVahZ4+NE1Df6LLUFkXlh8EIwoMFcg/MzsICgMZiDT082BMJmZ16F/tJfi7fmXr4WsQBKtHcNWX4Z5uGgu3YyZwuma3PmFeX5R1Agx2wnO6cMvq/yZzsZqRWdDTuF5GrTMQm9MlXPR/GsGvG/mtcLEnghznec/n2ZBRxsG8jPP84vPnp+3L0z94tyehz7eUHzVd4Rkai67yg41FV/xoLLriR2MRwmY3+itdWb8n+h/upXz/Bjxb9gAAAABJRU5ErkJggg=="
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "d2177615-152c-480f-a213-81d9d64d409d",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "接口名称\t摄像头图像接口\t超声波传感器接口\t车辆速度接口\t方向盘角度接口\t油门控制接口\t方向盘控制接口",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.0068562113439123,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "11e86819-9868-4b9e-ae62-92532e834fd0",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "接口名称\t摄像头图像接口\t超声波传感器接口\t车辆速度接口\t方向盘角度接口\t油门/刹车控制接口\t方向盘控制接口",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.0068562113439123,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "d7f76fbc-17c4-4426-8d9f-15e65143bf40",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "接口描述\t获取车辆前方摄像头图像\t获取车辆周围超声波传感器数据\t获取车辆速度信号\t获取车辆方向盘角度信号\t控制车辆油门\t控制车辆方向盘",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.2657620942609185,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "253811fb-2f2b-4bb1-a61c-a53c080cfef8",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "接口描述\t获取车辆前方摄像头图像\t获取车辆周围超声波传感器数据\t获取车辆速度信号\t获取车辆方向盘角度信号\t控制车辆油门/刹车\t控制车辆方向盘",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.2657620942609185,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "ed43dc13-0a87-4fcd-a521-60d4a83f6f30",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "输入\t无\t无\t无\t无\t油门控制信号\t方向盘控制信号",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9590305413050778,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "8efdf07a-494c-4c21-b7d0-260ee65ca17e",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "输入\t无\t无\t无\t无\t油门/刹车控制信号\t方向盘控制信号",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9590305413050778,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "af7cd831-05cc-49ad-983e-7c8e3d2fa537",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "输出\t摄像头图像\t超声波传感器数据\t车辆速度信号\t方向盘角度信号\t无\t无",
                "index": "F2",
                "id": 1,
                "row_index": 0,
                "col_index": 4,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9349379105336354,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "666c8c84-3b20-4eeb-9fbc-3e8f2dcb76d9",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "输出\t摄像头图像\t超声波传感器数据\t车辆速度信号\t方向盘角度信号\t无\t无",
                "index": "F2",
                "id": 1,
                "row_index": 0,
                "col_index": 4,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9349379105336354,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "71a26e3c-31a9-488c-92bc-3b4a77b7c200",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "数据格式\tRGB图像\t距离数据\t数字信号\t数字信号\t数字信号\t数字信号",
                "index": "G2",
                "id": 1,
                "row_index": 0,
                "col_index": 5,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9061705902095224,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "2399d9f4-a088-4e19-a55e-f215f5baf3f4",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "数据格式\tRGB图像\t距离数据\t数字信号\t数字信号\t数字信号\t数字信号",
                "index": "G2",
                "id": 1,
                "row_index": 0,
                "col_index": 5,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9061705902095224,
                    "height": 0.7885714285714286
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "1fb4e78e-a7ab-4279-a578-571208c2a9d6",
            "type": "update",
            "data_type": "table",
            "content_type": "cell",
            "sub_type": "cell",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "text",
            "diff_point_str": "text",
            "rule": "",
            "block_name": "接口定义",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "I-ID-0011",
                "index": "B3",
                "id": 1,
                "row_index": 1,
                "col_index": 0,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.060267536079014264,
                    "y": 0.21142857142857144,
                    "width": 0.09427290597880787,
                    "height": 0.13142857142857142
                },
                "context": {
                    "header_list": "接口编号|接口名称|接口描述|输入|输出|数据格式",
                    "current_row": "I-ID-0011|摄像头图像接口|获取车辆前方摄像头图像|无|摄像头图像|RGB图像",
                    "cell_header": "接口编号",
                    "pre_context": "",
                    "next_context": "I-ID-002|超声波传感器接口|获取车辆周围超声波传感器数据|无|超声波传感器数据|距离数据"
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "old": {
                "content": "I-ID-001",
                "index": "B3",
                "id": 1,
                "row_index": 1,
                "col_index": 0,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.060267536079014264,
                    "y": 0.21142857142857144,
                    "width": 0.09427290597880787,
                    "height": 0.13142857142857142
                },
                "context": {
                    "header_list": "接口编号|接口名称|接口描述|输入|输出|数据格式",
                    "current_row": "I-ID-001|摄像头图像接口|获取车辆前方摄像头图像|无|摄像头图像|RGB图像",
                    "cell_header": "接口编号",
                    "pre_context": "",
                    "next_context": "I-ID-002|超声波传感器接口|获取车辆周围超声波传感器数据|无|超声波传感器数据|距离数据"
                },
                "data": "",
                "head_content": ""
            },
            "raw": {}
        },
        {
            "id": "27dc688e-479e-4f19-b16d-6a80689b061a",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "测试用例名称\tACC功能测试\tLKA功能测试\tAEB功能测试\tTSR功能测试\tAPA功能测试",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.5112115714242758,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "b15566dc-5794-425b-8ead-3da3bbeac286",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "测试用例名称\tACC功能测试\tLKA功能测试\tAEB功能测试\tTSR功能测试\tAPA功能测试",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.5112115714242758,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "0f2e87e7-8da9-4613-a0b3-5d2d5dc957a7",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "测试目的\t验证ACC功能能够根据前方车辆速度自动调整本车速度\t验证LKA功能能够识别车道线并纠正车辆偏离\t验证AEB功能能够在检测到碰撞风险时自动制动\t验证TSR功能能够识别交通标志\t验证APA功能能够自动完成泊车操作",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.0524944117558617,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "30208aa0-7bfe-4612-b256-9203ee30289f",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "测试目的\t验证ACC功能能够根据前方车辆速度自动调整本车速度\t验证LKA功能能够识别车道线并纠正车辆偏离\t验证AEB功能能够在检测到碰撞风险时自动制动\t验证TSR功能能够识别交通标志\t验证APA功能能够自动完成泊车操作",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.0524944117558617,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "66411d3f-597e-4bd2-b627-19aced6b40a2",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "输入\t前方车辆速度：0-100km/h，本车速度：0-100km/h\t车辆偏离车道线\t前方车辆突然减速\t道路上的交通标志\t停车位",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9489991279332011,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "09301298-0913-4928-9c04-170a4aa51350",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "输入\t前方车辆速度：0-100km/h，本车速度：0-100km/h\t车辆偏离车道线\t前方车辆突然减速\t道路上的交通标志\t停车位",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9489991279332011,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "0910f3bd-be5b-468c-9d56-16347c82fbf7",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "预期输出\t本车速度根据前方车辆速度自动调整，保持安全距离\t方向盘自动纠正，使车辆回到车道中央\t本车自动制动，避免碰撞\t仪表盘显示识别到的交通标志\t车辆自动完成泊车操作",
                "index": "F2",
                "id": 1,
                "row_index": 0,
                "col_index": 4,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9923518739412409,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "c5a2370c-51bd-4525-a421-558e69c24d8a",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "预期输出\t本车速度根据前方车辆速度自动调整，保持安全距离\t方向盘自动纠正，使车辆回到车道中央\t本车自动制动，避免碰撞\t仪表盘显示识别到的交通标志\t车辆自动完成泊车操作",
                "index": "F2",
                "id": 1,
                "row_index": 0,
                "col_index": 4,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.9923518739412409,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "5ac92c6a-8ffd-4b35-8638-3972a4ae1171",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "实际输出\t\t\t\t\t",
                "index": "G2",
                "id": 1,
                "row_index": 0,
                "col_index": 5,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.3909264957950343,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "48db86fe-fb1f-4aa0-8725-adc7dc075175",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "实际输出\t\t\t\t\t",
                "index": "G2",
                "id": 1,
                "row_index": 0,
                "col_index": 5,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.3909264957950343,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "fc59ca2f-372a-4797-8e74-888d6b5f0c6a",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "测试结果\t\t\t\t\t",
                "index": "H2",
                "id": 1,
                "row_index": 0,
                "col_index": 6,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.40771628760161494,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "3e860d29-ae77-4139-b068-f20aafa58bbe",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "测试用例",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "测试结果\t\t\t\t\t",
                "index": "H2",
                "id": 1,
                "row_index": 0,
                "col_index": 6,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.40771628760161494,
                    "height": 0.8262910798122065
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "b3275697-5dd6-4309-90b2-c74b992e9d50",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "附录",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "内容\t1. ACC: 自适应巡航控制\n2. LKA: 车道保持辅助\n3. AEB: 自动紧急制动\n4. TSR: 交通标志识别\n5. APA: 自动泊车\t1. ISO 26262:2018 Road vehicles — Functional safety.\n2. SAE J3016:2021 Taxonomy and definitions for terms related to driving automation systems for on-road motor vehicles.",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.4764558029986108,
                    "height": 0.832579185520362
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "b1b46c49-9dff-43bd-9884-0b8de687d194",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "附录",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "内容\t1. ACC: 自适应巡航控制\n2. LKA: 车道保持辅助\n3. AEB: 自动紧急制动\n4. TSR: 交通标志识别\n5. APA: 自动泊车\t1. ISO 26262:2018 Road vehicles — Functional safety.\n2. SAE J3016:2021 Taxonomy and definitions for terms related to driving automation systems for on-road motor vehicles.",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 1.4764558029986108,
                    "height": 0.832579185520362
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "557cb8eb-739d-4d7a-bea5-85d15017014d",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "History",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "修改日期\t2023-10-26T00:00:00",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.18062604988891526,
                    "height": 0.3898305084745763
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "04a48a66-79fc-4bdd-8fb4-eca614416f37",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "History",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "修改日期\t2023-10-26T00:00:00",
                "index": "C2",
                "id": 1,
                "row_index": 0,
                "col_index": 1,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.18062604988891526,
                    "height": 0.3898305084745763
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "c2bd08fb-1d24-4daa-bbc2-7b9be4dd28ef",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "History",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "修改内容\t初始版本",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.4949153766956272,
                    "height": 0.3898305084745763
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "d5d5a7c9-3f6f-401c-b01f-599c83284dad",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "History",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "修改内容\t初始版本",
                "index": "D2",
                "id": 1,
                "row_index": 0,
                "col_index": 2,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.4949153766956272,
                    "height": 0.3898305084745763
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "0b701ba5-6f8a-4430-bda6-2d5cd94b79b7",
            "type": "add",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "History",
            "extend": [],
            "ai_judge": {},
            "new": {
                "content": "修改人\t",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.18785109188447113,
                    "height": 0.3898305084745763
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        },
        {
            "id": "38469917-a08f-493c-803c-5ee373279e8f",
            "type": "delete",
            "data_type": "table",
            "content_type": "col",
            "sub_type": "col",
            "head_type": "horizontal",
            "title": "",
            "belong_to": "block",
            "diff_point": "",
            "diff_point_str": "",
            "rule": "",
            "block_name": "History",
            "extend": [],
            "ai_judge": {},
            "old": {
                "content": "修改人\t",
                "index": "E2",
                "id": 1,
                "row_index": 0,
                "col_index": 3,
                "diff_context": "",
                "diff_content": "",
                "position": {
                    "x": 0.0,
                    "y": 0.0,
                    "width": 0.18785109188447113,
                    "height": 0.3898305084745763
                },
                "context": {
                    "header_list": "",
                    "cell_header": "",
                    "current_row": "",
                    "pre_context": "",
                    "next_context": ""
                },
                "data": "",
                "head_content": ""
            },
            "chapter": "",
            "raw": {}
        }
    ]
}


def count_braces(data_list):
    """
    Counts the number of `{}` appearing in the 'data' list.

    :param data_list: A list of dictionaries representing change points.
    :return: Number of occurrences of `{}`.
    """
    braces_count = 0
    for change_point in data_list:
        for key, value in change_point.items():
            if value == {}:
                braces_count += 1
    return braces_count

# Example usage:
import json


# Assuming `data` is the list inside loaded JSON
result = count_braces(data_list["data"])
print(result)