# 示例 Agent 实现指南

本文档通过实现一个天气查询 Agent 来展示如何开发一个完整的 Agent。

## 1. 项目结构

```
weather_agent/
├── __init__.py
├── agent.py
├── config.yaml
├── requirements.txt
├── schemas/
│   ├── input.json
│   └── output.json
├── tests/
│   ├── __init__.py
│   ├── test_agent.py
│   └── test_integration.py
└── README.md
```

## 2. 配置文件

### 2.1 config.yaml

```yaml
agent:
  name: "weather_agent"
  version: "1.0.0"
  description: "天气查询 Agent"
  
  capabilities:
    - name: "get_weather"
      description: "获取指定城市的天气信息"
      input_schema: "schemas/input.json"
      output_schema: "schemas/output.json"
    
  resources:
    max_memory: "256Mi"
    max_cpu: "0.2"
    
  health_check:
    endpoint: "/health"
    interval: "30s"
    timeout: "5s"
    
  weather_api:
    base_url: "https://api.weather.com"
    api_key: "${WEATHER_API_KEY}"
    timeout: 5
```

### 2.2 输入输出 Schema

```json
// schemas/input.json
{
  "type": "object",
  "properties": {
    "city": {
      "type": "string",
      "description": "城市名称"
    },
    "date": {
      "type": "string",
      "format": "date",
      "description": "查询日期"
    }
  },
  "required": ["city"]
}
```

```json
// schemas/output.json
{
  "type": "object",
  "properties": {
    "temperature": {
      "type": "number",
      "description": "温度"
    },
    "humidity": {
      "type": "number",
      "description": "湿度"
    },
    "weather": {
      "type": "string",
      "description": "天气状况"
    }
  }
}
```

## 3. Agent 实现

### 3.1 agent.py

```python
from typing import Dict, Any
import aiohttp
from datetime import datetime
from src.core.agent import BaseAgent
from src.core.validators import SchemaValidator


class WeatherAgent(BaseAgent):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.name = "weather_agent"
        self.validator = SchemaValidator()
        self.session = None

    async def initialize(self):
        """初始化 Agent"""
        self.session = aiohttp.ClientSession()
        await self.validator.load_schemas(
            self.config["capabilities"][0]["input_schema"],
            self.config["capabilities"][0]["output_schema"]
        )

    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理请求"""
        # 验证输入
        if not await self.validator.validate_input(request):
            raise ValueError("Invalid input")

        # 获取天气信息
        weather_data = await self._get_weather(
            request["city"],
            request.get("date", datetime.now().strftime("%Y-%m-%d"))
        )

        # 验证输出
        if not await self.validator.validate_output(weather_data):
            raise ValueError("Invalid output")

        return weather_data

    async def _get_weather(self, city: str, date: str) -> Dict[str, Any]:
        """调用天气 API"""
        async with self.session.get(
                f"{self.config['weather_api']['base_url']}/weather",
                params={
                    "city": city,
                    "date": date,
                    "api_key": self.config["weather_api"]["api_key"]
                },
                timeout=self.config["weather_api"]["timeout"]
        ) as response:
            if response.status != 200:
                raise Exception(f"Weather API error: {response.status}")
            return await response.json()

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查 API 连接
            async with self.session.get(
                    f"{self.config['weather_api']['base_url']}/health"
            ) as response:
                if response.status == 200:
                    return {"status": "healthy"}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def cleanup(self):
        """清理资源"""
        if self.session:
            await self.session.close()
```

## 4. 测试实现

### 4.1 test_agent.py

```python
import pytest
from unittest.mock import Mock, patch
from weather_agent import WeatherAgent

@pytest.fixture
def config():
    return {
        "capabilities": [{
            "input_schema": "schemas/input.json",
            "output_schema": "schemas/output.json"
        }],
        "weather_api": {
            "base_url": "https://api.weather.com",
            "api_key": "test_key",
            "timeout": 5
        }
    }

@pytest.fixture
async def agent(config):
    agent = WeatherAgent(config)
    await agent.initialize()
    yield agent
    await agent.cleanup()

async def test_agent_initialization(agent):
    assert agent.name == "weather_agent"
    assert agent.session is not None

async def test_handle_request(agent):
    with patch("aiohttp.ClientSession.get") as mock_get:
        mock_get.return_value.__aenter__.return_value.status = 200
        mock_get.return_value.__aenter__.return_value.json.return_value = {
            "temperature": 25,
            "humidity": 60,
            "weather": "sunny"
        }
        
        result = await agent.handle_request({"city": "Beijing"})
        assert result["temperature"] == 25
        assert result["humidity"] == 60
        assert result["weather"] == "sunny"
```

### 4.2 test_integration.py

```python
import pytest
from weather_agent import WeatherAgent

@pytest.mark.integration
async def test_weather_api_integration():
    config = {
        "capabilities": [{
            "input_schema": "schemas/input.json",
            "output_schema": "schemas/output.json"
        }],
        "weather_api": {
            "base_url": "https://api.weather.com",
            "api_key": "test_key",
            "timeout": 5
        }
    }
    
    agent = WeatherAgent(config)
    await agent.initialize()
    
    try:
        result = await agent.handle_request({"city": "Beijing"})
        assert "temperature" in result
        assert "humidity" in result
        assert "weather" in result
    finally:
        await agent.cleanup()
```

## 5. 部署配置

### 5.1 Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["python", "-m", "weather_agent"]
```

### 5.2 requirements.txt

```
bibrain-core>=1.0.0
aiohttp>=3.8.0
pytest>=7.0.0
pytest-asyncio>=0.18.0
```

## 6. 使用示例

### 6.1 启动 Agent

```bash
# 设置环境变量
export WEATHER_API_KEY=your_api_key

# 启动 Agent
python -m weather_agent
```

### 6.2 调用示例

```python
import asyncio
from weather_agent import WeatherAgent

async def main():
    config = {
        "capabilities": [{
            "input_schema": "schemas/input.json",
            "output_schema": "schemas/output.json"
        }],
        "weather_api": {
            "base_url": "https://api.weather.com",
            "api_key": "your_api_key",
            "timeout": 5
        }
    }
    
    agent = WeatherAgent(config)
    await agent.initialize()
    
    try:
        result = await agent.handle_request({
            "city": "Beijing",
            "date": "2024-03-20"
        })
        print(f"Weather in Beijing: {result}")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

## 7. 最佳实践

### 7.1 错误处理

- 使用 try-except 处理异常
- 实现重试机制
- 记录详细错误日志

### 7.2 性能优化

- 使用连接池
- 实现缓存机制
- 异步处理请求

### 7.3 安全考虑

- 验证输入数据
- 保护 API 密钥
- 实现访问控制

## 8. 注意事项

1. 确保正确处理资源清理
2. 实现完整的错误处理
3. 添加适当的日志记录
4. 遵循配置管理最佳实践
5. 编写完整的测试用例 