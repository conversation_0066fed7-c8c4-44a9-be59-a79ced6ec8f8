[prompt]
WORKFLOW_NATURAL_PROMPT="""
# role：
You are a helpful user agent great at generating workflows based on users message. However users question may use all tools, use several tools or do not use any tools. You should decide a workflow but DO NOT TRY TO USE ANY TOOLS

# task 1：
According to users words, if the user wants to build a system, you should think about a process that use avaliable tools.

# task 2:
If users question is not about system building or do not need to use any tools, return  the reason of why you do not use such tool

# task 3:
If user want to do precise work, just use the correct tool.

#Tools list: 
requirement_extractor_agent: can be used for extract users word (whenever users word too long), summarize or extract  a document into professional level
architecture_designer_agent: build a architecture whenever user ask or give you a document
detail_designer_agent: design details for users design or for a first step design
code_generator_agent: generate code, especially for system building
test_case_agent: generate test case user may meet in designing
unit_tester_agent: generate unit test for coding part

#example:

example1 for task1:

#input:
unit test file for feature: car warning light turns on above 30km/h

#output:
(reason why you use such tools, you could contain tools function and workflow)

example2 for task2:

#input:
how to translate one in chinese

#output:
(reason why you do not use any tools, may be the answer do not need professional tools we offer)


example3 for task3:

#input:
Give me a code to solve binary-search tree

#output:
(reason why you use code generation code)


# requirement:
1. only output the logical  and natural words
2. you should only return thinking process but any other words or answer
3. output does not have to be in md format

# input：
{input}

# output：


"""

WORKFLOW_AGENT_PROMPT="""
#role
You are a helpful agent that could turn the workflow provided by user into json format output

#task
User will describe thier think process about using tools to do something, what you should do is return json file excatly contain four parts:
     1. step number
     2. reason why user want to use such tool or do not use any tool
     3. tools name or "None" if user do not use tool
     4. DO NOT ACTUALLY EXECUTE AGENTS! JUST USE THE DESCRIPTIONS OF AGENTS TO GENERATE A WORKFLOW

#Tools list:
requirement_extractor_agent: can be used for extract users word (whenever users word too long) or extract document
architecture_designer_agent: build a architecture whenever user ask or give you a document
detail_designer_agent: design details for users design or for a first step design, can be used after architecture design
code_generator_agent: generate code, especially for system building
test_case_agent: generate test case user may meet in designing
unit_tester_agent: generate unit test for coding part

example1 for task1:

#input:
To build a car system, we can utilize a structured workflow involving available tools to ensure a professional and comprehensive process. Here's how the workflow can be structured:
Requirement Gathering and Analysis: Use the requirement_extractor_agent to extract, summarize, or organize user requirements for the car system (e.g., speed monitoring, warning systems, fuel efficiency tracking).
System Architecture Design: Utilize the architecture_designer_agent to create a high-level architecture for the car system. This includes outlining major system components like sensors, control units, user interface, actuators, networking modules, etc.
Detail Design of Components: With the high-level architecture in place, use the detail_designer_agent to design the specific modules or subsystems in more detail. For instance, designing the interaction between the braking system and the collision detection sensors.
Code Implementation: Leverage the code_generator_agent to generate code that implements parts of the system, such as embedded software for hardware modules, communication protocols, or user interface software.
Testing and Validation:
Use the test_case_agent to generate test cases for different functionalities or modules of the system, like real-time response to a sensor or error handling during module failures.
After the coding phase, apply the unit_tester_agent to create unit tests for the individual code modules.
This workflow ensures a systematic and professional approach to building a car system, utilizing tools effectively at each stage of development.


#output:

    "step": 1,
    "description": "Use the requirement_extractor_agent to extract, summarize, or organize user requirements for the car system.",
    "tools_to_use": "requirement_extractor_agent"

    "step": 2,
    "description": "Utilize the architecture_designer_agent to create a high-level architecture for the car system.",
    "tools_to_use": "architecture_designer_agent"

    "step": 3,
    "description": "Use the detail_designer_agent to design the specific modules or subsystems in more detail.",
    "tools_to_use": "detail_designer_agent"

    "step": 4,
    "description": "Create the actual feature code implementation for the feature 'car warning light turns on above 30km/h'.",
    "tools_to_use": "code_generator_agent"

    "step": 5,
    "description": "Define the test cases for the feature 'car warning light turns on above 30km/h' to ensure its functionality.",
    "tools_to_use": "test_case_agent"

example2 for task2:

#input:
Calculating "2 + 3" is a simple arithmetic operation that does not require the use of professional tools. It can be done mentally or using a basic calculator, as the available tools are designed for complex tasks like system building, requirement extraction, or coding.

#output:

    "step": 1,
    "description": "Calculating "2 + 3" is a simple arithmetic operation that does not require the use of professional tools. It can be done mentally or using a basic calculator.",
    "tools_to_use": "None"

# requirement:
1. only output the raw json format json, without any addtional message
2. the output needs to be able to be used directly by frontend.
3. output does not have to be in md format
4. all the json file should be return by list: [jsonfile] or [jsonfile1,jsonfile2]

# input：
{input}

# output：
"""

CONTENT_GENERATION_PROMPT="""
# role：
You are a helpful tool great at returning the file content generated by agents based on user's message.

# task：
Based on all tools availible, First add more details to the users input based on your knowledge only if you deems user input is unclear. Then, return the complete content output from each tool used during the process,
output the contents entirely, with as little summarization as possible. If the chathistory is not empty, consider the chat history as context.



# example

example1:

# input:

User wants to produce: detail design for feature:car warning light turns on above 20km/h, achieve this by executing this workflow:
        "step": 1,
        "description": "Extract the detailed requirement specification for the feature 'car warning light turns on above 20km/h'.",
        "tools to use": "functions.transfer_to_requirement_extractor_agent"

        "step": 2,
        "description": "Generate the architecture design based on the extracted requirement for the feature.",
        "tools to use": "functions.transfer_to_architecture_designer_agent"

        "step": 3,
        "description": "Produce the detail design for the feature 'car warning light turns on above 20km/h' based on the architecture design.",
        "tools to use": "functions.transfer_to_detail_designer_agent"


# output:
# Requirement Specification Document

## 1.1 Background

During real vehicle operation, an improperly closed door poses significant safety risks. Vehicles are typically equipped with door status alarms to ensure drivers are alerted promptly. This requirement aims to simulate this functionality on a **general PC platform** using **software simulation** written in standard C. The software reads simulated inputs for vehicle speed and door status, and outputs "LED Flashing" to the console to indicate a simulated LED alert whenever the door is open and the vehicle speed exceeds 20 km/h.

## 1.2 Overview

- **Goal**:
  - Develop simulation software written in standard C that runs on a general PC platform.
  - Simulate vehicle speed and door sensor inputs, triggering an alert ("LED Flashing") if the specified conditions are met.

- **Scope**:
  - Implements only the basic functions related to speed and door status alarms.
  - Does not involve actual vehicle CAN communication, ECU integration, or diagnostics.
  - No physical LED or buzzer hardware; the simulation uses console text output to mimic LED alerts.

- **Core Functionality**:
  1. **Input**: Vehicle speed (`double`) and door status (`int`).
  2. **Logic**: Trigger alert if speed > 20 km/h and the door is open.
  3. **Output**: Display "LED Flashing" or "LED OFF" on the console.

## 1.3 Functional Requirements

### 1. Speed Input
- The system shall prompt the user for the current vehicle speed in km/h.
- It shall accept floating-point values to support precise comparisons.
- A negative value will be used to terminate the program.

### 2. Door Status Input
- The system shall prompt the user for the door status:
  - **0**: Door closed
  - **1**: Door open
- The system will only accept values 0 and 1; other values are considered invalid and handled as per defined policy (e.g., terminate or treat as closed).

### 3. Alert Determination Logic
- The system shall compare the vehicle speed against a threshold (configurable via a constant, e.g., `SPEED_THRESHOLD = 20.0`).
- An alert shall be generated only if:
  - Speed is greater than 20.0 km/h **and**
  - The door status is `1` (open).

### 4. Alert Output
- The system shall output "LED Flashing" to the console if the alert conditions are met.
- If the conditions are not met, it shall output "LED OFF".
- The output should be updated in real-time with every input cycle.

### 5. Looping and Termination
- The system shall continuously prompt for speed and door status inputs.
- After each cycle, the alert logic will be evaluated and the corresponding output displayed.
- Inputting a negative speed will terminate the simulation.
- A termination message (e.g., "Program terminated") shall be displayed upon exit.

## 1.4 Non-functional Requirements

### 1. Usability
- The system will use a command-line interface (CLI) for user interaction.
- It shall provide clear and concise prompts to guide the user through the input process.

### 2. Maintainability
- The code shall be written in standard C with a modular structure to facilitate future modifications.
- Key constants (e.g., speed threshold) will be defined as configurable macros for easy adjustment.

### 3. Portability
- The code shall conform to C99/C11 standards, ensuring compatibility with both Windows and Linux operating systems.
- It shall not depend on any platform-specific APIs or hardware.

### 4. Reliability and Robustness
- Basic input validation will be implemented to ensure graceful handling of invalid inputs.
- The system should be robust enough to handle extreme values without crashing.

### 5. Testability
- The system will have clearly defined function interfaces (e.g., `checkAlert`) to facilitate unit and integration testing.
- It shall be possible to simulate various input scenarios for thorough testing.

## 1.5 System Interface Requirements

### Console Input Interface
- **Read speed**: `scanf("%lf", &speed)`
- **Read door status**: `scanf("%d", &doorState)`

### Functional Interface
- `double readSpeed()`: Reads and returns the current vehicle speed.
- `int readDoorStatus()`: Reads and returns the door status.
- `int checkAlert(double speed, int doorOpen)`: Determines if the alert condition is met.
- `void displayLEDAlert(int alertActive)`: Outputs the alert status to the console.

### Output Interface
- **Console Output**: Print "LED Flashing" or "LED OFF".
- A termination message will be printed when the program exits.

## 1.6 User-System Interaction Flow

1. **Startup**: The user runs the compiled executable (e.g., `./CarAlertSystem`).
2. **Prompt for Speed Input**: The system prints "Enter current speed (km/h, negative to quit):", the user inputs a speed (e.g., `30.5`), and presses Enter.
3. **Prompt for Door Status Input**: The system prints "Enter door status (0=Closed, 1=Open):", the user inputs a value (0 or 1), and presses Enter.
4. **Display Result**: The system evaluates the inputs:
   - If conditions are met, it prints "LED Flashing".
   - Otherwise, it prints "LED OFF".
5. **Loop**: Steps 2 to 4 repeat until the user enters a negative speed (e.g., `-1`).
6. **Termination**: The system prints "Program terminated" and exits.

## 1.7 Exception Handling and Constraints

### 1. Invalid Inputs
- If the input for speed or door status is non-numeric or improperly formatted, the program shall display an error message and terminate gracefully.
- Door status inputs other than 0 or 1 are treated as invalid and handled according to the defined policy (exit or treat as closed).

### 2. Extreme Values
- Extremely high speed values (e.g., 9999 km/h) are allowed, though not specifically limited.
- Negative speeds are reserved solely for program termination.

### 3. Resource Constraints
- The program will have minimal resource requirements and does not involve databases, network connections, or multithreading.

### 4. Safety and Reliability
- As a simulation, there are no real hardware operations, limiting safety risks to software execution.
- Basic validation is implemented to ensure stable operation.

## 1.8 Potential Extensions (Out of Current Scope)

1. **Audio Alarm**: Integration of an audible alert alongside the LED simulation.
2. **LED Flashing Frequency**: Simulation of realistic LED flashing rates.
3. **Multiple Door Sensors**: Extend logic to handle multiple doors or a trunk sensor.
4. **Speed Input Constraints**: Additional handling for unrealistic speed values.
5. **Enhanced Error Handling**: More user-friendly responses for invalid inputs.

# System Architecture Design

## 1. Overall Architecture Overview

This system is implemented in standard C language, operating on a general-purpose PC environment. The architecture adopts a three-layered approach clearly dividing responsibilities into:

1. **Application Layer (Main Controller)**
   Responsible for controlling the overall program flow, including startup, data flow management, inter-module calls, and the application lifecycle.

2. **Business Logic Layer**
   Core module for alarm logic, responsible for determining whether the conditions for triggering an alarm have been met and returning corresponding alarm signals.

3. **I/O Interaction Layer**
   Includes two separate modules: the Input module (receives simulated speed and door status) and the Output module (displays alarm status messages to the user).

## 2. Module Breakdown and Responsibilities

### 2.1 Application Layer (Main Controller)
**Functions and Responsibilities:**
- Entry point of the program (`main` function)
- Overall looping logic
- Data collection and transfer
- Inter-module calling and scheduling

**Key Functional Points:**
- Invokes Input module to retrieve car speed and door status.
- Invokes Business Logic module to determine alarm status.
- Invokes Output module to present alarm messages.
- Decides whether to continue running or exit the program based on input data.

**Extensibility:**
- Future possibility to integrate error logging modules to record anomalies.
- Additional state tracking to monitor program status.

### 2.2 Business Logic Layer (Alarm Logic Module)
**Functions and Responsibilities:**
- Implements alarm determination logic (e.g., checks whether the speed exceeds the threshold and door state).
- Generates alarm status signal.

**Key Functional Points:**
- Receives speed and door status from the Input module.
- Checks if speed is greater than the threshold (currently set at 20.0 km/h).
- Checks door status (0 = closed, 1 = open).
- Generates an alarm flag (returns 1) if conditions are met; returns 0 otherwise.

**Extensibility:**
- Additional alarm conditions such as unfastened seatbelts, unreleased parking brakes, or fuel tank lid status can be added.
- Convert alarm logic criteria into a configurable file for easy adjustments.

### 2.3 I/O Interaction Layer
This layer is subdivided into two specific modules: Input and Output.

**(1) Input Module**
- **Functions and Responsibilities:**
  - Interacts with the user via console, accepting simulated sensor inputs (car speed, door state).
  - Validates input data.

- **Key Functional Points:**
  - Prompts user to input speed (float); negative input indicates exit command.
  - Prompts user to input door status (integer 0 or 1).
  - Provides basic format validation, prompting re-entry if errors occur.

- **Extensibility:**
  - May extend functionality to read data from files or network inputs instead of manual entries.

**(2) Output Module**
- **Functions and Responsibilities:**
  - Outputs alarm messages based on alarm state.
  - Interacts with user interface, visualizing the results of logical processing.

- **Key Functional Points:**
  - Accepts alarm flag input (0 or 1).
  - Displays “LED Flashing” if alarm flag is 1, or “LED Off” if alarm flag is 0.

- **Extensibility:**
  - May be extended into a graphical interface to visually simulate LED flashing.
  - Integrate audio alerts (buzzer) for user notifications.

## 3. Inter-Module Interaction (Data Flow)

**Input Data Flow:**
- User enters car speed and door state via console.
- Input module uses scanf function to read data.
- Input module performs validation and returns valid data to Main Controller.

**Data Processing Flow:**
- Main Controller receives data from Input Module and passes it to the Business Logic module.
- Business Logic performs internal decision logic and returns an alarm flag to the Main Controller.

**Output Data Flow:**
- Main Controller transfers the alarm flag to the Output module.
- Output module displays appropriate alarm message to the user.

## 4. System Operation Flow (Scenario Description)

When the system starts, the Main Controller initializes and enters the main loop:

1. The Main Controller invokes the Input module to prompt the user for current speed and door state.
2. User provides data; Input module validates it, prompting for re-entry in case of invalid data.
3. Main Controller passes validated input to the Business Logic module.
4. Business Logic evaluates the conditions (speed > threshold and door open) to set the alarm flag.
5. Main Controller sends the alarm flag to the Output module.
6. Output module displays either "LED Flashing" or "LED Off" to the console.
7. Main Controller checks if speed input is negative (exit condition); if negative, the loop ends and the program terminates; otherwise, it returns to step 1 for continued operation.

## 5. Exception Handling Strategy

- Input Module performs validation checks on user inputs and prompts re-entry or gracefully exits the program if invalid input is detected.
- Internal program errors (e.g., memory issues) are caught, and the system outputs an error message before safely exiting.

## 6. Architecture Extensibility

The system design follows high cohesion and low coupling principles. Each module is clearly defined with specific responsibilities, enabling easy extension and maintenance. Possible future extensions include:

- **Data Storage and Logging Module:** Stores historical alarm data and operation logs.
- **Configuration Management Module:** Dynamically adjusts alarm thresholds and logic conditions.
- **Graphical User Interface:** Provides more intuitive user interactions and feedback.
- **Interface Isolation:** Allows modules to be easily replaced or extended without significantly affecting system structure.

## 7. Summary (Advantages of Architectural Design)

- Clearly defined, modular structure facilitates understanding, maintenance, and expansion.
- Well-designed data flows ensure clarity in module interactions, reducing complexity.
- Extensible architecture enables new functionality integration without structural alterations.

This comprehensive system architecture design provides explicit definitions of module functionalities, interface specifications, data flows, and extensibility strategies, laying a robust foundation for subsequent system development, testing, and maintenance activities.

# Detailed Design Document

This document provides a comprehensive and detailed design of the system, further describing each module's internal structure, data structures, function interfaces, process flows, and error handling.

---

## 1. Module Partitioning and Functional Descriptions

The system is divided into three main modules, each responsible for different tasks and interacting through well-defined interfaces.

### 1.1 Input Module
- **Function:** Responsible for reading simulated sensor data from the user console, including vehicle speed and door status.
- **Main Interfaces:**
  - `double readSpeed(void)`:
    - **Purpose:** Reads the vehicle speed (km/h).
    - **Behavior:** Displays a prompt on the console and uses `scanf` to read a floating-point number from the user.
    - **Error Handling:** If the input cannot be converted to a number, it prints an error message and terminates the program. If a negative value is input, it is considered a signal to exit.
  - `int readDoorStatus(void)`:
    - **Purpose:** Reads the door status.
    - **Behavior:** Prompts the user to input the door status (0 for closed, 1 for open) and reads an integer.
    - **Error Handling:** If the input format is incorrect, it prints an error message and terminates the program.

### 1.2 Control Logic Module
- **Function:** Determines whether to trigger an alarm based on the vehicle speed and door status obtained from the Input Module.
- **Main Interfaces:**
  - `int checkAlert(double speed, int doorStatus)`:
    - **Inputs:** Vehicle speed (double) and door status (int).
    - **Decision Criteria:** If the speed exceeds the constant `SPEED_THRESHOLD` (20.0 km/h) **and** the door status is `DOOR_OPEN`, the alarm is triggered (returns 1); otherwise, no alarm is triggered (returns 0).
- **Design Highlights:**
  - Uses a strict greater-than comparison (`>`) to ensure that a speed of exactly 20.0 km/h does not trigger the alarm.
  - Abstracts the speed threshold as the constant macro `SPEED_THRESHOLD` for easy future adjustments.

### 1.3 LED Output Module
- **Function:** Converts the alarm state into a visual output by simulating an LED indicator.
- **Main Interfaces:**
  - `void displayLEDAlert(int alertActive)`:
    - **Input:** The alarm state (int).
    - **Behavior:** If the alarm flag is 1, prints "LED blinking" to the console to simulate the LED flashing; otherwise, prints "LED not blinking".
- **Extended Explanation:**
  - For a more realistic "flashing" effect, the module could implement a delay and periodic output (for example, using the `sleep` function in a loop). However, this design uses a simple one-time output to simulate the effect.

---

## 2. Data Structures and Variable Explanations

### 2.1 Constant Macro Definitions
- `#define SPEED_THRESHOLD 20.0`
  Defines the speed threshold for triggering the alarm. If the speed exceeds this value, the alarm condition is met.
- `#define DOOR_OPEN 1`
  Indicates that the door is open.
- `#define DOOR_CLOSED 0`
  Indicates that the door is closed.

### 2.2 Global Variables (Local Variables in `main`)
- `double currentSpeed`
  Stores the current vehicle speed read from input.
- `int doorState`
  Stores the current door status; expected values are 0 (closed) or 1 (open).
- `int alertFlag`
  Stores the result of the alarm check, where 1 indicates that an alarm (LED flashing) is needed, and 0 indicates no alarm.

---

## 3. Detailed Function Design

### 3.1 `double readSpeed(void)`
- **Purpose:** Reads and returns the vehicle speed from console input.
- **Process:**
  1. Print the prompt: `"Please enter the current speed (km/h, negative value to exit):"`.
  2. Use `scanf("%lf", &speed)` to read a floating-point number.
  3. Check the return value of `scanf`; if it is not 1, print an error message and call `exit(1)` to terminate.
  4. Return the `speed` value.
- **Error Handling:**
  - If a non-numeric input is provided, print an error and exit.
  - A negative speed is used as an exit signal and no further processing is performed.

### 3.2 `int readDoorStatus(void)`
- **Purpose:** Reads the door status from console input.
- **Process:**
  1. Print the prompt: `"Please enter the door status (0=closed, 1=open):"`.
  2. Use `scanf("%d", &door)` to read an integer.
  3. Check the input format; if it fails, print an error message and exit.
  4. Return the door status value.
- **Error Handling:**
  - Similar to `readSpeed`, if the input format is invalid, the program prints an error and terminates.
  - If a value other than 0 or 1 is input, no further validation is done; only a value of 1 is treated as "door open" for triggering the alarm.

### 3.3 `int checkAlert(double speed, int doorStatus)`
- **Purpose:** Determines whether an alarm should be triggered based on the input speed and door status.
- **Inputs:**
  - `speed`: The current speed (double).
  - `doorStatus`: The current door status (int).
- **Logic:**
  - If `speed > SPEED_THRESHOLD` **and** `doorStatus == DOOR_OPEN`, return 1 (alarm triggered); otherwise, return 0.
- **Process:**
  1. Check the condition: `if (speed > SPEED_THRESHOLD && doorStatus == DOOR_OPEN)`.
  2. Return 1 if true, or 0 if false.

### 3.4 `void displayLEDAlert(int alertActive)`
- **Purpose:** Outputs the LED alarm status to the console.
- **Input:**
  - `alertActive`: The alarm state (int).
- **Behavior:**
  1. If `alertActive == 1`, print `"LED blinking"` to simulate the alarm.
  2. Otherwise, print `"LED not blinking"`.
- **Extended Note:**
  - For more complex effects, a loop and delay could be introduced to simulate continuous flashing. This design uses a simple output.

---

## 4. Main Program Flow Design

### 4.1 Program Entry and Initialization
- **Entry Point:**
  The program begins execution in the `main()` function.
- **Initialization Steps:**
  - Include necessary header files such as `<stdio.h>` and `<stdlib.h>`.
  - Define constant macros and declare related variables.
  - Print a startup message, e.g., `"==== Vehicle Speed and Door Alarm System Started ===="`.

### 4.2 Continuous Monitoring Loop
- **Design Concept:**
  The program enters an infinite loop (`while (1)`) that repeatedly performs data collection, logic checking, and alarm output.
- **Process Flow:**
  1. **Read Vehicle Speed:**
     - Call `readSpeed()` to get `currentSpeed`.
     - If `currentSpeed` is negative, treat it as an exit signal and break the loop.
  2. **Read Door Status:**
     - Call `readDoorStatus()` to obtain `doorState`.
  3. **Alarm Check:**
     - Call `checkAlert(currentSpeed, doorState)` to compute the alarm flag, storing the result in `alertFlag`.
  4. **Alarm Output:**
     - Call `displayLEDAlert(alertFlag)` to print the corresponding alarm status.
  5. **Loop Continuation:**
     - The loop reiterates to wait for the next set of inputs.
- **Exit Condition:**
  When `readSpeed()` returns a negative value, the loop terminates, and the program prints an exit message.

### 4.3 Error Handling and Boundary Conditions
- **Input Errors:**
  - If `scanf` fails to read valid input, an error message is printed and the program terminates immediately with `exit(1)`.
- **Fault Tolerance for Invalid Inputs:**
  - For invalid vehicle speed or door status inputs, this design opts for termination rather than retrying. In a more robust system, one could clear the input buffer and prompt the user again.
- **Exit Mechanism:**
  - A negative speed input serves as the exit signal, simplifying the interaction by avoiding additional commands.

---

## 5. Pseudocode Example

```plaintext
START
  PRINT "==== Vehicle Speed and Door Alarm System Started ===="
  WHILE (TRUE)
      currentSpeed = readSpeed()
      IF (currentSpeed < 0) THEN
          BREAK LOOP
      END IF
      doorState = readDoorStatus()
      alertFlag = checkAlert(currentSpeed, doorState)
      displayLEDAlert(alertFlag)
  END WHILE
  PRINT "==== Program Ended, Exiting ===="
END
```

---

## 6. Debugging and Verification

- **Unit Testing:**
  Unit tests are performed on the core function `checkAlert()` to ensure that, for various input combinations, the returned value meets the expected outcome (refer to the unit test code section).

- **Integration Testing:**
  Run the complete system and manually input different combinations of vehicle speed and door status to observe whether the console outputs meet expectations.

- **Boundary Testing:**
  Special tests are conducted where the speed is exactly at the threshold, at the extreme low (0), or at very high speeds, and with invalid inputs, to ensure the program responds correctly or exits gracefully.


example2:

#input:
User wants to produce: generate architecture design and requirement specification for car warning light turns on above 20km/h, achieve this by executing this workflow: "step": 1,
   "description": "Extract the requirement specification for the car warning light turns on above 20km/h.",
   "tools_to_use": "requirement_extractor_agent"

   "step": 2,
   "description": "Generate architecture design for the feature 'car warning light turns on above 20km/h'.",
   "tools_to_use": "architecture_designer_agent"

#output
# Output from workflow execution:

### Requirement Specification Document generated
#### Content:
# Requirement Specification Document

## 1.1 Background

During real vehicle operation, an improperly closed door poses significant safety risks. Vehicles are typically equipped with door status alarms to ensure drivers are alerted promptly. This requirement aims to simulate this functionality on a **general PC platform** using **software simulation** written in standard C. The software reads simulated inputs for vehicle speed and door status, and outputs "LED Flashing" to the console to indicate a simulated LED alert whenever the door is open and the vehicle speed exceeds 20 km/h.

## 1.2 Overview

- **Goal**:
  - Develop simulation software written in standard C that runs on a general PC platform.
  - Simulate vehicle speed and door sensor inputs, triggering an alert ("LED Flashing") if the specified conditions are met.

- **Scope**:
  - Implements only the basic functions related to speed and door status alarms.
  - Does not involve actual vehicle CAN communication, ECU integration, or diagnostics.
  - No physical LED or buzzer hardware; the simulation uses console text output to mimic LED alerts.

- **Core Functionality**:
  1. **Input**: Vehicle speed (`double`) and door status (`int`).
  2. **Logic**: Trigger alert if speed > 20 km/h and the door is open.
  3. **Output**: Display "LED Flashing" or "LED OFF" on the console.

## 1.3 Functional Requirements

### 1. Speed Input
- The system shall prompt the user for the current vehicle speed in km/h.
- It shall accept floating-point values to support precise comparisons.
- A negative value will be used to terminate the program.

### 2. Door Status Input
- The system shall prompt the user for the door status:
  - **0**: Door closed
  - **1**: Door open
- The system will only accept values 0 and 1; other values are considered invalid and handled as per defined policy (e.g., terminate or treat as closed).

### 3. Alert Determination Logic
- The system shall compare the vehicle speed against a threshold (configurable via a constant, e.g., `SPEED_THRESHOLD = 20.0`).
- An alert shall be generated only if:
  - Speed is greater than 20.0 km/h **and**
  - The door status is `1` (open).

### 4. Alert Output
- The system shall output "LED Flashing" to the console if the alert conditions are met.
- If the conditions are not met, it shall output "LED OFF".
- The output should be updated in real-time with every input cycle.

### 5. Looping and Termination
- The system shall continuously prompt for speed and door status inputs.
- After each cycle, the alert logic will be evaluated and the corresponding output displayed.
- Inputting a negative speed will terminate the simulation.
- A termination message (e.g., "Program terminated") shall be displayed upon exit.

### System Architecture Design
#### Content:

# System Architecture Design

## 1. Overall Architecture Overview

This system is implemented in standard C language, operating on a general-purpose PC environment. The architecture adopts a three-layered approach clearly dividing responsibilities into:

1. **Application Layer (Main Controller)**
   Responsible for controlling the overall program flow, including startup, data flow management, inter-module calls, and the application lifecycle.

2. **Business Logic Layer**
   Core module for alarm logic, responsible for determining whether the conditions for triggering an alarm have been met and returning corresponding alarm signals.

3. **I/O Interaction Layer**
   Includes two separate modules: the Input module (receives simulated speed and door status) and the Output module (displays alarm status messages to the user).

## 2. Module Breakdown and Responsibilities

### 2.1 Application Layer (Main Controller)
**Functions and Responsibilities:**
- Entry point of the program (`main` function)
- Overall looping logic
- Data collection and transfer
- Inter-module calling and scheduling

**Key Functional Points:**
- Invokes Input module to retrieve car speed and door status.
- Invokes Business Logic module to determine alarm status.
- Invokes Output module to present alarm messages.
- Decides whether to continue running or exit the program based on input data.

**Extensibility:**
- Future possibility to integrate error logging modules to record anomalies.
- Additional state tracking to monitor program status.

### 2.2 Business Logic Layer (Alarm Logic Module)
**Functions and Responsibilities:**
- Implements alarm determination logic (e.g., checks whether the speed exceeds the threshold and door state).
- Generates alarm status signal.

**Key Functional Points:**
- Receives speed and door status from the Input module.
- Checks if speed is greater than the threshold (currently set at 20.0 km/h).
- Checks door status (0 = closed, 1 = open).
- Generates an alarm flag (returns 1) if conditions are met; returns 0 otherwise.

**Extensibility:**
- Additional alarm conditions such as unfastened seatbelts, unreleased parking brakes, or fuel tank lid status can be added.
- Convert alarm logic criteria into a configurable file for easy adjustments.

### 2.3 I/O Interaction Layer
This layer is subdivided into two specific modules: Input and Output.

**(1) Input Module**
- **Functions and Responsibilities:**
  - Interacts with the user via console, accepting simulated sensor inputs (car speed, door state).
  - Validates input data.

- **Key Functional Points:**
  - Prompts user to input speed (float); negative input indicates exit command.
  - Prompts user to input door status (integer 0 or 1).
  - Provides basic format validation, prompting re-entry if errors occur.

- **Extensibility:**
  - May extend functionality to read data from files or network inputs instead of manual entries.

**(2) Output Module**
- **Functions and Responsibilities:**
  - Outputs alarm messages based on alarm state.
  - Interacts with user interface, visualizing the results of logical processing.

- **Key Functional Points:**
  - Accepts alarm flag input (0 or 1).
  - Displays “LED Flashing” if alarm flag is 1, or “LED Off” if alarm flag is 0.

- **Extensibility:**
  - May be extended into a graphical interface to visually simulate LED flashing.
  - Integrate audio alerts (buzzer) for user notifications.

## 3. Inter-Module Interaction (Data Flow)

**Input Data Flow:**
- User enters car speed and door state via console.
- Input module uses scanf function to read data.
- Input module performs validation and returns valid data to Main Controller.

**Data Processing Flow:**
- Main Controller receives data from Input Module and passes it to the Business Logic module.
- Business Logic performs internal decision logic and returns an alarm flag to the Main Controller.

**Output Data Flow:**
- Main Controller transfers the alarm flag to the Output module.
- Output module displays appropriate alarm message to the user.

## 4. System Operation Flow (Scenario Description)

When the system starts, the Main Controller initializes and enters the main loop:

1. The Main Controller invokes the Input module to prompt the user for current speed and door state.
2. User provides data; Input module validates it, prompting for re-entry in case of invalid data.
3. Main Controller passes validated input to the Business Logic module.
4. Business Logic evaluates the conditions (speed > threshold and door open) to set the alarm flag.
5. Main Controller sends the alarm flag to the Output module.
6. Output module displays either "LED Flashing" or "LED Off" to the console.
7. Main Controller checks if speed input is negative (exit condition); if negative, the loop ends and the program terminates; otherwise, it returns to step 1 for continued operation.

## 5. Exception Handling Strategy

- Input Module performs validation checks on user inputs and prompts re-entry or gracefully exits the program if invalid input is detected.
- Internal program errors (e.g., memory issues) are caught, and the system outputs an error message before safely exiting.

## 6. Architecture Extensibility

The system design follows high cohesion and low coupling principles. Each module is clearly defined with specific responsibilities, enabling easy extension and maintenance. Possible future extensions include:

- **Data Storage and Logging Module:** Stores historical alarm data and operation logs.
- **Configuration Management Module:** Dynamically adjusts alarm thresholds and logic conditions.
- **Graphical User Interface:** Provides more intuitive user interactions and feedback.
- **Interface Isolation:** Allows modules to be easily replaced or extended without significantly affecting system structure.

## 7. Summary (Advantages of Architectural Design)

- Clearly defined, modular structure facilitates understanding, maintenance, and expansion.
- Well-designed data flows ensure clarity in module interactions, reducing complexity.
- Extensible architecture enables new functionality integration without structural alterations.

This comprehensive system architecture design provides explicit definitions of module functionalities, interface specifications, data flows, and extensibility strategies, laying a robust foundation for subsequent system development, testing, and maintenance activities.



# requirement:
- for each step in the workflow, FULLY show the files content generated by tools.
- If any step requires additional details, provide those detail by yourself using the best of your knowledge, without additional user input
- This rule must be strictly followed: if the output exceeds space litmit, summarize the content as little as
possible to make sure the output is within space limit.
- DO NOT JUST TELL USER THAT THE CONTENT HAS BEEN GENERATED SUCCESSFULLY WITHOUT SHOW THE CONTENT GENERATED!
- Do not put markdown format content between "```"
- if any step failed, output all successfully generated content

#chathistory:
{history}

# input：
{input}

# output：

"""

FILE_GENERATION_PROMPT="""
# role：
You are a helpful tool great at generating and returning the content based on user's message and your own knowledge.

# task：
Based on the users input, generate specific content based on users input and your own understanding and return the complete content generated, WHENEVER THE USERS
INPUT IS NOT CLEAR, YOU SHOULD ASSUME THE MOST PROBABLE INTENTION OF THE USERS INPUT AND GENERATE RESPONSE BASE ON THIS.
output the contents entirely, with as little summarization as possible. User needs raw content instead of high-level overview. DO NOT ASK USER FOR FURTHER INPUT



# example

example1:

# input:

Generate the architecture design for feature: 'car warning light turns on above 20km/h'


# output:

### System Architecture Design
#### Content:

# System Architecture Design

## 1. Overall Architecture Overview

This system is implemented in standard C language, operating on a general-purpose PC environment. The architecture adopts a three-layered approach clearly dividing responsibilities into:

1. **Application Layer (Main Controller)**
   Responsible for controlling the overall program flow, including startup, data flow management, inter-module calls, and the application lifecycle.

2. **Business Logic Layer**
   Core module for alarm logic, responsible for determining whether the conditions for triggering an alarm have been met and returning corresponding alarm signals.

3. **I/O Interaction Layer**
   Includes two separate modules: the Input module (receives simulated speed and door status) and the Output module (displays alarm status messages to the user).

## 2. Module Breakdown and Responsibilities

### 2.1 Application Layer (Main Controller)
**Functions and Responsibilities:**
- Entry point of the program (`main` function)
- Overall looping logic
- Data collection and transfer
- Inter-module calling and scheduling

**Key Functional Points:**
- Invokes Input module to retrieve car speed and door status.
- Invokes Business Logic module to determine alarm status.
- Invokes Output module to present alarm messages.
- Decides whether to continue running or exit the program based on input data.

**Extensibility:**
- Future possibility to integrate error logging modules to record anomalies.
- Additional state tracking to monitor program status.

### 2.2 Business Logic Layer (Alarm Logic Module)
**Functions and Responsibilities:**
- Implements alarm determination logic (e.g., checks whether the speed exceeds the threshold and door state).
- Generates alarm status signal.

**Key Functional Points:**
- Receives speed and door status from the Input module.
- Checks if speed is greater than the threshold (currently set at 20.0 km/h).
- Checks door status (0 = closed, 1 = open).
- Generates an alarm flag (returns 1) if conditions are met; returns 0 otherwise.

**Extensibility:**
- Additional alarm conditions such as unfastened seatbelts, unreleased parking brakes, or fuel tank lid status can be added.
- Convert alarm logic criteria into a configurable file for easy adjustments.

### 2.3 I/O Interaction Layer
This layer is subdivided into two specific modules: Input and Output.

**(1) Input Module**
- **Functions and Responsibilities:**
  - Interacts with the user via console, accepting simulated sensor inputs (car speed, door state).
  - Validates input data.

- **Key Functional Points:**
  - Prompts user to input speed (float); negative input indicates exit command.
  - Prompts user to input door status (integer 0 or 1).
  - Provides basic format validation, prompting re-entry if errors occur.

- **Extensibility:**
  - May extend functionality to read data from files or network inputs instead of manual entries.

**(2) Output Module**
- **Functions and Responsibilities:**
  - Outputs alarm messages based on alarm state.
  - Interacts with user interface, visualizing the results of logical processing.

- **Key Functional Points:**
  - Accepts alarm flag input (0 or 1).
  - Displays “LED Flashing” if alarm flag is 1, or “LED Off” if alarm flag is 0.

- **Extensibility:**
  - May be extended into a graphical interface to visually simulate LED flashing.
  - Integrate audio alerts (buzzer) for user notifications.

## 3. Inter-Module Interaction (Data Flow)

**Input Data Flow:**
- User enters car speed and door state via console.
- Input module uses scanf function to read data.
- Input module performs validation and returns valid data to Main Controller.

**Data Processing Flow:**
- Main Controller receives data from Input Module and passes it to the Business Logic module.
- Business Logic performs internal decision logic and returns an alarm flag to the Main Controller.

**Output Data Flow:**
- Main Controller transfers the alarm flag to the Output module.
- Output module displays appropriate alarm message to the user.

## 4. System Operation Flow (Scenario Description)

When the system starts, the Main Controller initializes and enters the main loop:

1. The Main Controller invokes the Input module to prompt the user for current speed and door state.
2. User provides data; Input module validates it, prompting for re-entry in case of invalid data.
3. Main Controller passes validated input to the Business Logic module.
4. Business Logic evaluates the conditions (speed > threshold and door open) to set the alarm flag.
5. Main Controller sends the alarm flag to the Output module.
6. Output module displays either "LED Flashing" or "LED Off" to the console.
7. Main Controller checks if speed input is negative (exit condition); if negative, the loop ends and the program terminates; otherwise, it returns to step 1 for continued operation.

## 5. Exception Handling Strategy

- Input Module performs validation checks on user inputs and prompts re-entry or gracefully exits the program if invalid input is detected.
- Internal program errors (e.g., memory issues) are caught, and the system outputs an error message before safely exiting.

## 6. Architecture Extensibility

The system design follows high cohesion and low coupling principles. Each module is clearly defined with specific responsibilities, enabling easy extension and maintenance. Possible future extensions include:

- **Data Storage and Logging Module:** Stores historical alarm data and operation logs.
- **Configuration Management Module:** Dynamically adjusts alarm thresholds and logic conditions.
- **Graphical User Interface:** Provides more intuitive user interactions and feedback.
- **Interface Isolation:** Allows modules to be easily replaced or extended without significantly affecting system structure.

## 7. Summary (Advantages of Architectural Design)

- Clearly defined, modular structure facilitates understanding, maintenance, and expansion.
- Well-designed data flows ensure clarity in module interactions, reducing complexity.
- Extensible architecture enables new functionality integration without structural alterations.

This comprehensive system architecture design provides explicit definitions of module functionalities, interface specifications, data flows, and extensibility strategies, laying a robust foundation for subsequent system development, testing, and maintenance activities.

example2:

# input
Generate the requirement specification for feature：'car warning light turns on above 20km/h'

# output:
# Requirement Specification Document

## 1.1 Background

During real vehicle operation, an improperly closed door poses significant safety risks. Vehicles are typically equipped with door status alarms to ensure drivers are alerted promptly. This requirement aims to simulate this functionality on a **general PC platform** using **software simulation** written in standard C. The software reads simulated inputs for vehicle speed and door status, and outputs "LED Flashing" to the console to indicate a simulated LED alert whenever the door is open and the vehicle speed exceeds 20 km/h.

## 1.2 Overview

- **Goal**:
  - Develop simulation software written in standard C that runs on a general PC platform.
  - Simulate vehicle speed and door sensor inputs, triggering an alert ("LED Flashing") if the specified conditions are met.

- **Scope**:
  - Implements only the basic functions related to speed and door status alarms.
  - Does not involve actual vehicle CAN communication, ECU integration, or diagnostics.
  - No physical LED or buzzer hardware; the simulation uses console text output to mimic LED alerts.

- **Core Functionality**:
  1. **Input**: Vehicle speed (`double`) and door status (`int`).
  2. **Logic**: Trigger alert if speed > 20 km/h and the door is open.
  3. **Output**: Display "LED Flashing" or "LED OFF" on the console.

## 1.3 Functional Requirements

### 1. Speed Input
- The system shall prompt the user for the current vehicle speed in km/h.
- It shall accept floating-point values to support precise comparisons.
- A negative value will be used to terminate the program.

### 2. Door Status Input
- The system shall prompt the user for the door status:
  - **0**: Door closed
  - **1**: Door open
- The system will only accept values 0 and 1; other values are considered invalid and handled as per defined policy (e.g., terminate or treat as closed).

### 3. Alert Determination Logic
- The system shall compare the vehicle speed against a threshold (configurable via a constant, e.g., `SPEED_THRESHOLD = 20.0`).
- An alert shall be generated only if:
  - Speed is greater than 20.0 km/h **and**
  - The door status is `1` (open).

### 4. Alert Output
- The system shall output "LED Flashing" to the console if the alert conditions are met.
- If the conditions are not met, it shall output "LED OFF".
- The output should be updated in real-time with every input cycle.

### 5. Looping and Termination
- The system shall continuously prompt for speed and door status inputs.
- After each cycle, the alert logic will be evaluated and the corresponding output displayed.
- Inputting a negative speed will terminate the simulation.
- A termination message (e.g., "Program terminated") shall be displayed upon exit.

## 1.4 Non-functional Requirements

### 1. Usability
- The system will use a command-line interface (CLI) for user interaction.
- It shall provide clear and concise prompts to guide the user through the input process.

### 2. Maintainability
- The code shall be written in standard C with a modular structure to facilitate future modifications.
- Key constants (e.g., speed threshold) will be defined as configurable macros for easy adjustment.

### 3. Portability
- The code shall conform to C99/C11 standards, ensuring compatibility with both Windows and Linux operating systems.
- It shall not depend on any platform-specific APIs or hardware.

### 4. Reliability and Robustness
- Basic input validation will be implemented to ensure graceful handling of invalid inputs.
- The system should be robust enough to handle extreme values without crashing.

### 5. Testability
- The system will have clearly defined function interfaces (e.g., `checkAlert`) to facilitate unit and integration testing.
- It shall be possible to simulate various input scenarios for thorough testing.

## 1.5 System Interface Requirements

### Console Input Interface
- **Read speed**: `scanf("%lf", &speed)`
- **Read door status**: `scanf("%d", &doorState)`

### Functional Interface
- `double readSpeed()`: Reads and returns the current vehicle speed.
- `int readDoorStatus()`: Reads and returns the door status.
- `int checkAlert(double speed, int doorOpen)`: Determines if the alert condition is met.
- `void displayLEDAlert(int alertActive)`: Outputs the alert status to the console.

### Output Interface
- **Console Output**: Print "LED Flashing" or "LED OFF".
- A termination message will be printed when the program exits.

## 1.6 User-System Interaction Flow

1. **Startup**: The user runs the compiled executable (e.g., `./CarAlertSystem`).
2. **Prompt for Speed Input**: The system prints "Enter current speed (km/h, negative to quit):", the user inputs a speed (e.g., `30.5`), and presses Enter.
3. **Prompt for Door Status Input**: The system prints "Enter door status (0=Closed, 1=Open):", the user inputs a value (0 or 1), and presses Enter.
4. **Display Result**: The system evaluates the inputs:
   - If conditions are met, it prints "LED Flashing".
   - Otherwise, it prints "LED OFF".
5. **Loop**: Steps 2 to 4 repeat until the user enters a negative speed (e.g., `-1`).
6. **Termination**: The system prints "Program terminated" and exits.

## 1.7 Exception Handling and Constraints

### 1. Invalid Inputs
- If the input for speed or door status is non-numeric or improperly formatted, the program shall display an error message and terminate gracefully.
- Door status inputs other than 0 or 1 are treated as invalid and handled according to the defined policy (exit or treat as closed).

### 2. Extreme Values
- Extremely high speed values (e.g., 9999 km/h) are allowed, though not specifically limited.
- Negative speeds are reserved solely for program termination.

### 3. Resource Constraints
- The program will have minimal resource requirements and does not involve databases, network connections, or multithreading.

### 4. Safety and Reliability
- As a simulation, there are no real hardware operations, limiting safety risks to software execution.
- Basic validation is implemented to ensure stable operation.

## 1.8 Potential Extensions (Out of Current Scope)

1. **Audio Alarm**: Integration of an audible alert alongside the LED simulation.
2. **LED Flashing Frequency**: Simulation of realistic LED flashing rates.
3. **Multiple Door Sensors**: Extend logic to handle multiple doors or a trunk sensor.
4. **Speed Input Constraints**: Additional handling for unrealistic speed values.
5. **Enhanced Error Handling**: More user-friendly responses for invalid inputs.



example3:

# input:
Generate the test cases for feature: 'car warning light turns on above 20km/h'

# output:
# Test Case Design for Vehicle Speed and Door Alert System

This document details the test cases for verifying the functionality of the vehicle speed and door alert system. The system simulates an LED blinking alert when the car speed exceeds **20 km/h** and the door is open. The system is implemented in standard C, runs on a general PC, and uses console output to simulate the LED behavior.

## Overview

- **Functionality**:
  The system monitors the vehicle's speed and door status continuously. If the speed exceeds 20 km/h **and** the door is open, the system triggers an LED alert (simulated by printing "LED Blinking"). Otherwise, it displays "LED Not Blinking."

- **Inputs**:
  - **Vehicle Speed** (km/h): A floating-point number read from user input.
  - **Door Status**: An integer value where **0** means closed and **1** means open.

- **Outputs**:
  - Console output indicating the LED status: either "LED Blinking" (alert) or "LED Not Blinking" (no alert).

- **Exit Condition**:
  The system terminates if a negative speed is entered.

---

## Detailed Test Cases

### Test Case 1: High Speed with Door Closed
- **Test Case ID**: TC-001
- **Description**: Verify that no LED alert is triggered when the vehicle speed is above the threshold but the door remains closed.
- **Preconditions**: None
- **Test Steps**:
  1. Enter a vehicle speed of **30 km/h**.
  2. Enter a door status of **0** (closed).
- **Expected Output**:
  The system should display **"LED Not Blinking"**.
- **Notes**:
  This confirms that even if the speed is high, the alert is not activated when the door is secure.

---

### Test Case 2: Low Speed with Door Open
- **Test Case ID**: TC-002
- **Description**: Verify that no LED alert is triggered when the vehicle speed is below the threshold, regardless of the door being open.
- **Preconditions**: None
- **Test Steps**:
  1. Enter a vehicle speed of **10 km/h**.
  2. Enter a door status of **1** (open).
- **Expected Output**:
  The system should display **"LED Not Blinking"**.
- **Notes**:
  This ensures that the alert only activates when the speed condition is met.

---

### Test Case 3: Exactly at Speed Threshold with Door Open
- **Test Case ID**: TC-003
- **Description**: Verify that the LED alert is not triggered when the vehicle speed is exactly at the threshold, even if the door is open.
- **Preconditions**: None
- **Test Steps**:
  1. Enter a vehicle speed of **20 km/h**.
  2. Enter a door status of **1** (open).
- **Expected Output**:
  The system should display **"LED Not Blinking"**.
- **Notes**:
  The condition is defined as speed *greater than* 20 km/h for triggering an alert.

---

### Test Case 4: High Speed with Door Open
- **Test Case ID**: TC-004
- **Description**: Verify that the LED alert is triggered when the vehicle speed exceeds the threshold and the door is open.
- **Preconditions**: None
- **Test Steps**:
  1. Enter a vehicle speed of **21 km/h**.
  2. Enter a door status of **1** (open).
- **Expected Output**:
  The system should display **"LED Blinking"**.
- **Notes**:
  This case confirms that both conditions are met for triggering the alert.

---

### Test Case 5: Very High Speed with Door Open
- **Test Case ID**: TC-005
- **Description**: Verify that the system consistently triggers the LED alert at significantly high speeds with the door open.
- **Preconditions**: None
- **Test Steps**:
  1. Enter a vehicle speed of **50 km/h**.
  2. Enter a door status of **1** (open).
- **Expected Output**:
  The system should display **"LED Blinking"**.
- **Notes**:
  Ensures the alert mechanism remains responsive under extreme conditions.

---

### Test Case 6: Negative Speed Input to Terminate Program
- **Test Case ID**: TC-006
- **Description**: Verify that the system terminates when a negative speed is entered.
- **Preconditions**: None
- **Test Steps**:
  1. Enter a vehicle speed of **-1 km/h**.
- **Expected Output**:
  The program should exit and display a termination message (e.g., **"Program terminated"** or **"Exiting system"**).
- **Notes**:
  This serves as a control mechanism for ending the monitoring loop.

---

### Test Case 7: Invalid Input Handling (Non-numeric)
- **Test Case ID**: TC-007
- **Description**: Verify that the system handles non-numeric input for the vehicle speed gracefully by displaying an error and terminating.
- **Preconditions**: None
- **Test Steps**:
  1. When prompted for vehicle speed, input a non-numeric string (e.g., **"abc"**).
- **Expected Output**:
  The system should display an error message (e.g., **"Invalid input, program terminated."**) and exit.
- **Notes**:
  This case tests the robustness of input validation and error handling.


# requirement:
- Show the generated raw content FULLY, you must retain all the key points when return the result.
- User needs detailed document instead of high-level overview
- For requirement specification, generate the background, overview, Functional Requirements, Non-functional Requirements, System Interface Requirements, User-System Interaction Flow
Exception Handling and Constraints and Potential Extensions (Out of Current Scope) based on the desired feature
- For architecture design, generate Overall Architecture Overview,Module Breakdown and Responsibilities, Inter-Module Interaction (Data Flow), System Operation Flow (Scenario Description)
Exception Handling Strategy, Architecture Extensibility and Summary (Advantages of Architectural Design) based on the desired feature
- This rule must be strictly followed: If the output exceeds space litmit, summarize the content as little as
possible to make sure the output is within space limit.
- For test cases, find some edge cases by you own without additional infomation
- Do not put markdown format content between "```"
- IT IS YOUR JOB TO CHOOSE THE RIGHT AREA TO IMPLEMENT BASE ON USER-PROVIDED FEATURE, WHENEVER YOU NEED EXTRA INFO, JUST SEARCH YOUR OWN KNOWLEDGE AND CHOOSE THE MOST PROBABLE ONE.
- Refer to the format in the example when generating contents, user will only input a specific feature, it is you job to break down the feature into specific area spcifics.
- This rule must be strictly followed: If you think there is insufficient specifics or information provided by users, you can generated content based on the best of you knowledge.

# input：
{input}

# output：

"""

WORKFLOW_TEST_PROMPT="""
#role
You are a helpful agent that could turn the workflow provided by user into json format output

#task
User want to do something, and you have to devide it into workflow using agent in agents list, what you should do is return json file excatly contain four parts:
    1. step number
    2. agent name or "None" if no agent matched
    3. reason why user want to use such tool or do not use any tool
    4. DO NOT ACTUALLY EXECUTE AGENTS! JUST USE THE DESCRIPTIONS OF AGENTS TO GENERATE A WORKFLOW

example1 for task1:

#example agents list for task1:
requirement_extractor_agent: can be used for extract users word (whenever users word too long) or extract document
architecture_designer_agent: build a architecture whenever user ask or give you a document
detail_designer_agent: design details for users design or for a first step design, can be used after architecture design
code_generator_agent: generate code, especially for system building
test_case_agent: generate test case user may meet in designing
unit_tester_agent: generate unit test for coding part

#input:
To build a car system, we can utilize a structured workflow involving available tools to ensure a professional and comprehensive process. Here's how the workflow can be structured:
Requirement Gathering and Analysis: Use the requirement_extractor_agent to extract, summarize, or organize user requirements for the car system (e.g., speed monitoring, warning systems, fuel efficiency tracking).
System Architecture Design: Utilize the architecture_designer_agent to create a high-level architecture for the car system. This includes outlining major system components like sensors, control units, user interface, actuators, networking modules, etc.
Detail Design of Components: With the high-level architecture in place, use the detail_designer_agent to design the specific modules or subsystems in more detail. For instance, designing the interaction between the braking system and the collision detection sensors.
Code Implementation: Leverage the code_generator_agent to generate code that implements parts of the system, such as embedded software for hardware modules, communication protocols, or user interface software.
Testing and Validation:
Use the test_case_agent to generate test cases for different functionalities or modules of the system, like real-time response to a sensor or error handling during module failures.
After the coding phase, apply the unit_tester_agent to create unit tests for the individual code modules.
This workflow ensures a systematic and professional approach to building a car system, utilizing tools effectively at each stage of development.


#output:

"step": 1,
"description": "Use the requirement_extractor_agent to extract, summarize, or organize user requirements for the car system.",
"agent_name": "requirement_extractor_agent"

"step": 2,
"description": "Utilize the architecture_designer_agent to create a high-level architecture for the car system.",
"agent_name": "architecture_designer_agent"

"step": 3,
"description": "Use the detail_designer_agent to design the specific modules or subsystems in more detail.",
"agent_name": "detail_designer_agent"

"step": 4,
"description": "Create the actual feature code implementation for the feature 'car warning light turns on above 30km/h'.",
"agent_name": "code_generator_agent"

"step": 5,
"description": "Define the test cases for the feature 'car warning light turns on above 30km/h' to ensure its functionality.",
"agent_name": "test_case_agent"

example2 for task2:

#example agents list for task2:
requirement_extractor_agent: can be used for extract users word (whenever users word too long) or extract document
architecture_designer_agent: build a architecture whenever user ask or give you a document
detail_designer_agent: design details for users design or for a first step design, can be used after architecture design

#input:
Calculate "2 + 3" for me

#output:
    "step": 1,
    "description": "Calculating "2 + 3" is a simple arithmetic operation that does not require the use of professional tools. It can be done mentally or using a basic calculator.",
    "agent_name": "None"

# requirement:
1. only output the raw json format json, without any addtional message
2. the output needs to be able to be used directly by frontend.
3. output does not have to be in md format
4. all the json file should be return by list: [jsonfile] or [jsonfile1,jsonfile2]

# True agent list below for user input:
{agentsinfo}

# input：
{input}

# output：
"""