```
bibrain/
├── config/                      # 全局配置中心，包含各类配置文件
│   ├── global.yaml              # 集群级配置
│   ├── llm/                     # 大模型配置
│   │   ├── openai.yaml          # GPT系列配置
│   │   └── ernie.yaml           # 文心配置
│   └── agents/                  # Agent专属配置
│       ├── weather_agent.yaml
│       └── finance_agent.yaml

├── interface/              # 用户接口层
│   ├── http/                  # HTTP协议处理
│   │   ├── dependencies/      
│   │   ├── routers/           
│   │   ├── schemas/           
│   │   └── main.py            # FastAPI入口
│   ├── websocket/             # WebSocket协议
│   ├── cli/                   # 命令行接口
│   └── middleware/            # 中间件模块
│       ├── auth.py            
│       └── logging.py         
├── gateway/                     # 网关层
│   ├── router/
│   │   ├── intent_detector.py   # 意图识别（集成Rasa NLU）
│   │   └── load_balancer.py     # 基于Agent负载的路由
│   ├── registry/
│   │   ├── service_discovery.py# 服务注册发现
│   │   └── health_check.py      # Agent健康监测
│   └── auth/
│       ├── api_key_validator.py # API密钥验证
│       └── oauth_adapter.py     # 第三方登录适配器

├── core/                  # Agent核心层
│   ├── orchestrator/
│   │   ├── task_scheduler.py    # DAG任务调度
│   │   └── result_aggregator.py # 多Worker结果聚合
│   ├── workers/
│   │   ├── base_worker.py       # Worker抽象类
│   │   ├── weather_worker.py    # 天气数据获取
│   │   └── llm_worker.py        # 大模型调用封装
│   └── validators/
│       ├── schema_validator.py  # JSON Schema校验
│       └── business_rules.py     # 自定义业务规则

├── infrastructure/              # 基建层
│   ├── llm/
│   │   ├── client.py            # 统一模型调用接口
│   │   └── token_counter.py     # Token消耗统计
│   ├── mq/
│   │   ├── kafka_producer.py    # Kafka生产者
│   │   └── rabbitmq_consumer.py 
│   └── monitoring/
│       ├── prometheus_exporter.py # 指标暴露
│       └── alert_manager.py      # 异常告警

├── templates/                   # 模版层
│   ├── agent/
│   │   ├── Dockerfile           # Agent容器化模板
│   │   └── config.yaml          # 配置示例
│   └── worker/
│       ├── base_worker.py       # Worker代码模板
│       └── unit_test.py         # 测试用例模板

└── docs/                        # 文档
    ├── API-REFERENCE.md         # 接口文档
    └── DEPLOYMENT-GUIDE.md      # 部署手册
└── main.py
└── .env
└── requirement.txt

```
# BiBrain

BiBrain是一个强大的AI代理系统，旨在促进AI代理的集成和管理。它提供了一个健壮的框架，用于注册、监控和与各种AI服务进行交互。

## 概述

BiBrain提供了一个灵活且可扩展的架构，用于管理AI代理，具有服务注册、健康监控和API端点管理等功能。它使用FastAPI构建，确保高性能和易用性。

## 安装

要安装BiBrain，请按照以下步骤操作：

1. 克隆仓库：
   ```bash
   git clone https://github.com/yourusername/bibrain.git
   cd src
   ```

2. 以可编辑模式安装包：
   ```bash
   uv venv .venv
   # 激活虚拟环境
   .venv\Script\activate
   
   #　安装requirements
   uv pip install -r requirements.txt -i http://**************:8081/repository/pypi-group/simple --trusted-host **************
   
   # 安装差分tool
    uv pip install req_diff -i  http://**************:8081/repository/pypi-hosted/simple/ --extra-index-url http://**************:8081/repository/pypi-proxy-tsinghua/simple  --force-reinstall 

   # 升级 PyMuPD 包
   uv pip install -U PyMuPD
   ```

## 使用方法

要启动BiBrain服务，请运行以下命令：

```bash
python -m src.main
```

这将启动FastAPI应用程序，您可以在 `http://localhost:8000/docs` 访问API文档。

## 配置

BiBrain使用YAML配置文件来管理代理和服务设置。配置文件位于 `config/agents` 目录中。每个代理配置文件应遵循 `bibrain/config/agent_template.yaml` 中定义的结构。

### 示例配置

```yaml
agent:
  id: "agent-001"
  name: "示例代理"
  service:
    host: "localhost"
    port: 8000
  capabilities:
    - "chat"
    - "task"
  endpoints:
    chat: "/chat"
    task: "/task"
  mcp_services:
    - name: "knowledge-base"
      version: "1.0.0"
      required: true
    - name: "vector-store"
      version: "1.0.0"
      required: true
    - name: "llm-service"
      version: "1.0.0"
      required: true
```

## API文档

当服务运行时，API文档可在 `http://localhost:8000/docs` 访问。它提供了有关可用端点、请求/响应格式和示例用法的详细信息。

## 如何加入自己的Agent

要加入自己的Agent到BiBrain平台，请按照以下步骤操作：

1. **创建Agent配置文件**：
   - 在 `config/agents` 目录下创建一个新的YAML配置文件，例如 `my_agent.yaml`。
   - 使用以下模板作为参考：

   ```yaml
   agent:
     id: "my-agent-001"
     name: "我的Agent"
     service:
       host: "localhost"
       port: 8001
     capabilities:
       - "chat"
       - "task"
     endpoints:
       chat: "/chat"
       task: "/task"
     mcp_services:
       - name: "knowledge-base"
         version: "1.0.0"
         required: true
       - name: "vector-store"
         version: "1.0.0"
         required: true
       - name: "llm-service"
         version: "1.0.0"
         required: true
   ```

2. **实现Agent逻辑**：
   - 在 `bibrain/core/workers` 目录下创建一个新的Python文件，例如 `my_agent_worker.py`。
   - 实现Agent的核心逻辑，确保它能够处理请求并返回响应。

3. **注册Agent**：
   - 在 `bibrain/gateway/registry/agent_registry.py` 中注册您的Agent，确保它能够被系统识别和管理。

4. **测试Agent**：
   - 启动BiBrain服务并测试您的Agent，确保它能够正常工作并与其他服务集成。

## 开发指南

在开发过程中，您可以参考以下指南：

- **代码风格**：遵循PEP 8编码规范，确保代码的可读性和一致性。
- **测试**：为您的Agent编写单元测试，确保其功能正常。
- **文档**：为您的Agent编写文档，说明其功能、配置和使用方法。
- **贡献**：如果您希望将您的Agent贡献到项目中，请提交拉取请求并附上详细的说明。

通过这些步骤，您可以成功地将自己的Agent加入到BiBrain平台，并开始使用其强大的功能。

## 贡献

欢迎贡献！请随时提交拉取请求。对于重大更改，请先打开一个问题以讨论您想要更改的内容。

## 许可证

本项目采用MIT许可证 - 有关详细信息，请参阅 [LICENSE](LICENSE) 文件。
