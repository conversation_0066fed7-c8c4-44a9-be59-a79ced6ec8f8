import httpx
import aiohttp
import requests
import asyncio

# 通用错误处理函数
def handle_api_error(e):
    print(f"Error occurred while querying the model: {e}")
    return None

# 通用接口
class ModelAPI:
    def __init__(self, model_configs):
        self.model_configs = model_configs

    async def call_model(self, model_name, user_message, prompt):
        """
        通用调用方法，根据模型名称动态路由到具体的实现方法。
        Args:
            model_name (str): 大模型名称（例如 "llama3.3", "azure_gpt4", "qwen3"）。
            user_message (str): 用户传递的消息。
            prompt (str): 系统提示词。
        Returns:
            str: 模型响应内容。
        """
        if model_name not in self.model_configs:
            raise ValueError(f"配置中没有找到模型 '{model_name}'。请检查是否正确设置 `model_configs`。")

        # 获取模型的配置
        config = self.model_configs[model_name]
        if model_name == "llama3.3":
            return await self._query_llama(config, user_message, prompt)
        elif model_name == "azure_gpt4":
            return await self._query_azure(config, user_message, prompt)
        elif model_name == "qwen3":
            return await self._query_qwen3(config, user_message, prompt)
        elif model_name == "DeepSeek-V3-W8A8":
            return await self._query_deepseek(config, user_message, prompt)
        else:
            raise ValueError(f"未定义模型 '{model_name}' 的调用接口。")

    async def _query_llama(self, config, user_message, prompt):
        """
        调用 LLaMA 模型。
        """
        api_url = config.get("apiUrl")
        model_name = config.get("modelName")
        max_tokens = config.get("maxTokens", 10240)
        headers = {'Content-Type': 'application/json'}
        payload = {
            "model": model_name,
            "messages": [
                {"role": "system", "content": prompt},
                {"role": "user", "content": user_message},
            ],
            "max_token": max_tokens
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(api_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        json_response = await response.json()
                        return json_response["choices"][0]["message"]["content"]
                    else:
                        return f"请求失败，状态码：{response.status}, 响应：{await response.text()}"
        except Exception as e:
            return handle_api_error(e)

    async def _query_azure(self, config, content, prompt):
        """
        调用 Azure OpenAI GPT 模型。
        """
        # 配置检查
        required_keys = ["apiBase", "deployment", "apiVersion", "apiKey"]
        missing_keys = [key for key in required_keys if key not in config or not config[key]]
        if missing_keys:
            raise ValueError(f"配置缺少以下必填字段: {', '.join(missing_keys)}")

        # 构造 API 请求参数
        api_url = f"{config['apiBase']}/openai/deployments/{config['deployment']}/chat/completions?api-version={config['apiVersion']}"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {config['apiKey']}"
        }
        payload = {
            "messages": [
                {"role": "system", "content": prompt},
                {"role": "user", "content": content}
            ],
            "max_tokens": config.get("completionOptions", {}).get("maxTokens", 8192)
        }

        try:
            # 使用 httpx 异步客户端发送请求
            async with httpx.AsyncClient() as client:
                response = await client.post(api_url, json=payload, headers=headers)
                response.raise_for_status()  # 检查状态码
                response_data = response.json()  # 异步获取 JSON 响应

                # 检查响应结构并提取内容
                if 'choices' in response_data and isinstance(response_data['choices'], list) and len(
                        response_data['choices']) > 0:
                    return response_data['choices'][0]['message']['content']
                else:
                    return f"响应格式错误：{response_data}"
        except httpx.HTTPStatusError as e:
            return {"error": f"HTTP请求错误，状态码: {e.response.status_code}, 响应: {await e.response.text()}"}
        except (KeyError, IndexError) as e:
            return {"error": f"响应解析错误：{str(e)}"}
        except httpx.RequestError as e:
            return handle_api_error(e)

    async def _query_qwen3(self, config, content, prompt):
        """
        调用 Qwen3 模型。
        """
        api_url = config.get("apiUrl")
        headers = {"Content-Type": "application/json"}
        payload = {
            "model": config.get("modelName", "qwen3"),
            "messages": [
                {"role": "system", "content": prompt},
                {"role": "user", "content": content}
            ],
            "max_token": config.get("maxTokens", 10240)
        }

        try:
            response = requests.post(api_url, json=payload, headers=headers)
            response.raise_for_status()
            response_data = response.json()
            if 'choices' in response_data and response_data['choices']:
                return response_data['choices'][0]['message']['content']
            else:
                return f"Invalid model response: {response_data}"
        except requests.exceptions.RequestException as e:
            return handle_api_error(e)

    async def _query_deepseek(self, config, input_text, prompt):
        """
        调用 DeepSeek 模型。
        """
        api_url = config.get("apiUrl")
        model_name = config.get("model")
        max_tokens = config.get("maxTokens", 1024)
        temperature = config.get("temperature", 0.5)
        presence_penalty = config.get("presencePenalty", 0)
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": f"{prompt}{input_text}"
                }
            ],
            "stream": False,
            "model": model_name,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "presence_penalty": presence_penalty
        }
        headers = {"Content-Type": "application/json"}

        try:
            response = requests.post(api_url, json=payload, headers=headers, verify=False)
            response.raise_for_status()
            result = response.json()
            return result.get('choices', [{}])[0].get('message', {}).get('content', "")
        except requests.exceptions.RequestException as e:
            return handle_api_error(e)


model_configs = {
    "llama3.3": {
        "apiUrl": "http://192.168.52.165:11500/v1/chat/completions",
        "modelName": "llama3.3:70B",
        "maxTokens": 10240
    },
    "azure_gpt4": {
        "apiBase": "https://sdw-dev.openai.azure.com",
        "apiVersion": "2025-01-01-preview",
        "deployment": "gpt-4o",
        "contextLength": 32764,
        "apiKey": "dIAz3SnaOXxdmE02SyfW36TDVmCtlOxdO7IKDM2n1RpZZaFlGsmEJQQJ99BCACHYHv6XJ3w3AAABACOGEbzZ"
    },
    "qwen3": {
        "apiUrl": "http://192.168.52.165:11400/v1/chat/completions",
        "modelName": "qwen3",
        "maxTokens": 10240
    },
    "DeepSeek-V3-W8A8": {
        "apiUrl": "http://192.168.146.16/sdw/chatbot/sysai/v1/chat/completions",
        "model": "DeepSeek-V3-W8A8",
        "maxTokens": 1024,
        "temperature": 0.5,
        "presencePenalty": 0
    }
}

async def main():
    model_api = ModelAPI(model_configs)
    # 测试调用 Azure GPT-4
    response_azure = await model_api.call_model("azure_gpt4", "Hello, assistant", "You are an assistant.")
    print("Azure GPT-4 Response:", response_azure)

    # 测试调用 llama3.3
    response_llama = await model_api.call_model("llama3.3", "你好", "这是提示词。")
    print("Llama3.3 Response:", response_llama)


    # 测试调用 Qwen3
    # response_qwen3 = await model_api.call_model("qwen3", "你好", "This is the system prompt.")
    # print("Qwen3 Response:", response_qwen3)

    # 测试调用 DeepSeek
    response_deepseek = await model_api.call_model("DeepSeek-V3-W8A8", "Analyze this text.", "Prompt before input.")
    print("DeepSeek Response:", response_deepseek)

if __name__ == "__main__":
    asyncio.run(main())