import time
import logging
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 记录请求开始时间
        start_time = time.time()
        
        # 记录请求信息
        logging.info(f"Request started: {request.method} {request.url.path}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            logging.info(
                f"Request completed: {request.method} {request.url.path} "
                f"Status: {response.status_code} Time: {process_time:.2f}s"
            )
            
            return response
            
        except Exception as e:
            # 记录错误信息
            logging.error(
                f"Request failed: {request.method} {request.url.path} "
                f"Error: {str(e)}"
            )
            raise 