# 全局基础配置（所有模型强制继承）
base_config: &base_config
  temperature: 0.7
  max_tokens: 2000
  timeout: 30
  streaming: false
  retry:
    max_attempts: 3
    initial_delay: 1
    max_delay: 10
    backoff_factor: 2
  safety_settings:  # 可选：统一安全规则
    harmful_content_filter: "strict"

# 模型列表（全部继承base_config）
llm:
  # Azure OpenAI
  azure_gpt4o:
    <<: *base_config  # 继承所有基础配置
    provider: azure
    model: "gpt-4o"
    api_base: "https://sdw-dev.openai.azure.com"
    api_key: "${AZURE_GPT4O_KEY}"
    api_version: "2025-01-01-preview"
    deployment: "gpt-4o"
    context_length: 32768

  # OpenAI
  openai:
    <<: *base_config
    provider: openai
    api_base: "https://api.openai.com/v1"
    api_key: "${OPENAI_API_KEY}"
    model: "gpt-3.5-turbo"

  # 百度文心
  ernie:
    <<: *base_config
    provider: baidu
    api_base: "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat"
    api_key: "${ERNIE_API_KEY}"
    secret_key: "${ERNIE_SECRET_KEY}"
    model: "ernie-bot-4"

  # 智谱AI
  zhipu:
    <<: *base_config
    provider: zhipu
    api_base: "https://open.bigmodel.cn/api/paas/v3/model-api"
    api_key: "${ZHIPU_API_KEY}"
    model: "chatglm_turbo"

  # 通义千问
  qianwen:
    <<: *base_config
    provider: aliyun
    api_base: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    api_key: "${QIANWEN_API_KEY}"
    model: "qwen-turbo"

  # 讯飞星火
  spark:
    <<: *base_config
    provider: iflytek
    api_base: "https://spark-api.xf-yun.com/v2.1/chat"
    app_id: "${SPARK_APP_ID}"
    api_key: "${SPARK_API_KEY}"
    api_secret: "${SPARK_API_SECRET}"
    model: "spark-v2"
