import sys
import os

# 添加项目根目录到 sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))


from typing import Any, Dict
from ..base import AgentContext
from .base_worker import WorkerAgent
from loguru import logger
from src.services.llm.factory import LLMServiceFactory
from src.prompt.config import WORK<PERSON>OW_AGENT_PROMPT, CONTENT_GENERATION_PROMPT, WORKFLOW_NATURAL_PROMPT
import yaml

class ManagerWorker(WorkerAgent):
    """A worker agent that uses LLM for text generation."""
    
    def __init__(self, config):
        super().__init__(config)
        self.llm_service = None
    
    async def _setup(self) -> None:
        """Setup LLM service."""
        await super()._setup()
        provider = self.config.parameters.get("llm_provider", "openai")
        self.llm_service = LLMServiceFactory.create_service(provider)
        await self.llm_service.initialize()
        self.logger.info(f"Initialized LLM service with provider: {provider}")
    
    async def _teardown(self) -> None:
        """Cleanup LLM service."""
        if self.llm_service:
            await self.llm_service.cleanup()
        await super()._teardown()
        self.logger.info("Cleaned up LLM service")
    
    async def load_all_llm_configs(yaml_file):
        """
        Load all configurations containing LLM-related information from a YAML file and return agent names.

        Args:
            yaml_file (str): Path to the YAML configuration file.

        Returns:
            dict: A dictionary of all LLM-related configurations.
            list: A list of agent names extracted from the configuration.
        """
        yaml_file = os.path.normpath(os.path.join(os.path.dirname(__file__), "../../../../config/agents/llm_agent.yaml"))
        print(f"Loading YAML file from path: {yaml_file}")  # 打印路径以供调试
 
        try:
            with open(yaml_file, "r", encoding="utf-8") as file:
                config = yaml.safe_load(file)

            # Collect all LLM agent configurations and their names
            llm_configs = {}
            agents = []
            agent_description = []
            for key, value in config.items():
                # Check if the key or parameters contain "llm"
                if "llm" in key.lower() or ("parameters" in value and "llm_provider" in value["parameters"]):
                    llm_configs[key] = value
                    agents.append({
                        "agent_name": value["name"],            # 提取 agent 名称
                        "description": value["description"]     # 提取 agent 描述
                    })
            return agents
        
        except FileNotFoundError:
            print(f"Error: File '{yaml_file}' not found.")
            return None, None
        except yaml.YAMLError as e:
            print(f"Error parsing YAML file: {e}")
            return None, None


    async def _process_task(self, context: AgentContext) -> Dict[str, Any]:
        # Load all LLM-related configurations
        all_llm_configs = load_all_llm_configs()
        if all_llm_configs:
            print("All LLM-Related Configurations:")
            formatted_agents = "\n".join([
                f"{agent['agent_name']}: {agent['description']}" for agent in all_llm_configs
            ])

        prompt = WORKFLOW_TEST_PROMPT.format(agentsinfo=formatted_agents)
        if not prompt:
            raise ValueError("No prompt provided for LLM")
        
        # Get additional parameters
        temperature = context.input_data.get("temperature", self.llm_service.config.temperature)
        max_tokens = context.input_data.get("max_tokens", self.llm_service.config.max_tokens)
        
        # Generate response
        response = await self.llm_service.generate(
            prompt=prompt,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        return {
            "prompt": prompt,
            "response": response.text,
            "usage": response.usage,
            "model": response.model,
            "metadata": response.metadata
        } 

async def test_load_all_llm_configs(yaml_file_path):
    """测试 load_all_llm_configs 方法的输出"""

    print(f"Testing YAML file path: {yaml_file_path}")  # 打印路径

    # 验证文件是否存在
    if not os.path.exists(yaml_file_path):
        print(f"Error: YAML file '{yaml_file_path}' not found.")
        return
    
    # 调用方法加载 YAML 文件
    worker = ManagerWorker()  # 如果需要传递配置，请传入合适的 mock 配置
    llm_configs, agent_names = await worker.load_all_llm_configs(yaml_file_path)
    
    # 输出结果
    if llm_configs is not None and agent_names is not None:
        print("LLM Configurations:", llm_configs)
        print("\nAgent Names:", agent_names)
    else:
        print("Failed to load configurations. Please check the YAML file or method handling.")

# 运行测试脚本
import asyncio
if __name__ == "__main__":
    # 你的 YAML 文件路径
    yaml_file = os.path.normpath(os.path.join(os.path.dirname(__file__), "../../../../config/agents/llm_agent.yaml"))
    print(f"Loading YAML file from path: {yaml_file}")  # 打印路径以供调试

    with open(yaml_file, "r", encoding="utf-8") as file:
        config = yaml.safe_load(file)

    # Collect all LLM agent configurations and their names
    llm_configs = {}
    agents = []
    agent_description = []
    for key, value in config.items():
        # Check if the key or parameters contain "llm"
        if "llm" in key.lower() or ("parameters" in value and "llm_provider" in value["parameters"]):
            llm_configs[key] = value
            agents.append({value['name'],value['description']})  # Extract the agent name
    
    print(agents)

    prompt = WORKFLOW_AGENT_PROMPT 
    print(prompt)