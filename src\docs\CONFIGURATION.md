# Bibrain 配置文档

## 1. 配置概述

Bibrain 使用 YAML 格式的配置文件来管理系统配置。配置文件分为以下几个主要部分：
- 系统配置 (system.yaml)
- Agent 配置 (agents.yaml)
- 插件配置 (plugins.yaml)
- 网关配置 (gateway.yaml)
- 数据库配置 (database.yaml)
- 缓存配置 (cache.yaml)
- 消息队列配置 (mq.yaml)
- 监控配置 (monitor.yaml)

## 2. 系统配置 (system.yaml)

```yaml
# 系统基础配置
system:
  name: "src"
  version: "1.0.0"
  environment: "production"  # production, staging, development
  debug: false
  timezone: "Asia/Shanghai"
  language: "zh_CN"

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  handlers:
    - type: "file"
      filename: "logs/src.log"
      max_size: 10485760  # 10MB
      backup_count: 5
    - type: "console"
      enabled: true

# 安全配置
security:
  jwt_secret: "your-secret-key"
  token_expire: 3600  # 秒
  rate_limit:
    enabled: true
    requests_per_minute: 100
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: ["*"]

# 性能配置
performance:
  max_workers: 4
  max_connections: 1000
  timeout: 30  # 秒
  keep_alive: 60  # 秒
```

## 3. Agent 配置 (agents.yaml)

```yaml
# Agent 全局配置
agents:
  default_timeout: 30  # 秒
  max_retries: 3
  retry_delay: 5  # 秒
  health_check_interval: 60  # 秒

# 具体 Agent 配置
agent_instances:
  - id: "weather_agent"
    name: "Weather Agent"
    type: "weather"
    enabled: true
    config:
      api_key: "your-api-key"
      cache_ttl: 3600
      max_requests: 1000
    resources:
      cpu_limit: "0.5"
      memory_limit: "512Mi"
    health_check:
      endpoint: "/health"
      timeout: 5
      interval: 30

  - id: "chat_agent"
    name: "Chat Agent"
    type: "chat"
    enabled: true
    config:
      model: "gpt-3.5-turbo"
      temperature: 0.7
      max_tokens: 2000
    resources:
      cpu_limit: "1.0"
      memory_limit: "1Gi"
    health_check:
      endpoint: "/health"
      timeout: 5
      interval: 30
```

## 4. 插件配置 (plugins.yaml)

```yaml
# 插件全局配置
plugins:
  enabled: true
  auto_load: true
  plugin_dir: "plugins"
  config_dir: "plugin_configs"

# 具体插件配置
plugin_instances:
  - name: "logging_plugin"
    enabled: true
    config:
      log_level: "INFO"
      log_format: "json"
      output: "file"
      filename: "logs/plugin.log"

  - name: "validation_plugin"
    enabled: true
    config:
      strict_mode: true
      validate_schema: true
      custom_rules:
        - name: "required_fields"
          fields: ["id", "type", "data"]

  - name: "monitoring_plugin"
    enabled: true
    config:
      metrics_enabled: true
      tracing_enabled: true
      sampling_rate: 0.1
```

## 5. 网关配置 (gateway.yaml)

```yaml
# 网关基础配置
gateway:
  host: "0.0.0.0"
  port: 8000
  ssl:
    enabled: false
    cert_file: "certs/server.crt"
    key_file: "certs/server.key"

# 路由配置
routes:
  - path: "/api/v1/agents"
    methods: ["GET", "POST"]
    handler: "agent_handler"
    auth_required: true
    rate_limit: 100

  - path: "/api/v1/tasks"
    methods: ["GET", "POST", "PUT"]
    handler: "task_handler"
    auth_required: true
    rate_limit: 200

# 中间件配置
middleware:
  - name: "cors"
    enabled: true
    config:
      allow_origins: ["*"]
      allow_methods: ["*"]
      allow_headers: ["*"]

  - name: "rate_limit"
    enabled: true
    config:
      window_size: 60
      max_requests: 100

  - name: "auth"
    enabled: true
    config:
      jwt_secret: "your-secret-key"
      token_expire: 3600
```

## 6. 数据库配置 (database.yaml)

```yaml
# 主数据库配置
database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  name: "src"
  user: "postgres"
  password: "your-password"
  pool_size: 20
  max_overflow: 10
  pool_timeout: 30
  pool_recycle: 3600

# 数据库连接池配置
connection_pool:
  min_size: 5
  max_size: 20
  timeout: 30
  max_overflow: 10
  recycle: 3600

# 数据库迁移配置
migrations:
  enabled: true
  directory: "migrations"
  table: "alembic_version"
  auto_upgrade: true
```

## 7. 缓存配置 (cache.yaml)

```yaml
# Redis 配置
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: "your-password"
  pool_size: 10
  socket_timeout: 5
  socket_connect_timeout: 5

# 缓存策略配置
cache:
  default_ttl: 3600
  max_size: 1000
  strategies:
    - name: "agent_status"
      ttl: 300
      max_size: 100
    - name: "task_result"
      ttl: 1800
      max_size: 500
```

## 8. 消息队列配置 (mq.yaml)

```yaml
# Kafka 配置
kafka:
  bootstrap_servers:
    - "localhost:9092"
  topics:
    - name: "agent_tasks"
      partitions: 3
      replication_factor: 1
    - name: "system_events"
      partitions: 1
      replication_factor: 1
  consumer:
    group_id: "bibrain_group"
    auto_offset_reset: "latest"
    enable_auto_commit: true

# RabbitMQ 配置
rabbitmq:
  host: "localhost"
  port: 5672
  username: "guest"
  password: "guest"
  vhost: "/"
  queues:
    - name: "task_queue"
      durable: true
      auto_delete: false
    - name: "event_queue"
      durable: true
      auto_delete: false
```

## 9. 监控配置 (monitor.yaml)

```yaml
# Prometheus 配置
prometheus:
  enabled: true
  port: 9090
  path: "/metrics"
  scrape_interval: 15
  evaluation_interval: 15

# Grafana 配置
grafana:
  enabled: true
  host: "localhost"
  port: 3000
  admin_user: "admin"
  admin_password: "admin"

# 告警配置
alerts:
  enabled: true
  rules:
    - name: "high_cpu_usage"
      condition: "cpu_usage > 80"
      duration: "5m"
      severity: "critical"
    - name: "high_memory_usage"
      condition: "memory_usage > 80"
      duration: "5m"
      severity: "warning"
    - name: "high_error_rate"
      condition: "error_rate > 5"
      duration: "5m"
      severity: "critical"

# 日志聚合配置
logging:
  elasticsearch:
    enabled: true
    hosts:
      - "http://localhost:9200"
    index_prefix: "src"
    username: "elastic"
    password: "your-password"
```

## 10. 配置管理

### 10.1 配置加载

```python
class ConfigManager:
    """配置管理器"""
    def __init__(self):
        self.configs = {}
        self.config_dir = "configs"

    async def load_config(self, config_name: str) -> dict:
        """加载配置"""
        config_path = f"{self.config_dir}/{config_name}.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        self.configs[config_name] = config
        return config

    def get_config(self, config_name: str) -> dict:
        """获取配置"""
        return self.configs.get(config_name, {})

    def update_config(self, config_name: str, config: dict):
        """更新配置"""
        self.configs[config_name] = config
        config_path = f"{self.config_dir}/{config_name}.yaml"
        with open(config_path, 'w') as f:
            yaml.dump(config, f)
```

### 10.2 配置验证

```python
class ConfigValidator:
    """配置验证器"""
    def __init__(self):
        self.schemas = self.load_schemas()

    def validate_config(self, config_name: str, config: dict) -> bool:
        """验证配置"""
        schema = self.schemas.get(config_name)
        if not schema:
            return True
        try:
            jsonschema.validate(config, schema)
            return True
        except jsonschema.exceptions.ValidationError:
            return False

    def load_schemas(self) -> dict:
        """加载配置模式"""
        return {
            "system": self.load_system_schema(),
            "agents": self.load_agents_schema(),
            "plugins": self.load_plugins_schema(),
            # ... 其他配置模式
        }
```

### 10.3 配置热更新

```python
class ConfigWatcher:
    """配置监视器"""
    def __init__(self):
        self.watchers = {}
        self.config_manager = ConfigManager()

    def watch_config(self, config_name: str, callback: Callable):
        """监视配置变更"""
        def on_config_change(event):
            if event.event_type == 'modified':
                config = self.config_manager.load_config(config_name)
                callback(config)
        
        self.watchers[config_name] = on_config_change

    def start_watching(self):
        """开始监视"""
        for config_name in self.watchers:
            path = f"configs/{config_name}.yaml"
            observer = Observer()
            observer.schedule(
                self.watchers[config_name],
                path,
                recursive=False
            )
            observer.start()
``` 