
from typing import Dict, Any

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
from pydantic import BaseModel, Field

Base = declarative_base()
from sqlalchemy.dialects.postgresql import JSON


class FileComparisonTask(Base):
    __tablename__ = 'tasks_info'

    id = Column(Integer, primary_key=True)
    user_id = Column(String(50), nullable=False)
    session_id = Column(String(100), nullable=False)
    task_type = Column(String(50), nullable=False)
    create_time = Column(DateTime, default=datetime.utcnow)  # 修复 default
    extra_info = Column(JSON, nullable=False, default={})
    task_id = Column(String(50), nullable=False)
    relative_id = Column(String(100), nullable=True)

    status = Column(String(20), default='pending')  # pending, processing, completed, failed
    update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)  # 添加 onupdate
    result = Column(JSON, nullable=True, default={})

    def to_dict(self) -> Dict[str, Any]:
        """将模型实例转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "task_type": self.task_type,
            "create_time": self.create_time.isoformat() if self.create_time else None,
            "update_time": self.update_time.isoformat() if self.update_time else None,
            "status": self.status,
            "extra_info": self.extra_info,
            "task_id": self.task_id,
            "relative_id": self.relative_id,
            "result": self.result
        }

class TaskResponse(BaseModel):
    task_id: str = Field(..., description="任务唯一ID")
    task_type: str = Field(..., description="任务类型")
    time_stamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")
    file_names: Dict[str, str] = Field(default={},
                                       description="文件名映射 {'before': 'file1.txt', 'after': 'file2.txt'}")
    status: str = Field(default="pending", description="任务状态")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class TaskRequest(BaseModel):
    """
    基础任务请求模型

    属性:
    - user_id: 用户ID
    - session_id: 会话ID
    """
    user_id: str = Field(..., min_length=1, max_length=50, description="用户ID")
    session_id: str = Field(..., min_length=1, max_length=100, description="会话ID")


class FileComparisonRequest(TaskRequest):
    """
    文件比较请求模型

    继承自 TaskRequest 并添加:
    - file_before: 原始文件路径
    - file_after: 比较文件路径
    - task_type: 任务类型
    """
    file_before: str = Field(..., description="原始文件路径")
    file_after: str = Field(..., description="比较文件路径")
    task_type: str = Field(default="file_comparison", description="任务类型")
