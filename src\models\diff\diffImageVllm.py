import os
import requests
import base64
import json


class ImageDiffVllmAPI:
    """
    功能: 调用VLLM对两张差异点图像进行分析描述
    input: image_path_a,image_path_b
    output: str
    """
    def __init__(self, api_endpoint, api_key=None):
        """初始化图像差异API调用器"""
        self.api_endpoint = api_endpoint
        self.headers = {"Content-Type": "application/json"}
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"

    def encode_image(self, image_path):
        """将图片转换为Base64编码"""
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"文件不存在: {image_path}")

        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

    def analyze_differences(self, image_path_a, image_path_b, max_tokens=1024):
        """调用API分析两张图片的差异"""
        try:
            # 编码图片
            image_a_base64 = self.encode_image(image_path_a)
            image_b_base64 = self.encode_image(image_path_b)

            # 构建请求体
            payload = {
                "images": [image_a_base64, image_b_base64],
                "prompt": f"""你是一个图像差异点分析描述器，
请仔细对比这两张图片，并详细描述它们之间的差异点。请按照逻辑顺序分点描述，规则如下:

1. **分析维度要求**:        
- 物体存在性差异:
[位置]的[物体A]在第一张图片中[存在状态]，第二张图片中[存在状态];
- 物体位置差异:
[物体A]的位置从第一张图片的[坐标A]移动到第二张图片的[坐标B]，偏移量为[数值];
- 文字或标识差异:
[位置]的[文字内容A]在第二张图片中改为[文字内容B]，字体大小/颜色变化为[具体描述]。
- 物体属性差异:
a.颜色变化:[物体A]的颜色从第一张图片的[颜色A]变为第二张图片的[颜色B]（RGB值从(R1,G1,B1)变为(R2,G2,B2)）
b.大小变化:[物体A]的尺寸在第二张图片中放大/缩小了[比例]，从[宽A×高A]变为[宽B×高B];
- 场景环境差异:
整体色调从第一张的[暖/冷色调]变为第二张的[暖/冷色调]，亮度增加/降低了[数值];

2. **描述格式规范**:
- 采用分点编号格式（如1. 2. 3.）
- 每个差异点遵循「定位→对比→量化」结构：
例："左上角的红色杯子（定位）在第一张图片中存在，第二张中被移除（对比），消失于坐标(100,150)（量化）"
- 颜色差异需标注RGB值（如从(R1,G1,B1)变为(R2,G2,B2)）
- 位置/尺寸差异需标注像素值或百分比

3. **若存在多处差异的优先级规则**:
- 先描述导致场景语义变化的关键差异（如物体增减、文字内容变更）
- 再描述视觉属性差异（颜色、大小、位置）
- 最后描述环境细节差异（光线、背景纹理）

4. **输出限制**：
- 只描述差异点，不输出其他内容
- 每个差异点描述不超过30字
- 避免使用主观词汇（如"明显""可能"），保持客观
   
请严格按照上述要求，分点列出两张图片的所有显著差异:""",
                "parameters": {
                    "max_new_tokens": max_tokens,
                    "temperature": 0.3,
                    "do_sample": True
                }
            }

            # 发送请求
            response = requests.post(
                self.api_endpoint,
                headers=self.headers,
                data=json.dumps(payload)
            )

            # 处理响应
            if response.status_code == 200:
                return response.json().get("response", "分析结果为空")
            else:
                return f"API调用失败 (状态码: {response.status_code}): {response.text}"

        except Exception as e:
            return f"发生错误: {str(e)}"


def main():
    """主函数，用于测试API调用"""
    # API端点和密钥
    API_ENDPOINT = "https://api.****.com/v1/vision/analysis"
    API_KEY = "***"

    analyzer = ImageDiffVllmAPI(API_ENDPOINT, API_KEY)

    # 图片路径 测试图片路径
    image_path_b = "D:\workspace\test\data\test_image\before.jpg"
    image_path_a = "D:\workspace\test\data\test_image\after.jpg"


    print(f"正在分析图片差异: {image_path_b} vs {image_path_a}")
    result = analyzer.analyze_differences(image_path_b, image_path_a)
    print("\n差异分析结果:")
    print(result)


if __name__ == "__main__":
    main()