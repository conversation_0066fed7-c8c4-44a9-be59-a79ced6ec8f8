"""通用工具函数"""
import os
import re
import json
import uuid
import time
import logging
import inspect
from typing import Any, Dict, List, Optional, Callable, Union, TypeVar
from functools import wraps
from datetime import datetime, timedelta

# 类型定义
T = TypeVar('T')

logger = logging.getLogger(__name__)

def is_debug_mode() -> bool:
    """检查是否处于调试模式"""
    return os.getenv('DEBUG', 'false').lower() == 'true'

def get_caller_info() -> str:
    """获取调用者信息"""
    frame = inspect.currentframe()
    try:
        # 跳过当前帧和调用帧
        outer_frames = inspect.getouterframes(frame)[2]
        module = inspect.getmodule(outer_frames.frame)
        return f"{module.__name__ if module else 'unknown'}:{outer_frames.function}"
    finally:
        del frame

def safe_get(d: Dict, path: str, default: Any = None) -> Any:
    """安全获取嵌套字典值
    
    Args:
        d: 原始字典
        path: 字段路径，使用点号分隔
        default: 默认值
        
    Example:
        >>> data = {"a": {"b": {"c": 1}}}
        >>> safe_get(data, "a.b.c")
        1
    """
    try:
        keys = path.split('.')
        for key in keys:
            d = d[key]
        return d
    except (KeyError, TypeError):
        return default

def deep_merge(dict1: Dict, dict2: Dict) -> Dict:
    """深度合并两个字典"""
    result = dict1.copy()
    
    for key, value in dict2.items():
        if isinstance(value, dict) and key in result and isinstance(result[key], dict):
            result[key] = deep_merge(result[key], value)
        else:
            result[key] = value
            
    return result

def generate_id(prefix: str = "", length: int = 8) -> str:
    """生成唯一标识符"""
    unique_part = str(uuid.uuid4())[:length].replace('-', '')
    return f"{prefix}{unique_part}" if prefix else unique_part

def retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器
    
    Args:
        max_attempts: 最大重试次数（包含首次尝试）
        delay: 初始延迟时间（秒）
        backoff: 每次重试的延迟增长系数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal delay
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts: {str(e)}")
                        raise
                    logger.warning(f"Attempt {attempt + 1} failed: {str(e)}, retrying in {delay:.2f}s...")
                    time.sleep(delay)
                    delay *= backoff
            return None  # 这里应该永远不会执行到
            
        return wrapper
    return decorator


def validate_type(value: Any, expected_type: type, allow_none: bool = False) -> bool:
    """验证值类型
    
    Args:
        value: 需要验证的值
        expected_type: 预期类型
        allow_none: 是否允许None
    """
    if allow_none and value is None:
        return True
    return isinstance(value, expected_type)

def format_size(size_bytes: int) -> str:
    """格式化文件大小为可读形式"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024:
            return f"{size_bytes:.2f}{unit}"
        size_bytes /= 1024
    return f"{size_bytes:.2f}PB"