from typing import Any, Dict, List
from ..base import AgentContext
from .base_validator import ValidatorAgent
from loguru import logger
from ...usual_func import list_agent

import json
import asyncio
class ListJsonValidator:
    """Class to validate list[json] format and JSON keys."""
    
    def __init__(self, available_agents: List[str]) -> None:
        self.available_agents = available_agents
    
    def validate(self, data: str) -> Dict[str, Any]:
        required_fields = {"old_file_name": str, "new_file_name": str, "changes": list}
        try:
            parsed_data = json.loads(data)
            
            # 校验顶层字段是否齐全以及类型是否正确
            for field, field_type in required_fields.items():
                if field not in parsed_data:
                    return {"confidence": 0.0, "error": f"Missing required field: '{field}'"}
                if not isinstance(parsed_data[field], field_type):
                    return {"confidence": 0.0, "error": f"Field '{field}' must be of type {field_type.__name__}"}
            
            # 校验 changes 列表结构
            if not isinstance(parsed_data["changes"], list):
                return {"confidence": 0.0, "error": "'changes' must be a list"}

            # 校验 changes 列表中的每个对象格式
            for idx, change in enumerate(parsed_data["changes"]):
                # 检查是否是 dict
                if not isinstance(change, dict):
                    return {"confidence": 0.0, "error": f"Item at index {idx} in 'changes' is not a JSON object"}

                # Required fields for each change item
                change_required_fields = {
                    "type": str,
                    "ai_judge_type": str,
                    "raw": dict,
                    "old": (str, type(None)),  # old can be string or None
                    "new": (str, type(None)),  # new can be string or None
                    "ai_judge": str,
                }

                for field, field_type in change_required_fields.items():
                    if field not in change:
                        return {"confidence": 0.0, "error": f"Missing field '{field}' in 'changes' at index {idx}"}
                    if not isinstance(change[field], field_type):
                        valid_types = (
                            field_type.__name__
                            if isinstance(field_type, type)
                            else " or ".join([t.__name__ for t in field_type])
                        )
                        return {"confidence": 0.0, "error": f"Field '{field}' in 'changes' at index {idx} must be of type {valid_types}"}

            # 到这里为止，格式校验成功
            # 
            # 
            # 这里接ai校验 返回直接给 {"confidence": 1.0, "details": parsed_data} 就可以了，是不是details无所谓，目前只要一个confidence
            return {"confidence": 1.0, "details": parsed_data}
        except json.JSONDecodeError:
            return {"confidence": 0.0, "error": "Invalid JSON format"}


class TextValidatorAgent(ValidatorAgent):
    """A validator agent that validates text processing results."""
    
    async def _setup(self) -> None:
        """Setup text validation resources."""
        await super()._setup()
        self.logger.info("Initializing text validator resources")
    
    async def _teardown(self) -> None:
        """Cleanup text validation resources."""
        await super()._teardown()
        self.logger.info("Cleaning up text validator resources")
    
    async def _initialize_rules(self) -> None:
        """Initialize validation rules."""
        self.validation_rules = [
            {
                "name": "json_format",
                "description": "Check if text is in json format",
            }
        ]
        self.logger.info("Initialized text validation rules")
    
    async def _validate_rule(self, rule: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Validate a single rule against the input data."""
        rule_name = rule.get("name")
        text = context.input_data.get("text", "")
        
        if not text:
            return {
                "rule": rule_name,
                "passed": False,
                "confidence": 0.0,
                "error": "No text provided for validation"
            }
        
        try:
            if rule_name == "list_json_format":
                available_agents = await list_agents()  # Fetch available agent names dynamically
                validator = ListJsonValidator(available_agents)
                validation_result = validator.validate(text)
                passed = validation_result["valid_format"]
                return {
                    "rule": rule_name,
                    "passed": passed,
                    "confidence": 1.0,
                    "details": validation_result
                }
            else:
                raise ValueError(f"Unknown validation rule: {rule_name}")
            
            return {
                "rule": rule_name,
                "passed": passed,
                "confidence": confidence,
                "details": {
                    "text_length": len(text),
                    "word_count": len(text.split())
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error validating rule {rule_name}: {str(e)}")
            return {
                "rule": rule_name,
                "passed": False,
                "confidence": 0.0,
                "error": str(e)
            } 


# 示例 JSON 数据
input_json = """
{
    "old_file_name": "example_old.txt",
    "new_file_name": "example_new.txt",
    "changes": [
        {
            "type": "edit",
            "ai_judge_type": "manual",
            "raw": {"context": "example"},
            "old": "old value",
            "new": "new value",
            "ai_judge": "dididi"
        },
        {
            "type": "delete",
            "ai_judge_type": "auto",
            "raw": {"context": "example 2"},
            "old": "old value 2",
            "new": null,
            "ai_judge": "balabala"
        }
    ]
}
"""

# 测试可用的 agents 列表
available_agents = ["manual", "auto"]

def generate_test_cases():
    """生成所有可能的测试场景"""
    return [
        # Test Case 1: 完整有效的输入数据
        {
            "input_data": """
            {
                "old_file_name": "example_old.txt",
                "new_file_name": "example_new.txt",
                "changes": [
                    {
                        "type": "edit",
                        "ai_judge_type": "manual",
                        "raw": {"context": "example"},
                        "old": "old value",
                        "new": "new value",
                        "ai_judge": "approved"
                    }
                ]
            }
            """,
            "expected_confidence": 1.0,
            "description": "Valid input JSON with all fields correctly formatted"
        },
        # Test Case 2: 缺少字段 - 缺少 old_file_name
        {
            "input_data": """
            {
                "new_file_name": "example_new.txt",
                "changes": []
            }
            """,
            "expected_confidence": 0.0,
            "description": "Missing the 'old_file_name' field"
        },
        # Test Case 3: 字段类型错误 - old_file_name 不是字符串
        {
            "input_data": """
            {
                "old_file_name": 123,
                "new_file_name": "example_new.txt",
                "changes": []
            }
            """,
            "expected_confidence": 0.0,
            "description": "'old_file_name' field has incorrect type (int instead of str)"
        },
        # Test Case 4: changes 不是列表
        {
            "input_data": """
            {
                "old_file_name": "example_old.txt",
                "new_file_name": "example_new.txt",
                "changes": "not_a_list"
            }
            """,
            "expected_confidence": 0.0,
            "description": "'changes' field is not a list"
        },
        # Test Case 5: changes 的 change 项缺失字段
        {
            "input_data": """
            {
                "old_file_name": "example_old.txt",
                "new_file_name": "example_new.txt",
                "changes": [
                    {
                        "ai_judge_type": "manual",
                        "raw": {"context": "example"},
                        "old": "old value",
                        "new": "new value",
                        "ai_judge": "approved"
                    }
                ]
            }
            """,
            "expected_confidence": 0.0,
            "description": "A change item is missing the 'type' field"
        },
        # Test Case 6: 空JSON
        {
            "input_data": "{}",
            "expected_confidence": 0.0,
            "description": "JSON with no required fields"
        },
        # Test Case 7: 非法 JSON 格式
        {
            "input_data": "{invalid_json}",  # 无效 JSON
            "expected_confidence": 0.0,
            "description": "Malformed JSON input"
        }
    ]


async def test_list_json_validator():
    """测试 ListJsonValidator 的兼容性和边界情况"""
    # 初始化 ListJsonValidator
    available_agents = ["manual", "auto"]
    validator = ListJsonValidator(available_agents)

    # 获取所有测试用例
    test_cases = generate_test_cases()

    for idx, case in enumerate(test_cases):
        print(f"Running Test Case {idx + 1}: {case['description']}")

        # 调用 ListJsonValidator.validate 方法
        result = validator.validate(case["input_data"])

        # 检查结果是否符合期望
        if result["confidence"] == case["expected_confidence"]:
            print(f"✅ Test Case {idx + 1} Passed\n")
        else:
            print(f"❌ Test Case {idx + 1} Failed")
            print(f"Expected confidence: {case['expected_confidence']}, got: {result['confidence']}")
            print(f"Error Details: {result.get('error', '')}\n")

if __name__ == "__main__":
   # 这里测试
   asyncio.run(test_list_json_validator())