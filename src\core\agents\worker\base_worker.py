from typing import Any, Dict, List, Optional
from ..base import BaseAgent, AgentContext, AgentConfig
from loguru import logger

class WorkerAgent(BaseAgent):
    """Base class for worker agents that perform specific tasks."""

    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.task_queue: List[AgentContext] = []
        self.max_queue_size = config.parameters.get("max_queue_size", 100)

    async def _setup(self) -> None:
        """Setup worker-specific resources."""
        self.logger.info(f"Setting up worker agent: {self.config.name}")
        # Add worker-specific setup logic here

    async def _teardown(self) -> None:
        """Cleanup worker-specific resources."""
        self.logger.info(f"Tearing down worker agent: {self.config.name}")
        self.task_queue.clear()

    async def add_task(self, context: AgentContext) -> bool:
        """Add a task to the worker's queue."""
        if len(self.task_queue) >= self.max_queue_size:
            self.logger.warning(f"Task queue full for worker: {self.config.name}")
            return False

        self.task_queue.append(context)
        self.logger.debug(f"Added task to queue for worker: {self.config.name}")
        return True

    async def get_next_task(self) -> Optional[AgentContext]:
        """Get the next task from the queue."""
        if not self.task_queue:
            return None
        return self.task_queue.pop(0)

    async def execute(self, context: AgentContext) -> Dict[str, Any]:
        """Execute the worker's main logic."""
        self.logger.info(f"Executing task for worker: {self.config.name}")
        try:
            result = await self._process_task(context)
            return {
                "status": "success",
                "result": result,
                "worker": self.config.name
            }
        except Exception as e:
            self.logger.error(f"Error executing task: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "worker": self.config.name
            }

    async def _process_task(self, context: AgentContext) -> Dict[str, Any]:
        """Process a single task. To be implemented by specific worker agents."""
        raise NotImplementedError("Worker agents must implement _process_task")