# -*- coding: utf-8 -*-
"""
@File    : hook-sklearn.py.py
<AUTHOR> zhenp
@Date    : 2025-06-09 09:47
@Desc    : Description of the file
"""
from PyInstaller.utils.hooks import collect_submodules, collect_dynamic_libs, collect_data_files

print("==== Hook sklearn loaded! ====")

binaries = collect_dynamic_libs('sklearn')

hiddenimports = collect_submodules('numpy.fft')  # Include all numpy submodules
hiddenimports.extend([
    'sklearn.externals.array_api_compat.numpy',  # Add array_api_compat submodules explicitly
    'sklearn.externals.array_api_compat.numpy.fft',
])

# Collect the missing CSS file and other resources from sklearn/utils/_repr_html/
datas = collect_data_files('sklearn')