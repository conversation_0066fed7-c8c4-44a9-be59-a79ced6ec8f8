import sys
import os

# 添加项目根目录到 sys.path
# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

from src.core.agents.base import AgentConfig
from typing import Any, Dict
from ..base import AgentContext
from .base_worker import WorkerAgent
from loguru import logger
from src.services.llm.factory import LLMServiceFactory
from src.services.llm.general_llm_connect import model_configs, ModelAPI
from src.services.llm.connect_model import query_model, connect_qwen3_model_nothink, query_llama
from src.prompt.config import DIFF_PROMPT_AI_JUDGE
import yaml
import json

# Define a mock prompt and test JSON for diff_handle
prompt_ai_judge = """
# role
You are an expert in the field of difference analysis, specializing in analyzing data related to the automotive industry. You are now required to process the difference analysis of relevant file materials.

# task
The user will provide you with a JSON-formatted input, and you need to focus on the "data" list within the JSON file.
This is a dict list, for each dict type, you have to analyze the difference between new content and the old one, then you should judge the **change type** and fill that in "judge_result", then briefly conclude the difference and fill it in "diff_content", then comes the most important part: 
1. Combine the context in "raw" to determine whether the change belongs to a functional change. 
    Functional changes:Changes such as numerical modifications, additions, deletions, or edits of descriptive information 
    Non-Functional changes:Headers, footers, font colors, or text sizes 
    ** Attention ** :  If it involves symbol changes, contextual information in "raw" is required for judgment.
2. Fill your judge for if it is a functional change and the reason briefly in  "judge_reason". You should judge according to context.

# change type list
update
delete
add

# input format:
{
"old_file_name": (old filename), 
"new_file_name": (new filename), 
"data": [
    "id": (special id here),
    {
        "type": (change type here), 
        "new": { 
                "context": {
                    "header_list": "",
                    "current_row": "",
                    "cell_header": "",
                    "pre_context": "(context before new content)",
                    "next_context": "(context after new content)"
                },
        },
        "old": { 
                "context": {
                    "header_list": "",
                    "current_row": "",
                    "cell_header": "",
                    "pre_context": "(context before old content)",
                    "next_context": "(context after old content)"
                },
        }
    }
    ]
}

# output format
{
    "idx": (same to id in data)
    "judge_result": "(AI judge change type, must be in english within update,delete,add)",
    "diff_content":" (ここに変更内容を簡潔にまとめてください)",
    "judge_reason": "(この変更が機能の結果に影響を与えるかどうかは、CONTENT と CONTEXT に基づいて判断してください)"
}

# requirement
- If the "old" part is blank or "new" part is blank, it means add or delete occurs
- ONLY ANSWER WITH ONE JSON FILE IN DICT FORMAT,  do not give any other answer, just start with { but not ''' or ```
- You should answer as a json file strictly follow the output format but in dict, which means start with'{' and end with'}'
- you have to automatically check and adjust your language in "ai_judge" according to most content language with user's input and Context  
- All the result should be in Japanese text but not unicode

  Attention! Output text should strictly follow this rule: 
    - Backslash must only be followed by English characters to represent standard escape sequences.
    - Non-English characters (e.g., `你`, `中文`, or other Unicode symbols) **must not** follow a backslash.
- CHANGE TYPE LIST AND judge_result MUST BE IN ENGLISH
"""


class DiffWorker(WorkerAgent):
    """A worker agent that uses LLM for text generation."""

    def __init__(self, config):
        super().__init__(config)
        self.llm_service = None

    async def _setup(self) -> None:
        """Setup LLM service."""
        await super()._setup()
        provider = self.config.parameters.get("llm_provider", "openai")
        self.llm_service = LLMServiceFactory.create_service(provider)
        await self.llm_service.initialize()
        self.logger.info(f"Initialized LLM service with provider: {provider}")

    async def _teardown(self) -> None:
        """Cleanup LLM service."""
        if self.llm_service:
            await self.llm_service.cleanup()
        await super()._teardown()
        self.logger.info("Cleaned up LLM service")

    async def diff_handle(self, ai_jud: bool, DiffJson: json, prompt_diff: str,llm_config: dict = None):
        ai_jud = True
        diff_jsonfile = DiffJson
        # print(diff_jsonfile)
        # 提取旧文件名和新文件名
        old_file_name = diff_jsonfile.get("old_file_name")
        new_file_name = diff_jsonfile.get("new_file_name")

        if not llm_config:
            llm_config = model_configs

        text_send = f":: status Analyzing difference between {old_file_name} and {new_file_name}\n:: loading\n"
        print("========text========")
        print(text_send)
        # 发送给前端口
        # await send_to_frontend(text_send)

        # 提取 data 列表并构造 result_list
        item = diff_jsonfile.get("data")
        mid_result = {
            "old_file_name": old_file_name,
            "new_file_name": new_file_name,
            "data_type": item.get("data_type"),
            "block_name": item.get("block_name"),
            "data": {
                "type": item.get("type", {}),
                "ai_judge": item.get("ai_judge", {}),
                "old": item.get("old", {}),
                "new": item.get("new", {}),
            }
        }
        # region 万一以后要把这一步放到worker里我们也不影响
        # async def process_single_result(single_result):
        #     if ai_jud is True:
        #         # 这里保证 json 转字符串丢给 llm，不然会报错
        #         single_result_str = json.dumps(single_result, ensure_ascii=False)

        #         test_answer = await query_model(single_result_str, prompt_diff)
        #         # validate 放在这里
        #         validate_answer = await validate(test_answer)
        #         print(test_answer)
        #         # 那么验证会调整到这里

        #         return test_answer
        #     else:
        #         return single_result

        # async def process_results_in_batches(mid_result, batch_size=7):
        #     # 将结果按照每 batch_size 分组
        #     for i in range(0, len(mid_result), batch_size):
        #         batch = mid_result[i:i + batch_size]
        #         # 并发处理当前批次
        #         batch_results = await asyncio.gather(*[process_single_result(single_result) for single_result in batch])
        #         # 打印当前批次结果或其他处理
        #         print(batch_results)
        #         # 累积最终结果
        #         if i == 0:
        #             all_results = batch_results
        #         else:
        #             all_results.extend(batch_results)
        #         await asyncio.sleep(5)
        #     return all_results
        # endregion

        # 这里可以直接把mid_result即一个文件的结果传入给ai 判定
        ai_jud = True
        if ai_jud == True:
            single_result_str = json.dumps(mid_result, ensure_ascii=False)
            # test_answer = await connect_qwen3_model_nothink(single_result_str, prompt_diff)

            model_api = ModelAPI(llm_config)
            # test_answer = await model_api.call_model("azure_gpt4", single_result_str, prompt_diff)
            # test_answer = await connect_qwen3_model_nothink(single_result_str, prompt_diff)
            test_answer = await query_model(single_result_str, DIFF_PROMPT_AI_JUDGE)

            #test_answer = await query_llama(single_result_str, DIFF_PROMPT_AI_JUDGE)
            #test_answer = await query_llama(single_result_str, prompt_diff)

            # await process_results_in_batches(mid_result, batch_size=7)
            files_analysis = test_answer
        else:
            files_analysis = mid_result

        return files_analysis

    async def execute(self, context: AgentContext) -> Dict[str, Any]:
        """Execute the worker's main logic."""
        self.logger.info(f"Executing task for worker: {self.config.name}")

        try:
            # JsonTest = json.loads(getDiffJson)
            JsonTest = context.metadata.get("single_json")
            one_pair_result = await self.diff_handle(
                ai_jud=True,
                DiffJson=JsonTest,
                prompt_diff=context.metadata.get("prompt"),
                llm_config=context.metadata.get("llm_config")
            )
            return {
                "status": "success",
                "result": one_pair_result,
                "worker": self.config.name
            }
        except Exception as e:
            self.logger.error(f"Error executing task: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "worker": self.config.name
            }


# 运行测试脚本
import asyncio

prompt_ai_judge_2 = """
# role
You are an expert in the field of difference analysis, specializing in analyzing data related to the automotive industry. You are now required to process the difference analysis of relevant file materials.

# task
The user will provide you with a JSON-formatted input, and you need to focus on the "data" list within the JSON file.
This is a dict list, for each dict type, you have to analyze the difference between new content and the old one, then you should judge the **change type** and fill that in "judge_result", then briefly conclude the difference and fill it in "diff_content", then comes the most important part: 
1. Combine the context in "raw" to determine whether the change belongs to a functional change. 
    Functional changes:Changes such as numerical modifications, additions, deletions, or edits of descriptive information 
    Non-Functional changes:Headers, footers, font colors, or text sizes 
    ** Attention ** :  If it involves symbol changes, contextual information in "raw" is required for judgment.
2. Fill your judge for if it is a functional change and the reason briefly in  "judge_reason".

# change type list
update
delete
add

# input & output  format:
{
"old_file_name": (old filename), 
"new_file_name": (new filename), 
"data": [
    {
        "type": (change type here), 
        "ai_judge":
            { 
                "judge_result": "(AI judge change type)",
                "diff_content":" (Conclude change briefly here)",
                "judge_reason": "(You should judge whether this change will influnce function result)",,
            }
        "raw": {** context here **}, 
        "old": { 
            (old content here), 
         },
        "new": { 
        (new content here), 
        },
    }
    ]
}

# example: here we have already insert AI judge
{
    "old_file_name": "E:\\bjx\\tags\\DHCP188_office365\\901\\before\\901_01101-HCU-新改3_多重通信仕様_1.CAN(別紙_通信ノード間途絶判定条件_復帰条件).xlsx", 
    "new_file_name": "E:\\bjx\\tags\\DHCP188_office365\\901\\before\\901_01101-HCU-制御KS_多重通信仕様_1.CAN(別紙_通信ノード間途絶判定条件_復帰条件).xlsx", 
    "data": [
        {
            "type": "add",
            "data_type": "graphic",
            "content_type": "graphic",
            "sub_type": "graphic",
            "ai_judge": { 
                "judge_result": "追加",
                "diff_content":" テーブルに新しい列が追加されました。",
                "judge_reason": "新規テーブルカラムを追加し、「条件付き適用」などのコンテンツを含めます。これらの変更はビジネスロジックやデータの表示に影響するため、機能変更とみなされます。",
            },
            "new": {
                "content": "id:2\nname:吹き出し: 四角形 1\ntext:QA No.183の内容反映\nｴﾝｼﾞﾝ回転数を使用のためENG1S11を参\ngraphic_type:shape\nwidth:125\nheight:52\nstyle.font_style.bold:False\nstyle.font_style.italic:False\nstyle.font_style.underline:False\nstyle.font_style.strikeout:False\ncoordinate.desc:B77\ncoordinate.left:18\ncoordinate.top:1101\ndigest:1f0f03001f1f1f1f",
                "index": "B77",
                "id": 3,
                "row_index": "",
                "col_index": "",
                "diff_context": "",
                "diff_content": ""
            },
            "chapter": "",
            "raw": {},
        }
    ]
}


# requirement
- If the user input don't have ai judge key, you have to add it
- You should not change user input, but you should add information and fill it in each "judge_result", "diff_content" and "judge_reason" part in "ai_judge" key
- If the "old" part is blank or "new" part is blank, just leave it there because that means add or delete occurs
-  ONLY ANSWER WITH ONE JSON FILE,  do not give any other answer
-  User may upload json file with a little different, however you should keep this little different if it doesnt break the format 
- you have to automatically check and adjust your language in "ai_judge" according to most content language with user's input   
"""


async def mock_test():
    # Create a valid AgentConfig instance for ManagerWorker
    config = AgentConfig(
        name="diff_worker",
        type="worker",
        description="A worker agent for analyzing diffs.",
        priority=1,
        parameters={
            "llm_provider": "openai"  # Example provider
        }
    )

    # Initialize the DiffWorker instance
    diff_worker = DiffWorker(config)

    # Perform setup before calling the method
    await diff_worker._setup()

    # Define a mock prompt and test JSON for diff_handle

    JsonTest = json.loads(getDiffJson)
    mock_diff_json = JsonTest
    # Call the diff_handle method
    result = await diff_worker.diff_handle(True, mock_diff_json, prompt_ai_judge)

    # Print the result for validation
    # print(json.dumps(result, indent=4, ensure_ascii=False))

    # Perform teardown after the test
    await diff_worker._teardown()


from uuid import uuid4


async def full_process_test():
    # Create a valid AgentConfig instance for DiffWorker
    config = AgentConfig(
        name="diff_worker",
        type="worker",
        description="A worker agent for analyzing diffs.",
        priority=1,
        parameters={
            "llm_provider": "openai"  # Example provider
        }
    )

    # Initialize the DiffWorker instance
    diff_worker = DiffWorker(config)

    # Perform setup before calling the method
    await diff_worker._setup()

    # Construct the AgentContext for the test
    context = AgentContext(
        session_id=str(uuid4()),
        input_data={
            "temperature": 0.7,  # Example temperature setting
            "max_tokens": 1500  # Example limit for token generation
        },
        metadata={
            # Provide mock file paths for testing
            "file_path_list": [
                "mock/path/to/before.docx",
                "mock/path/to/after.docx"
            ],
            "ai_judge": True,
            "prompt": prompt_ai_judge
        }
    )

    # Call the execute method (entire flow starts here)
    result = await diff_worker.execute(context)

    # Perform teardown after the test
    await diff_worker._teardown()


if __name__ == "__main__":
    # asyncio.run(mock_test())
    print("仅供测试")
    # asyncio.run(full_process_test())