import os
import uuid
import hashlib
import re
from datetime import datetime
from sqlalchemy.orm import Session
from src.models.task.models import FileComparisonTask
from loguru import logger

from src.models.upload.FileComparisonRepository import FileComparisonRepository

# 配置允许的文件类型
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'xlsm','txt', 'XLSX'}

# 配置最大文件大小（默认20MB）
MAX_CONTENT_LENGTH = 20 * 1024 * 1024

# 配置最小文件大小（1KB）
MIN_CONTENT_LENGTH = 1 * 1024


class FileCheckResult:
    def __init__(self, is_valid: bool, message: str = "", file_type: str = None, file_hash: str = None):
        self.is_valid = is_valid
        self.message = message
        self.file_type = file_type
        self.file_hash = file_hash


def check_file(file_path) -> FileCheckResult:
    """
    检查文件的有效性
    :param file: 文件对象
    :return: FileCheckResult 对象
    """
    file = open(file_path, 'rb')
    try:
        # 1. 检查文件是否为空
        file.seek(0, 2)  # 移动到文件末尾
        size = file.tell()
        file.seek(0)  # 重置文件指针

        if size == 0:
            return FileCheckResult(False, "文件为空")

        if size < MIN_CONTENT_LENGTH:
            return FileCheckResult(False, f"文件太小（小于{MIN_CONTENT_LENGTH / 1024}KB）")

        if size > MAX_CONTENT_LENGTH:
            return FileCheckResult(False, f"文件太大（超过{MAX_CONTENT_LENGTH / 1024 / 1024}MB）")

        # 2. 读取文件头部进行文件类型检测
        header = file.read(2048)  # 读取前2KB用于检测
        file.seek(0)  # 重置文件指针

        # 获取文件类型
        file_type = get_file_mime_type(header)

        # 3. 检查文件格式
        ext = os.path.splitext(file_path)[1].lower().lstrip('.')
        if ext not in ALLOWED_EXTENSIONS:
            return FileCheckResult(False, f"不支持的文件类型: {ext}")

        # 4. 计算文件哈希值（用于后续病毒检测）
        file_hash = hashlib.sha256(header).hexdigest()

        # 5. 预留病毒检测接口
        # TODO: 实现病毒检测
        # virus_check_result = check_virus(file_hash)
        # if not virus_check_result.is_safe:
        #     return FileCheckResult(False, "文件可能包含病毒")
        return FileCheckResult(True, "文件检查通过", file_type, file_hash)

    except Exception as e:
        logger.error(f"文件检查失败: {str(e)}")
        return FileCheckResult(False, f"文件检查失败: {str(e)}")


def get_file_mime_type(file_content):
    """
    获取文件的MIME类型
    :param file_content: 文件内容
    :return: MIME类型字符串
    """
    # 常见文件类型的魔数（文件头）
    signatures = {
        b'%PDF': 'application/pdf',
        b'PK\x03\x04': 'application/zip',  # ZIP文件
        b'\xD0\xCF\x11\xE0': 'application/vnd.ms-office',  # Office文件
        b'\x50\x4B\x03\x04': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # DOCX
        b'\x50\x4B\x03\x04': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',  # XLSX
    }

    # 检查文件头
    for signature, mime_type in signatures.items():
        if file_content.startswith(signature):
            return mime_type

    # 如果无法通过魔数识别，使用扩展名判断
    return 'application/octet-stream'


def safe_filename(filename):
    # 允许中文、英文、数字、下划线、点、横线
    return re.sub(r'[^\u4e00-\u9fa5\w\.-]', '', filename)


class FileComparisonService:
    def __init__(self, db: Session):
        """
        初始化服务类，接收数据库会话对象
        """
        self.repository = FileComparisonRepository(db)

    async def upload_files(self,
                           user_id: str,
                           session_id: str,
                           task_type: str,
                           file_before: str,
                           file_after: str) -> FileComparisonTask:
        """
        上传文件并创建文件比对任务

        :param user_id: 用户 ID
        :param session_id: 会话 ID
        :param task_type: 任务类型
        :param file_before: 变更前的文件路径
        :param file_after: 变更后的文件路径
        :return: 创建的 FileComparisonTask 对象
        """
        # 校验文件的合法性
        result_file_before = check_file(file_before)
        result_file_after = check_file(file_after)

        if not result_file_before.is_valid:
            raise ValueError("file_before校验失败，请检查文件格式或内容")

        if not result_file_after.is_valid:
            raise ValueError("file_after校验失败，请检查文件格式或内容")

        # 生成任务 ID 和文件路径信息
        random_uuid = uuid.uuid4()
        file_path = {
            "file_before": file_before,
            "file_after": file_after
        }

        # 创建任务对象
        task = FileComparisonTask(
            user_id=user_id,
            session_id=session_id,
            task_type=task_type,
            create_time=datetime.utcnow(),
            extra_info=file_path,
            task_id=random_uuid.hex,
            status="processing",
            relative_id=None
        )
        # 调用 Repository 创建任务
        return await self.repository.create_task(task)





