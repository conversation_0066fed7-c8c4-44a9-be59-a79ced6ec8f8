from word_convert_service import WordConvertService
from excel_convert_service import ExcelConvertService
import traceback


def safe_conversion():
    """带完整异常处理的转换示例"""
    files = [
        {
            "type": "word",
            "path": "C:/docs/important.docx",
            "output": "C:/output/important_doc"
        },
        {
            "type": "excel",
            "path": "C:/data/financial.xlsx",
            "output": "C:/output/financial_report",
            "sheets": [0, 1]  # 只转换前两个工作表
        }
    ]

    for file_info in files:
        try:
            if file_info["type"] == "word":
                with WordConvertService() as converter:
                    print(f"\n开始转换Word文档: {file_info['path']}")

                    # 带水印的高质量转换
                    png_files = converter.convert_to_images(
                        file_info["path"],
                        output_prefix=file_info["output"],
                        format="png"
                    )

                    print(f"成功生成 {len(png_files)} 页文档图片")

            elif file_info["type"] == "excel":
                with ExcelConvertService() as converter:
                    print(f"\n开始转换Excel文件: {file_info['path']}")

                    for sheet_idx in file_info.get("sheets", [0]):
                        try:
                            jpg_files = converter.convert_to_images(
                                file_info["path"],
                                output_prefix=f"{file_info['output']}_sheet{sheet_idx + 1}",
                                format="jpg",
                                sheet_index=sheet_idx
                            )
                            print(f"工作表 {sheet_idx + 1} 生成 {len(jpg_files)} 张图片")
                        except Exception as sheet_error:
                            print(f"工作表 {sheet_idx + 1} 转换失败: {str(sheet_error)}")
                            continue

        except FileNotFoundError as e:
            print(f"\n错误: 文件不存在 - {file_info['path']}")
        except PermissionError as e:
            print(f"\n错误: 没有权限访问文件 - {file_info['path']}")
        except Exception as e:
            print(f"\n发生未知错误: {str(e)}")
            traceback.print_exc()  # 打印完整堆栈跟踪
        finally:
            print("-" * 50)  # 分隔线


if __name__ == "__main__":
    safe_conversion()
