from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from src.common.exceptions import BaseError, to_http_exception
import logging

logger = logging.getLogger(__name__)

async def handle_exceptions(request: Request, call_next):
    try:
        return await call_next(request)
    except BaseError as e:
        return JSONResponse(
            status_code=e.status_code,
            content={
                "error": {
                    "message": e.message,
                    "code": e.status_code,
                    "details": e.details
                }
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "message": "Internal server error",
                    "code": 500,
                    "details": str(e)
                }
            }
        )

async def exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """全局异常处理器"""
    # 记录异常信息
    logger.error(f"Exception occurred: {str(exc)}", exc_info=True)
    
    # 处理自定义异常
    if isinstance(exc, BaseError):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "message": exc.message,
                    "details": exc.details
                }
            }
        )
    
    # 处理FastAPI验证错误
    if isinstance(exc, RequestValidationError):
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={
                "error": {
                    "message": "Validation error",
                    "details": exc.errors()
                }
            }
        )
    
    # 处理其他异常
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": {
                "message": "Internal server error",
                "details": str(exc)
            }
        }
    ) 