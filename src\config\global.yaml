app:
  name: "src"
  version: "0.1.0"
  environment: "development"
  debug: true

server:
  host: "0.0.0.0"
  port: 15743
  workers: 4
  timeout: 30

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/src.log"

database:
  url: "sqlite:///./src.db"
  pool_size: 5
  max_overflow: 10

security:
  secret_key: "${SECRET_KEY}"
  token_expire_minutes: 60
  allowed_hosts:
    - "localhost"
    - "127.0.0.1" 