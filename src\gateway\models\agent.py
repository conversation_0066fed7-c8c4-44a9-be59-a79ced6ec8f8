from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from enum import Enum
import time

class AgentCapability(str, Enum):
    """Agent能力类型"""
    CHAT = "chat"           # 对话能力
    TASK = "task"          # 任务处理
    TOOL = "tool"          # 工具调用
    KNOWLEDGE = "knowledge" # 知识库
    VISION = "vision"      # 视觉能力
    AUDIO = "audio"        # 音频能力

class AgentMetadata(BaseModel):
    """Agent元数据"""
    description: str = Field(..., description="Agent功能描述")
    capabilities: List[str] = Field(..., description="Agent支持的能力")
    version: str = Field(..., description="Agent版本")

class AgentInfo(BaseModel):
    """Agent信息"""
    agent_id: str = Field(..., description="Agent唯一标识")
    name: str = Field(..., description="Agent名称")
    enabled: bool = Field(..., description="是否可用")
    metadata: AgentMetadata = Field(..., description="Agent元数据")
    endpoints: Dict[str, str] = Field(default_factory=dict, description="API端点")
    rate_limit: Dict[str, int] = Field(default_factory=dict, description="速率限制")
