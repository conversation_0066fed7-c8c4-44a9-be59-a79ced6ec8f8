# Bibrain 数据流转设计文档

## 1. 数据流转架构

### 1.1 整体架构

```mermaid
graph TD
    A[客户端] --> B[API Gateway]
    B --> C[认证/授权]
    C --> D[请求分发]
    D --> E[插件处理链]
    E --> F[Agent 处理]
    F --> G[结果聚合]
    G --> H[响应处理]
    H --> A
```

### 1.2 核心组件

```python
# 数据流转核心组件
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from enum import Enum

class RequestContext(BaseModel):
    """请求上下文"""
    request_id: str
    client_id: str
    timestamp: float
    metadata: Dict[str, Any]
    plugins: List[str]
    agent_id: Optional[str]

class ResponseContext(BaseModel):
    """响应上下文"""
    request_id: str
    status: str
    data: Any
    error: Optional[str]
    metadata: Dict[str, Any]

class DataFlowManager:
    """数据流转管理器"""
    def __init__(self):
        self.plugin_chain = PluginChain()
        self.agent_manager = AgentManager()
        self.response_aggregator = ResponseAggregator()

    async def process_request(self, request: RequestContext) -> ResponseContext:
        """处理请求"""
        try:
            # 1. 插件预处理
            request = await self.plugin_chain.pre_process(request)
            
            # 2. Agent 处理
            agent_response = await self.agent_manager.process(request)
            
            # 3. 插件后处理
            response = await self.plugin_chain.post_process(agent_response)
            
            # 4. 响应聚合
            final_response = await self.response_aggregator.aggregate(response)
            
            return ResponseContext(
                request_id=request.request_id,
                status="success",
                data=final_response,
                metadata=request.metadata
            )
        except Exception as e:
            return ResponseContext(
                request_id=request.request_id,
                status="error",
                error=str(e),
                metadata=request.metadata
            )
```

## 2. 插件机制

### 2.1 插件接口

```python
class PluginInterface:
    """插件接口"""
    async def pre_process(self, context: RequestContext) -> RequestContext:
        """预处理"""
        raise NotImplementedError

    async def post_process(self, context: ResponseContext) -> ResponseContext:
        """后处理"""
        raise NotImplementedError

class PluginChain:
    """插件链"""
    def __init__(self):
        self.plugins: List[PluginInterface] = []

    def register_plugin(self, plugin: PluginInterface):
        """注册插件"""
        self.plugins.append(plugin)

    async def pre_process(self, context: RequestContext) -> RequestContext:
        """执行预处理链"""
        for plugin in self.plugins:
            context = await plugin.pre_process(context)
        return context

    async def post_process(self, context: ResponseContext) -> ResponseContext:
        """执行后处理链"""
        for plugin in reversed(self.plugins):
            context = await plugin.post_process(context)
        return context
```

### 2.2 示例插件

```python
class LoggingPlugin(PluginInterface):
    """日志插件"""
    async def pre_process(self, context: RequestContext) -> RequestContext:
        logger.info(f"Processing request: {context.request_id}")
        return context

    async def post_process(self, context: ResponseContext) -> ResponseContext:
        logger.info(f"Request completed: {context.request_id}")
        return context

class ValidationPlugin(PluginInterface):
    """验证插件"""
    async def pre_process(self, context: RequestContext) -> RequestContext:
        # 验证请求数据
        if not self.validate_request(context):
            raise ValidationError("Invalid request data")
        return context

    async def post_process(self, context: ResponseContext) -> ResponseContext:
        # 验证响应数据
        if not self.validate_response(context):
            raise ValidationError("Invalid response data")
        return context
```

## 3. 网关机制

### 3.1 网关核心

```python
class APIGateway:
    """API 网关"""
    def __init__(self):
        self.data_flow = DataFlowManager()
        self.rate_limiter = RateLimiter()
        self.cache = Cache()

    async def handle_request(self, request: Request) -> Response:
        """处理请求"""
        # 1. 限流检查
        if not await self.rate_limiter.check(request):
            return Response(status_code=429)

        # 2. 缓存检查
        cached_response = await self.cache.get(request)
        if cached_response:
            return cached_response

        # 3. 创建请求上下文
        context = RequestContext(
            request_id=generate_request_id(),
            client_id=request.client_id,
            timestamp=time.time(),
            metadata=request.metadata
        )

        # 4. 处理请求
        response_context = await self.data_flow.process_request(context)

        # 5. 缓存响应
        await self.cache.set(request, response_context)

        return self.create_response(response_context)
```

### 3.2 路由管理

```python
class Router:
    """路由管理器"""
    def __init__(self):
        self.routes: Dict[str, Route] = {}

    def register_route(self, path: str, handler: Callable):
        """注册路由"""
        self.routes[path] = Route(path, handler)

    async def route(self, request: Request) -> Response:
        """路由请求"""
        route = self.routes.get(request.path)
        if not route:
            return Response(status_code=404)
        return await route.handler(request)
```

## 4. 完整请求处理流程

### 4.1 请求处理流程

```python
class RequestProcessor:
    """请求处理器"""
    def __init__(self):
        self.gateway = APIGateway()
        self.plugin_chain = PluginChain()
        self.agent_manager = AgentManager()

    async def process(self, request: Request) -> Response:
        """处理请求"""
        # 1. 网关处理
        gateway_response = await self.gateway.handle_request(request)
        if gateway_response.status_code != 200:
            return gateway_response

        # 2. 创建请求上下文
        context = RequestContext(
            request_id=generate_request_id(),
            client_id=request.client_id,
            timestamp=time.time(),
            metadata=request.metadata
        )

        # 3. 插件预处理
        context = await self.plugin_chain.pre_process(context)

        # 4. Agent 处理
        agent_response = await self.agent_manager.process(context)

        # 5. 插件后处理
        response = await self.plugin_chain.post_process(agent_response)

        # 6. 返回响应
        return self.create_response(response)
```

### 4.2 错误处理

```python
class ErrorHandler:
    """错误处理器"""
    def __init__(self):
        self.error_handlers: Dict[Type[Exception], Callable] = {}

    def register_handler(self, exception_type: Type[Exception], handler: Callable):
        """注册错误处理器"""
        self.error_handlers[exception_type] = handler

    async def handle_error(self, error: Exception) -> Response:
        """处理错误"""
        handler = self.error_handlers.get(type(error))
        if handler:
            return await handler(error)
        return Response(
            status_code=500,
            content={"error": str(error)}
        )
```

## 5. 性能优化

### 5.1 缓存策略

```python
class Cache:
    """缓存管理器"""
    def __init__(self):
        self.redis = Redis()
        self.memory_cache = LRUCache(1000)

    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        # 1. 检查内存缓存
        value = self.memory_cache.get(key)
        if value:
            return value

        # 2. 检查 Redis 缓存
        value = await self.redis.get(key)
        if value:
            self.memory_cache.set(key, value)
            return value

        return None

    async def set(self, key: str, value: Any, ttl: int = 3600):
        """设置缓存"""
        # 1. 设置内存缓存
        self.memory_cache.set(key, value)

        # 2. 设置 Redis 缓存
        await self.redis.set(key, value, ttl)
```

### 5.2 限流策略

```python
class RateLimiter:
    """限流器"""
    def __init__(self):
        self.redis = Redis()
        self.window_size = 60  # 时间窗口大小（秒）
        self.max_requests = 100  # 最大请求数

    async def check(self, request: Request) -> bool:
        """检查是否允许请求"""
        key = f"rate_limit:{request.client_id}"
        current = await self.redis.incr(key)
        if current == 1:
            await self.redis.expire(key, self.window_size)
        return current <= self.max_requests
```

## 6. 监控和日志

### 6.1 监控指标

```python
class Metrics:
    """监控指标"""
    def __init__(self):
        self.request_count = Counter(
            "request_count",
            "Total number of requests",
            ["endpoint", "method"]
        )
        self.request_latency = Histogram(
            "request_latency",
            "Request latency in seconds",
            ["endpoint"]
        )
        self.error_count = Counter(
            "error_count",
            "Total number of errors",
            ["endpoint", "error_type"]
        )

    async def record_request(self, request: Request, duration: float):
        """记录请求"""
        self.request_count.labels(
            endpoint=request.path,
            method=request.method
        ).inc()
        self.request_latency.labels(
            endpoint=request.path
        ).observe(duration)

    async def record_error(self, request: Request, error: Exception):
        """记录错误"""
        self.error_count.labels(
            endpoint=request.path,
            error_type=type(error).__name__
        ).inc()
```

### 6.2 日志记录

```python
class Logger:
    """日志记录器"""
    def __init__(self):
        self.logger = logging.getLogger("src")

    def setup(self):
        """设置日志"""
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def log_request(self, request: Request):
        """记录请求"""
        self.logger.info(
            f"Request: {request.method} {request.path} "
            f"from {request.client_id}"
        )

    def log_response(self, response: Response):
        """记录响应"""
        self.logger.info(
            f"Response: {response.status_code} "
            f"for {response.request_id}"
        )
``` 