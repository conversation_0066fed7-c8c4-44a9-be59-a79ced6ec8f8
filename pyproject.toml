[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "bibrain"
version = "1.0.0"
description = "BiBrain - A powerful AI agent system"
requires-python = ">=3.8" 
dependencies = [
    "aiohttp~=3.12.13",
    "fastapi>=0.68.0",
    "fitz~=0.0.1.dev2",
    "httpx~=0.28.1",
    "loguru~=0.7.3",
    "numpy~=2.3.1",
    "openai~=1.93.0",
    "pillow~=11.2.1",
    "pydantic>=1.8.0",
    "pyinstaller~=6.12.0",
    "python-dotenv~=1.1.1",
    "pywin32>=310",
    "pyyaml~=6.0.2",
    "requests~=2.32.4",
    "scikit-learn~=1.7.0",
    "setuptools~=80.7.1",
    "sqlalchemy~=2.0.23",
    "starlette~=0.46.2",
    "toml~=0.10.2",
    "uvicorn~=0.35.0",
]
