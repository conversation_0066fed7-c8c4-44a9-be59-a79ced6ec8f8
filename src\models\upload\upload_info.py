from dataclasses import dataclass
from typing import Optional

from pydantic import BaseModel


@dataclass
class UploadInfo:
    """服务信息"""


# 定义请求体模型
class CompareFilesRequest(BaseModel):
    ai_judge: Optional[bool] = True
    user_id: str
    session_id: str
    task_type: str
    file_before: str  # 文件内容或文件路径
    file_after: str   # 文件内容或文件路径

class CompareDirectoriesRequest(BaseModel):
    user_id: str
    session_id: str
    task_type: str
    dir_before: str  # 目录路径或内容
    dir_after: str  # 目录路径或内容
    ai_judge: Optional[bool] = True

