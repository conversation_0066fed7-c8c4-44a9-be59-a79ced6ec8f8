
import logging

from src.services.convert.excel_convert_service import ExcelConvertService
from src.services.convert.word_convert_service import WordConvertService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def convert_word_file():
    """Word文件转换示例"""
    word_file = "D:/Users/<USER>/Desktop/before/A.docx"

    with WordConvertService() as converter:
        try:
            print(f"\n开始转换Word文件: {word_file}")

            # 转换Word为PNG图片（默认输出到同目录）
            png_files = converter.convert_to_images(word_file)

            print("\n转换成功，生成以下文件:")
            for i, png_path in enumerate(png_files, 1):
                print(f"第{i}页: {png_path}")

        except Exception as e:
            print(f"\nWord转换失败: {str(e)}")


def convert_excel_to_images():
    """Excel转换示例"""
    excel_file = "D:/Users/<USER>/Desktop/详细json数据/excel/1220演示数据变更前.xlsx"

    with ExcelConvertService() as converter:
        try:
            print(f"开始转换Excel文件: {excel_file}")

            # 高分辨率输出(600dpi)
            image_files = converter.convert_to_images(
                excel_file,
                output_prefix="D:/output/report",
                format="png",
                dpi=600  # 高质量输出
            )

            print("\n转换成功，生成以下工作表图片:")
            for img_path in image_files:
                print(f"- {img_path}")

        except Exception as e:
            print(f"转换失败: {str(e)}")
#
# def convert_excel_file():
#     """Excel文件转换示例"""
#     excel_file = "D:/Users/<USER>/Desktop/详细json数据/excel/1220演示数据变更前.xlsx"
#
#     with ExcelConvertService() as converter:
#         try:
#             print(f"\n开始转换Excel文件: {excel_file}")
#
#             # 转换Excel为JPG图片（指定输出位置和格式）
#             output_prefix = "D:/output/excel_sheet"
#             jpg_files = converter.convert_to_images(
#                 excel_file,
#                 output_prefix=output_prefix,
#                 format="jpg",
#                 sheet_index=0  # 转换第一个工作表
#             )
#
#             print("\n转换成功，生成以下文件:")
#             for i, jpg_path in enumerate(jpg_files, 1):
#                 print(f"工作表图片{i}: {jpg_path}")
#
#         except Exception as e:
#             print(f"\nExcel转换失败: {str(e)}")
#

if __name__ == "__main__":
    convert_word_file()
    # convert_excel_to_images()
