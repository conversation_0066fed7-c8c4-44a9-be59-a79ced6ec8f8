from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio

router = APIRouter()

@router.post("/compare-files")
async def compare_files(
        request: CompareFilesRequest,
        db_session_factory=Depends(get_db),
        background_tasks: BackgroundTasks = BackgroundTasks()
):
    try:
        # 文件比较任务
        async with db_session_factory() as session:
            service = FileComparisonService(session)
            task = await service.upload_files(
                user_id=request.user_id,
                session_id=request.session_id,
                task_type=request.task_type,
                file_before=request.file_before,
                file_after=request.file_after,
            )

        # 生成任务名称
        job_name = generate_job_name(request)

        # 添加后台任务，任务并行运行且使用独立数据库会话
        background_tasks.add_task(
            run_background_tasks,
            db_session_factory,  # 工厂传入，提供独立会话
            task.task_id,
            request.file_before,
            request.file_after,
            [request.file_before, request.file_after],
            job_name,
        )

        return generate_response(task, request)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件对比失败: {str(e)}")


# ================== 后台任务运行逻辑 ==================
def run_background_tasks(
        db_session_factory,
        task_id,
        file_before_path,
        file_after_path,
        file_paths,
        job_name
):
    """
    后台并行运行任务，保证独立数据库会话。
    """
    async def run_tasks():
        # 定义任务 1：文件差异分析
        async def run_diff_task():
            async with db_session_factory() as session:
                await run_diff_in_background(
                    session=session,
                    task_id=task_id,
                    file_before_path=file_before_path,
                    file_after_path=file_after_path,
                )

        # 定义任务 2：文件转换
        async def file_conversion_task():
            async with db_session_factory() as session:
                await handle_file_conversion(
                    session=session,
                    task_id=task_id,
                    file_paths=file_paths,
                    job_name=job_name,
                )

        # 通过 `asyncio.gather` 并发执行任务
        await asyncio.gather(
            run_diff_task(),
            file_conversion_task()
        )

    # 启动事件循环中的任务
    asyncio.run(run_tasks())