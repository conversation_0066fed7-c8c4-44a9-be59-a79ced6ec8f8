import asyncio
from contextlib import asynccontextmanager
from typing import Optional

from sqlalchemy.orm import sessionmaker
from loguru import logger
from src.models.task.models import Base,FileComparisonTask
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from datetime import datetime
import uuid
from sqlalchemy.future import select
from sqlalchemy import update, and_
from sqlalchemy import delete
from sqlalchemy.exc import SQLAlchemyError

# 使用 SQLite 作为数据库
SQLALCHEMY_DATABASE_URL = "sqlite+aiosqlite:///./task_info.db"
# 创建异步数据库引擎
engine = create_async_engine(SQLALCHEMY_DATABASE_URL, echo=True)

# 创建会话工厂
async_session_factory = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# FastAPI 的依赖函数
async def get_db():
    """
    返回一个会话工厂
    """
    return async_session_factory

# 初始化数据库，创建所有表
async def init_db():
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("SQLite 数据库初始化成功")
        # 自动恢复逻辑：更新上次未完成的任务状态
        async with async_session_factory() as session:
            # 更新所有状态为 'processing' 的任务为 'failed'
            stmt = (
                update(FileComparisonTask)
                .where(and_(FileComparisonTask.status == "processing"))
                .values(status="failed")
            )
            await session.execute(stmt)
            await session.commit()
    except Exception as e:
        logger.error(f"SQLite 数据库初始化失败: {str(e)}")
        raise

class FileDatabase:
    def __init__(self):
        pass

@asynccontextmanager
async def get_db_session_context(db_session_factory: async_sessionmaker[AsyncSession]):
    """安全数据库会话上下文"""
    session = None
    try:
        async with db_session_factory() as session:
            async with session.begin():
                yield session
    except SQLAlchemyError as e:
        logger.error(f"数据库会话错误: {str(e)}")
        if session:
            await session.rollback()
        raise
    finally:
        if session:
            await session.close()


async def safe_db_operation(
    db_session_factory: async_sessionmaker[AsyncSession],
    operation: callable,
    max_retries: int = 3,
    **kwargs
):
    """带重试的数据库操作"""
    for attempt in range(max_retries):
        try:
            async with get_db_session_context(db_session_factory) as session:
                return await operation(session=session, **kwargs)
        except (SQLAlchemyError, ConnectionError) as e:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(0.5 * (attempt + 1))
            logger.warning(f"数据库操作重试中... (尝试 {attempt + 1}/{max_retries})")



async def add_file_comparison_task(
    session: AsyncSession,
    user_id: str,
    session_id: str,
    task_type: str,
    extra_info:dict,
    status: str = "pending",
    result: str = None,

):
    """
    异步添加 FileComparisonTask 到数据库。

    参数:
        - user_id: 用户 ID
        - session_id: 会话 ID
        - task_type: 任务类型（如 file_comparison）
        - extra_info：文件路径信息
        - status: 任务状态（默认: pending）
        - result: 任务结果（默认: None）
    """

    try:
        task = FileComparisonTask(
            user_id=user_id,
            session_id=session_id,
            task_type=task_type,
            create_time=datetime.utcnow(),
            extra_info=extra_info,
            task_id=str(uuid.uuid4()),
            status=status,
            result=result
        )

        session.add(task)
        await session.commit()
        return task.task_id  # 返回 task_id 供后续使用
    except Exception as e:
        await session.rollback()
        raise RuntimeError(f"任务添加失败: {e}")

async def get_file_comparison_task(session: AsyncSession,task_id: str):
    """
    根据 task_id 获取 FileComparisonTask 任务详情
    """

    try:
        result = await session.execute(
            select(FileComparisonTask).where(FileComparisonTask.task_id == task_id)
        )
        task = result.scalar_one_or_none()
        return task  # 返回 ORM 对象
    except Exception as e:
        raise RuntimeError(f"查询任务失败: {e}")

async def update_file_comparison_task(
        session: AsyncSession,
        task_id: str,
        *,
        extra_info: Optional[dict] = None,
        merge_extra_info: bool = False,
        **fields_to_update
):
    if not task_id:
        raise ValueError("task_id 是必填参数。")

    logger.debug(f"开始更新任务，task_id: {task_id}")

    # 处理 extra_info 更新逻辑
    if extra_info is not None:
        if merge_extra_info:
            logger.debug("合并 extra_info...")
            # 获取当前 extra_info
            current_info = await session.execute(
                select(FileComparisonTask.extra_info).where(FileComparisonTask.task_id == task_id)
            )
            current_info = current_info.scalar_one_or_none() or {}

            # 深度合并字典
            def deep_update(target, source):
                for key, value in source.items():
                    if isinstance(value, dict) and key in target and isinstance(target[key], dict):
                        deep_update(target[key], value)
                    else:
                        target[key] = value
                return target

            fields_to_update["extra_info"] = deep_update(current_info.copy(), extra_info)
        else:
            logger.debug("直接更新 extra_info...")
            fields_to_update["extra_info"] = extra_info

    # 过滤 None 值
    fields_to_update = {k: v for k, v in fields_to_update.items() if v is not None}
    if not fields_to_update:
        logger.warning("所有提供的字段值均为 None，未执行更新。")
        return

    try:
        # 更新任务
        stmt = (
            update(FileComparisonTask)
            .where(FileComparisonTask.task_id == task_id)
            .values(**fields_to_update)
        )
        logger.debug(f"生成的 SQL 语句: {stmt}")
        await session.execute(stmt)
        await session.commit()
        logger.info(f"任务 {task_id} 更新成功。")
    except SQLAlchemyError as e:
        logger.error(f"更新任务失败: {e}")
        await session.rollback()
        raise RuntimeError(f"更新任务失败: {e}")


async def delete_file_comparison_task(session: AsyncSession, task_id: str):
    """
    根据 task_id 删除任务
    """

    try:
        stmt = delete(FileComparisonTask).where(FileComparisonTask.task_id == task_id)
        await session.execute(stmt)
        await session.commit()
    except Exception as e:
        await session.rollback()
        raise RuntimeError(f"删除任务失败: {e}")

