from typing import Dict, List, Optional
import time
import logging
from ..models.agent import AgentInfo
from src.common.exceptions import NotFoundError, ValidationError

logger = logging.getLogger(__name__)

class AgentRegistry:
    """Agent注册中心"""
    def __init__(self):
        self._agents: Dict[str, AgentInfo] = {}
        self._agent_groups: Dict[str, List[str]] = {}  # 能力类型 -> Agent ID列表
        self._capability_index: Dict[str, List[str]] = {}  # 能力 -> Agent ID列表
    
    def register(self, agent: AgentInfo) -> None:
        """注册Agent"""
        # 验证Agent信息
        self._validate_agent(agent)
        
        # 注册Agent
        self._agents[agent.agent_id] = agent
        
        # 添加到能力索引
        for capability in agent.metadata.capabilities:
            if capability not in self._capability_index:
                self._capability_index[capability] = []
            if agent.agent_id not in self._capability_index[capability]:
                self._capability_index[capability].append(agent.agent_id)
        
        logger.info(f"Agent registered: {agent.name} ({agent.agent_id})")
    
    def unregister(self, agent_id: str) -> None:
        """注销Agent"""
        if agent_id not in self._agents:
            raise NotFoundError(f"Agent not found: {agent_id}")
        
        agent = self._agents[agent_id]
        
        # 从能力索引中移除
        for capability in agent.metadata.capabilities:
            if capability in self._capability_index:
                self._capability_index[capability].remove(agent_id)
        
        del self._agents[agent_id]
        logger.info(f"Agent unregistered: {agent.name} ({agent_id})")
    
    def update_heartbeat(self, agent_id: str) -> None:
        """更新Agent心跳"""
        if agent_id not in self._agents:
            raise NotFoundError(f"Agent not found: {agent_id}")
        
        self._agents[agent_id].last_heartbeat = time.time()
        logger.info(f"Agent heartbeat updated: {agent_id}")
    
    def get_agent(self, agent_id: str) -> AgentInfo:
        """获取Agent信息"""
        if agent_id not in self._agents:
            raise NotFoundError(f"Agent not found: {agent_id}")
        return self._agents[agent_id]
    
    def get_agents_by_capability(self, capability: str) -> List[AgentInfo]:
        """获取支持指定能力的Agent列表"""
        if capability not in self._capability_index:
            return []
        return [self._agents[agent_id] for agent_id in self._capability_index[capability]]
    
    def get_all_agents(self) -> List[AgentInfo]:
        """获取所有Agent"""
        return list(self._agents.values())
    
    def _validate_agent(self, agent: AgentInfo) -> None:
        """验证Agent信息"""
        if not agent.agent_id or not agent.name:
            raise ValidationError("Agent ID and name are required")
        
        if not agent.metadata.capabilities:
            raise ValidationError("Agent must have at least one capability")
        
        # 验证端点格式
        for endpoint, path in agent.endpoints.items():
            if not path.startswith("/"):
                raise ValidationError(f"Invalid endpoint path: {path}")
        
        # 验证速率限制
        for limit_type, limit in agent.rate_limit.items():
            if limit < 0:
                raise ValidationError(f"Invalid rate limit: {limit_type}")

# 创建全局Agent注册中心实例
agent_registry = AgentRegistry() 