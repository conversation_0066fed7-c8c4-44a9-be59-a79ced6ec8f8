# BiBrain 系统使用文档

## 目录
1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [配置说明](#配置说明)
4. [Agent开发指南](#agent开发指南)
5. [API接口说明](#api接口说明)
6. [常见问题](#常见问题)

## 系统概述

BiBrain是一个基于FastAPI的智能Agent网关系统，提供以下核心功能：
- Agent服务注册与发现
- 负载均衡
- 服务健康检查
- 统一的异常处理
- 灵活的配置管理

## 快速开始

### 1. 环境准备
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量
复制 `config/env_template.txt` 到 `config/.env`，并填写必要的配置：
```bash
cp config/env_template.txt config/.env
```

### 3. 启动服务
```bash
uvicorn src.main:app --reload
```

## 配置说明

### Agent配置
在 `config/agents` 目录下创建Agent配置文件（YAML格式）：

```yaml
agent:
  id: "my-agent-001"
  name: "My Custom Agent"
  service:
    host: "localhost"
    port: 8000
  capabilities:
    - "chat"
  endpoints:
    chat: "/chat"
  mcp_services:
    - name: "knowledge-base"
      version: "1.0.0"
      required: true
```

### 必需配置项
- `id`: Agent唯一标识
- `name`: Agent名称
- `service`: 服务配置（host和port）
- `capabilities`: Agent能力列表
- `endpoints`: API端点配置

### 可选配置项
- `mcp_services`: MCP服务依赖
- `env`: 环境变量
- `description`: 描述信息
- `author`: 开发者信息
- `tags`: 标签列表

## Agent开发指南

### 1. 创建Agent
1. 在 `config/agents` 目录下创建配置文件
2. 实现必要的API端点
3. 注册到网关系统

### 2. 实现API端点
```python
from fastapi import APIRouter

router = APIRouter()

@router.post("/chat")
async def chat_endpoint(request: dict):
    # 实现聊天逻辑
    return {"response": "Hello!"}
```

### 3. 注册Agent

```python
from src.gateway.registry import service_registry

# 注册服务
service_registry.register(
    service_id="my-agent-001",
    name="My Custom Agent",
    host="localhost",
    port=8000
)
```

## API接口说明

### 网关接口

#### 1. 注册服务
```http
POST /gateway/register
Content-Type: application/json

{
    "service_id": "my-agent-001",
    "name": "My Custom Agent",
    "host": "localhost",
    "port": 8000
}
```

#### 2. 注销服务
```http
DELETE /gateway/unregister/{service_id}
```

#### 3. 获取服务列表
```http
GET /gateway/services
```

#### 4. 路由请求
```http
GET /gateway/route/{service_name}
```

### 配置接口

#### 1. 获取配置
```http
GET /config/{config_type}
```

#### 2. 更新配置
```http
PUT /config/{config_type}
```

## 常见问题

### 1. 服务注册失败
- 检查服务ID是否唯一
- 确认服务地址和端口是否正确
- 验证服务是否可访问

### 2. 配置加载错误
- 检查配置文件格式是否正确
- 确认必需配置项是否完整
- 查看日志获取详细错误信息

### 3. MCP服务依赖问题
- 确认所需MCP服务是否已注册
- 检查服务版本是否兼容
- 验证服务是否正常运行

### 4. 性能问题
- 检查负载均衡策略
- 监控服务响应时间
- 调整并发请求限制

## 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 检查认证信息 |
| 403 | 权限不足 | 确认访问权限 |
| 404 | 资源不存在 | 检查资源路径 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 503 | 服务不可用 | 检查服务状态 |

## 最佳实践

1. **配置管理**
   - 使用环境变量管理敏感信息
   - 定期备份配置文件
   - 遵循配置模板规范

2. **服务开发**
   - 实现健康检查接口
   - 添加适当的日志记录
   - 处理异常情况

3. **系统维护**
   - 定期检查服务状态
   - 监控系统资源使用
   - 及时更新依赖包

4. **安全建议**
   - 使用HTTPS
   - 实现访问控制
   - 保护敏感配置

## 联系与支持

如有问题或需要支持，请：
1. 查看系统日志
2. 检查文档
3. 提交Issue
4. 联系技术支持 