from fastapi import HTTPException, status
from typing import Any, Dict, Optional, List


class ConfigurationError(Exception):
    """配置相关错误"""
    def __init__(self, message: str = "配置错误", details: Dict[str, Any] = None):
        self.message = message
        self.details = details or {}
        super().__init__(message)

class ValidationError(Exception):
    """验证失败异常"""
    def __init__(self, message: str = "验证失败", errors: List[str] = None):
        self.message = message
        self.errors = errors or []
        super().__init__(message)

class ResourceNotFoundError(Exception):
    """资源未找到异常"""
    def __init__(self, resource_type: str, identifier: str):
        self.resource_type = resource_type
        self.identifier = identifier
        message = f"{resource_type} '{identifier}' 未找到"
        super().__init__(message)

class BaseError(Exception):
    """基础异常类"""
    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)

class ConfigError(BaseError):
    """配置错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)

class PermissionError(BaseError):
    """权限错误"""
    def __init__(self, message: str = "Permission denied", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=403, details=details)

class ServiceDependencyError(BaseError):
    """MCP服务依赖错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=503, details=details)

class ValidationError(BaseError):
    """验证错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=400, details=details)

class NotFoundError(BaseError):
    """资源未找到错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=404, details=details)

class RateLimitError(BaseError):
    """速率限制错误"""
    def __init__(self, message: str = "Rate limit exceeded", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=429, details=details)

class AuthenticationError(BaseError):
    """认证错误"""
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=401, details=details)

class ServiceUnavailableError(BaseError):
    """服务不可用错误"""
    def __init__(self, message: str = "Service unavailable", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=503, details=details)

def to_http_exception(error: BaseError) -> HTTPException:
    """将自定义异常转换为HTTPException"""
    return HTTPException(
        status_code=error.status_code,
        detail={
            "message": error.message,
            "details": error.details
        }
    ) 