import httpx
import requests
import aiohttp

async def call_azure_model(url, payload, headers=None):
    """Send a POST request to Azure's OpenAI service."""
    try:
        async with httpx.AsyncClient() as client:
            # 异步发送 POST 请求
            response = await client.post(url, json=payload, headers=headers)
            # 检查 HTTP 状态码
            response.raise_for_status()
            return response.json()
    except httpx.RequestError as e:
        print(f"Request failed: {e}")
        return None
    except httpx.HTTPStatusError as e:
        print(f"HTTP error occurred: {e}")
        return None




async def query_model(content: str, test_prompt: str):
    """
    Call Azure OpenAI model to get user's intent.

    Args:
        content (str): The input message for intent prediction.

    Returns:
        str: The user's intent extracted by the model.
    """
    try:
        print(f"🌐 Analyzing users file")

        # Configuration for Azure model
        config = {
            "title": "gpt-4o",
            "model": "gpt-4o",
            "provider": "azure",
            "apiBase": "https://sdw-dev.openai.azure.com",
            "apiVersion": "2025-01-01-preview",
            "deployment": "gpt-4o",
            "contextLength": 32764,
            "apiKey": "dIAz3SnaOXxdmE02SyfW36TDVmCtlOxdO7IKDM2n1RpZZaFlGsmEJQQJ99BCACHYHv6XJ3w3AAABACOGEbzZ",
            "apiType": "azure",
            "systemMessage": test_prompt,
            "completionOptions": {
                "maxTokens": 16384
            }
        }

        # Construct the URL for Azure API
        url = f"{config['apiBase']}/openai/deployments/{config['deployment']}/chat/completions?api-version={config['apiVersion']}"

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {config['apiKey']}"
        }

        # Payload for the request
        payload = {
            "messages": [
                {"role": "system", "content": config["systemMessage"]},
                {"role": "user", "content": content}
            ],
            "max_tokens": config["completionOptions"]["maxTokens"]
        }

        # Call the Azure model
        response_data = await call_azure_model(url, payload, headers)

        # Extract the generated response from the model
        if response_data and 'choices' in response_data:
            message_content = response_data['choices'][0]['message']['content']
            return message_content
        else:
            print(f"Incomplete response from Azure model: {response_data}")
            return None

    except Exception as e:
        print(f"Error occurred while querying the model: {e}")
        return None

async def connect_qwen3_model(content: str,prompt:str) -> str:
    """
    连接 Qwen3 模型并返回响应内容
    """
    url = "http://**************:11400/v1/chat/completions"
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "model": "qwen3",
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": content}
        ],
        "max_token": 10240
    }

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()  
        response_data = response.json()  
        # 确认 `response_data['choices']` 是否存在
        if response_data and 'choices' in response_data and response_data['choices']:
            return response_data['choices'][0]['message']['content']  # 返回内容部分
        else:
            print(f"Invalid model response: {response_data}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None

async def extract_reply_from_response(message_content: str) -> str:
    """
    从模型响应中提取最终的回复内容（排除 <think> 部分）。
    Args:
        message_content (str): 模型返回的完整内容，包括 <think> 部分。
    Returns:
        str: 仅包含用户回复的内容（不包含 <think>）。
    """
    try:
        # 判断是否存在 <think> 标签，剔除 <think> 内容
        if "<think>" in message_content and "</think>" in message_content:
            start_idx = message_content.find("</think>") + len("</think>")
            reply_content = message_content[start_idx:].strip()
            return reply_content
        else:
            # 如果没有 <think> 标签，则直接返回内容
            return message_content.strip()
    except Exception as e:
        print(f"Error extracting reply: {e}")
        return None

async def connect_qwen3_model_nothink(content: str,prompt:str) -> str:
    """
    连接 Qwen3 模型并返回响应内容
    """
    url = "http://**************:11400/v1/chat/completions"
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "model": "qwen3",
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": content}
        ],
        "max_token": 10240
    }

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()  
        response_data = response.json()  
        # 确认 `response_data['choices']` 是否存在
        if response_data and 'choices' in response_data and response_data['choices']:
            text = response_data['choices'][0]['message']['content']
            text_nothink = await extract_reply_from_response(text)
            return text_nothink  # 返回内容部分
        else:
            print(f"Invalid model response: {response_data}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None

async def get_intent_from_model(content: str):
    """
    Call Azure OpenAI model to get user's intent.

    Args:
        content (str): The input message for intent prediction.

    Returns:
        str: The user's intent extracted by the model.
    """
    try:
        print(f"🌐 Attempting to analyze user's intent for content: {content}")

        # Configuration for Azure model
        config = {
            "title": "gpt-4o",
            "model": "gpt-4o",
            "provider": "azure",
            "apiBase": "https://sdw-dev.openai.azure.com",
            "apiVersion": "2025-01-01-preview",
            "deployment": "gpt-4o",
            "contextLength": 32764,
            "apiKey": "dIAz3SnaOXxdmE02SyfW36TDVmCtlOxdO7IKDM2n1RpZZaFlGsmEJQQJ99BCACHYHv6XJ3w3AAABACOGEbzZ",
            "apiType": "azure",
            "systemMessage": "You are a helpful assistant",
            "completionOptions": {
                "maxTokens": 8192
            }
        }

        # Construct the URL for Azure API
        url = f"{config['apiBase']}/openai/deployments/{config['deployment']}/chat/completions?api-version={config['apiVersion']}"

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {config['apiKey']}"
        }

        # Payload for the request
        payload = {
            "messages": [
                {"role": "system", "content": config["systemMessage"]},
                {"role": "user", "content": content}
            ],
            "max_tokens": config["completionOptions"]["maxTokens"]
        }

        # Call the Azure model
        response_data =await call_azure_model(url, payload, headers)

        # Extract the generated response from the model
        if response_data and 'choices' in response_data:
            message_content = response_data['choices'][0]['message']['content']
            return message_content
        else:
            print(f"Incomplete response from Azure model: {response_data}")
            return None

    except Exception as e:
        print(f"Error occurred while querying the model: {e}")
        return None


async def connect_V3(input_text,prompt, model="DeepSeek-V3-W8A8", max_tokens=1024, temperature=0.5, presence_penalty=0, stream=False):
    """
    Generates a response from the LLM based on the input provided.
    
    Parameters:
        input_text (str): The text to send to the LLM.
        model (str): The model to use. Default is "DeepSeek-V3-W8A8".
        max_tokens (int): Maximum tokens for the response. Default is 1024.
        temperature (float): Sampling temperature. Default is 0.5.
        presence_penalty (float): Penalizes new tokens based on presence in input. Default is 0.
        stream (bool): Whether to stream responses. Default is False.

    Returns:
        str: Response content from the LLM.
    """
    url = "http://192.168.146.16/sdw/chatbot/sysai/v1/chat/completions"
    prompttext= prompt+input_text
    payload = {
        "messages": [
            {
                "role": "user",
                "content": prompttext
            }
        ],
        "stream": stream,
        "model": model,
        "max_tokens": max_tokens,
        "temperature": temperature,
        "presence_penalty": presence_penalty
    }

    headers = {"Content-Type": "application/json"}

    try:
        response = requests.post(url, json=payload, headers=headers, verify=False)
        response.raise_for_status()  # Raises an HTTPError for non-200 status codes
        result = response.json()
        content = result.get('choices', [{}])[0].get('message', {}).get('content', "")
        return content
    except requests.exceptions.RequestException as e:
        return f"An error occurred: {e}"

async def query_llama(user_message, prompt):
    max_tokens=10240
    api_url = "http://**************:11500/v1/chat/completions"
    model_name = "llama3.3:70B"
    headers = {
        'Content-Type': 'application/json',
    }

    payload = {
        "model": model_name,
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": user_message},
        ],
        "max_token": max_tokens
    }

    try:
        # 异步创建 HTTP 会话并发送请求
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, headers=headers, json=payload) as response:
                # 检查响应状态码
                if response.status == 200:
                    # 异步获取 JSON 数据
                    json_response = await response.json()
                    
                    # 提取 content 的内容
                    try:
                        content = json_response["choices"][0]["message"]["content"]
                        return content  # 仅返回 content
                    except (KeyError, IndexError) as e:
                        return {"error": f"响应解析失败：无法提取 content，详细信息：{str(e)}"}
                else:
                    return {"error": f"请求失败，状态码：{response.status}", "response": await response.text()}
    except Exception as e:
        return {"error": f"连接 API 过程中发生错误: {str(e)}"}

import requests
import aiohttp

async def async_call_qwen_vl(base64_image: str) -> str:
    """
    异步调用 Qwen2.5-VL 多模态模型以分析图像内容。

    Args:
        base64_image (str): Base64 编码的图片字符串。

    Returns:
        str: 模型返回的描述文本或错误信息。
    """
    # 定义基础 URL 和 Prompt
    server_url = "http://192.168.53.118:5000/qwen2.5-vl-inference"
    en_prompt_describe = """{} Please describe the image content in detail according to the following dimensions:
Basic Information: Image dimensions (width * height), resolution, format, overall color tone, and color saturation.
Main Content: Number of main subjects, their positions, shapes, sizes, and colors. If the subjects are people, describe their actions; if they are objects, explain their uses.
Background Environment: Background color, patterns, scene, and the relationship between the background and the main subjects.
Detailed Elements: Positions, content, fonts, sizes, colors, and functions of details such as text and symbols.
Layout and Composition: Composition method, distribution of various elements, and the resulting visual effects."""

    # 构建请求消息结构（仅包含文本）
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": en_prompt_describe.format(base64_image)},
            ],
        }
    ]

    payload = {
        "messages": messages
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(server_url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if "result" in result:
                        return result["result"]
                    else:
                        return f"Error: {result.get('error', 'Unknown error')}"
                else:
                    return f"Request failed with status code {response.status}"

    except Exception as e:
        return f"Exception occurred: {str(e)}"


import asyncio
# 示例：异步主函数
async def main():
    # 修改为实际的 API 地址和用户输入

    user_message = "你好"
    # 调用异步 API 请求函数
    response = await query_llama(user_message)


# 启动 asyncio 异步事件循环
if __name__ == "__main__":
    asyncio.run(main())
# # region  以下是尝试的动态调度大模型
# async def dynamic_call_model(model_name: str, content: str=None, system_message: str=None):
#     """Use dynamically discovered configuration to call a model."""
#     # Dynamically fetch configuration for the given model
#     llm_config = config_manager.get_llm_config(model_name)
#     if not llm_config:
#         raise ValueError(f"Configuration for '{model_name}' not found.")

#     api_base = llm_config.get("api_base")
#     api_key = os.getenv(llm_config.get("api_key").strip("${}"), llm_config.get("api_key"))
#     model = llm_config.get("model")
#     deployment = llm_config.get("deployment")
#     api_version = llm_config.get("api_version")
#     max_tokens = llm_config.get("completion_options", {}).get("max_tokens", 8192)

#     if not api_base or not api_key or not model or not deployment:
#         raise ValueError(f"Incomplete configuration for '{model_name}'.")

#     print(f"Calling model '{model_name}' with deployment '{deployment}'.")
#     test = {"api_base"=api_base,
#         "api_key"=api_key,
#         "model"=model,
#         "deployment"=deployment,
#         "content"=content,
#         "system_message"=system_message,
#         "max_tokens"=max_tokens,
#         "api_version"=api_version
#     }
#     return test



# def get_intent_from_model(content: str):
#     """
#     Wrapper for the existing functionality using dynamic LLM discovery.
#     """
#     try:
#         system_message = "You are a helpful assistant"
#         print(f"🌐 Attempting to analyze user's intent for content: {content}")
        
#         response_data = dynamic_call_model("azure_gpt4o", content, system_message)
#         if response_data and 'choices' in response_data:
#             message_content = response_data['choices'][0]['message']['content']
#             return message_content
#         else:
#             print(f"Incomplete response from model: {response_data}")
#             return None
#     except Exception as e:
#         print(f"Error occurred while querying the model: {e}")
#         return None



# def use_llm_from_config(model_name: str):
#     """根据 YAML 文件中获取的配置使用特定 LLM"""
#     # 获取 LLM 的配置
#     llm_config = config_manager.get_llm_config(model_name)
    
#     if not llm_config:
#         raise ValueError(f"未找到模型 '{model_name}' 的配置，请确认配置文件是否正确。")
    
#     # 检查 api_base 和 api_key 的必需字段是否存在
#     api_base = llm_config.get("api_base")
#     api_key = llm_config.get("api_key")
#     model = llm_config.get("model")
#     deployment = llm_config.get("deployment")
#     api_version = llm_config.get("api_version")

#     if not api_base or not api_key or not model or not deployment:
#         raise ValueError(f"模型 '{model_name}' 的配置不完整，请检查 api_base, api_key, model, deployment 字段。")
    
#     # 替换环境变量（例如 ${AZURE_GPT4O_KEY}）
#     api_key = os.getenv(api_key.strip("${}"), api_key)
#     if not api_key:
#         raise ValueError(f"未找到环境变量 '{api_key}'，无法加载此模型。")
    
#     print(f"加载 Azure GPT-4o，API 基础地址：{api_base}")
#     print(f"模型名称：{model}, 部署名称：{deployment}, API版本：{api_version}")
    
#     # 假设你调用了 Azure OpenAI 的 API（伪代码示例）
#     response = call_azure_openai(
#         api_base=api_base,
#         api_key=api_key,
#         model=model,
#         deployment=deployment,
#     )
#     return response


# try:
#     result = dynamic_call_model("azure_gpt4o")
#     print(result)
# except Exception as e:
#     print(f"发生错误: {str(e)}")