import os
from pathlib import Path

import toml

# 日志配置
LOGGING_LEVEL = "INFO"

# 默认输出目录（如果需要自定义）
DEFAULT_OUTPUT_DIR = os.getcwd()

current_file_path = Path(__file__).resolve()
PROJ_PATH = current_file_path.parent

config_path = Path.joinpath(PROJ_PATH, 'config.toml')

with open(config_path, 'r', encoding='utf-8') as f:
    config_data = toml.load(f)

config_diff_path = Path.joinpath(PROJ_PATH, 'config_diff.toml')

with open(config_diff_path, 'r', encoding='utf-8') as f:
    config_diff_path = toml.load(f)

# prompt
WORKFLOW_NATURAL_PROMPT = config_data["prompt"]["WORKFLOW_NATURAL_PROMPT"]
WORKFLOW_AGENT_PROMPT = config_data["prompt"]["WORKFLOW_AGENT_PROMPT"]
CONTENT_GENERATION_PROMPT = config_data["prompt"]["CONTENT_GENERATION_PROMPT"]
FILE_GENERATION_PROMPT = config_data["prompt"]["FILE_GENERATION_PROMPT"]
WORKFLOW_TEST_PROMPT = config_data["prompt"]["WORKFLOW_TEST_PROMPT"]

DIFF_PROMPT_AI_JUDGE = config_diff_path["prompt"]["prompt_ai_judge"]
PROMPT_INTENT = config_diff_path["prompt"]["prompt_intent"]
