import json

# AI generation start

# 抽ai_judge 变为一个list
def extract_ai_judge(input_list):
    """
    Extract the 'ai_judge' key from each JSON in the list and return a new list.
    :param input_list: List of JSON objects
    :return: List containing values of 'ai_judge' key
    """
    ai_judge_list = []
    
    for item in input_list:
        if 'result' in item:
            try:
                # 将 'result' 转换为嵌套的 JSON 对象
                result_data = item["result"]
                if 'data' in result_data and 'ai_judge' in result_data['data']:
                    ai_judge_list.append(result_data['data']['ai_judge'])
            except Exception as e:
                print(f"Error parsing 'result' field: {e}")
    
    return ai_judge_list


def backfill_ai_judge(input_data, ai_judge_content_list):
    """
    Backfill the `ai_judge` field in each dictionary within the `data_list`.

    :param data_list: List of dictionaries containing the `ai_judge` fields to be updated.
    :param ai_judge_content_list: List of `ai_judge` values to update the dictionaries.
    :return: Updated list of dictionaries with backfilled `ai_judge` fields.
    """
    # 检查两个列表长度是否匹配
    if len(input_data["data"]) != len(ai_judge_content_list):
        raise ValueError("Length of `data` and `ai_judge_content_list` must match!")

    # 遍历并回填到原始输入的 data 部分
    for i in range(len(input_data["data"])):
        input_data["data"][i]["ai_judge"] = ai_judge_content_list[i]

    return input_data
# 测试示例
input_list = [
    {
        "status": "success",
        "result": {
            "old_file_name": "3130_ユーザーカスタマイズ制御(オフボード通信用)-a.docx",
            "new_file_name": "3130_ユーザーカスタマイズ制御(オフボード通信用)-b.docx",
            "data": {
                "type": "update",
                "ai_judge": {
                    "judge_result": "更新",
                    "judge_reason": "PAGE番号が13から1に変更されました。",
                    "judge_explanation": "この変更はページ番号の編集にすぎず、機能的な影響はありません。"
                },
                "old": {
                    "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用) –( PAGE 13)",
                    "id":"",
                    "row_index": "",
                    "col_index": "",
                    "diff_context": "",
                    "diff_content": ""
                },
                "new": {
                    "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用) –( PAGE 1)",
                    "id": "",
                    "row_index": "",
                    "col_index": "",
                    "diff_context": "",
                    "diff_content": ""
                }
            }
        }
    },
    {
        "status": "success",
        "result": {
            "old_file_name": "3130_ユーザーカスタマイズ制御(オフボード通信用)-a.docx",
            "new_file_name": "3130_ユーザーカスタマイズ制御(オフボード通信用)-b.docx",
            "data": {
                "type": "update",
                "ai_judge": {
                    "judge_result": "更新",
                    "judge_reason": "PAGE番号が13から1に変更されました。",
                    "judge_explanation": "この変更はページ番号の編集にすぎず、機能的な影響はありません。"
                },
                "old": {
                    "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用) –( PAGE 13)",
                    "id": "",
                    "row_index": "",
                    "col_index": "",
                    "diff_context": "",
                    "diff_content": ""
                },
                "new": {
                    "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用) –( PAGE 1)",
                    "id": "",
                    "row_index": "",
                    "col_index": "",
                    "diff_context": "",
                    "diff_content": ""
                }
            }
        }
    }
]


data = [
    {
        "type": "update",
        "data_type": "graphic",
        "content_type": "graphic",
        "ai_judge": {},  # 需要回填
        "new": {
            "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用) –( PAGE 1)"
        },
        "old": {
            "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用) –( PAGE 13)"
        }
    },
    {
        "type": "update",
        "data_type": "graphic",
        "content_type": "graphic",
        "ai_judge": {},  # 需要回填
        "new": {
            "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用) –( PAGE 1)"
        },
        "old": {
            "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用) –( PAGE 13)"
        }
    }
]


data_toberefill = {
    "old_path": "C:\\Users\\<USER>\\Desktop\\0530\\124_dafa4_9\\before\\3130_ユーザーカスタマイズ制御(オフボード通信用)-a.docx",
    "new_path": "C:\\Users\\<USER>\\Desktop\\0530\\124_dafa4_9\\after\\3130_ユーザーカスタマイズ制御(オフボード通信用)-b.docx",
    "old_file_name": "3130_ユーザーカスタマイズ制御(オフボード通信用)-a.docx",
    "new_file_name": "3130_ユーザーカスタマイズ制御(オフボード通信用)-b.docx",
    "total_diff_count": 93,
    "text_diff_count": 1,
    "table_diff_count": 20,
    "graphic_diff_count": 72,
    "picture_diff_count": 0,
    "extends": [],
    "data": [
        {
            "type": "update",
            "data_type": "graphic",
            "content_type": "graphic",
            "sub_type": "graphic",
            "head_type": None,
            "belong_to": "header",
            "diff_point": "text",
            "diff_point_str": "text",
            "rule": "",
            "block_name": "1",
            "extend": [],
            "ai_judge": {},  # 需要回填
            "new": {
                "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用)　–( PAGE 1)",
                "index": "",
                "id": "",
                "row_index": "",
                "col_index": "",
                "diff_context": "",
                "diff_content": ""
            },
            "chapter": "",
            "old": {
                "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用)　–( PAGE 13)",
                "index": "",
                "id": "",
                "row_index": "",
                "col_index": "",
                "diff_context": "",
                "diff_content": ""
            },
            "raw": {}
        },
        {
            "type": "update",
            "data_type": "graphic",
            "content_type": "graphic",
            "sub_type": "graphic",
            "head_type": None,
            "belong_to": "header",
            "diff_point": "text",
            "diff_point_str": "text",
            "rule": "",
            "block_name": "2",
            "extend": [],
            "ai_judge": {},  # 需要回填
            "new": {
                "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用)　–( PAGE 1)",
                "index": "",
                "id": "",
                "row_index": "",
                "col_index": "",
                "diff_context": "",
                "diff_content": ""
            },
            "chapter": "",
            "old": {
                "content": "text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用)　–( PAGE 13)",
                "index": "",
                "id": "",
                "row_index": "",
                "col_index": "",
                "diff_context": "",
                "diff_content": ""
            },
            "raw": {}
        }
    ]
}
# 提取 ai_judge 的列表
ai_judge_content_list = extract_ai_judge(input_list)

# 打印结果
print(ai_judge_content_list)
# AI generation end

updated_input_data = backfill_ai_judge(data_toberefill, ai_judge_content_list)
print(updated_input_data)