import queue
import threading
import traceback
from concurrent.futures.thread import Thread<PERSON>oolExecutor

from fastapi import APIRouter, Depends, Query, BackgroundTasks
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Dict, Any, Optional, Tuple
import os
from datetime import datetime
from loguru import logger
from difflib import get_close_matches

from sqlalchemy.orm import sessionmaker, Session

from src.core.agents.orchestrator.diff_orchestrator import get_status, update_cot
from src.models.task.models import FileComparisonTask
import asyncio
from src.models.upload.upload_info import CompareFilesRequest, CompareDirectoriesRequest
from src.services.convert.update_word_coordinates import extract_size, update_result_regions
from src.services.files_upload.file_comparison_service import FileComparisonService
from src.services.convert.file_convert_service import FileConversionService
from src.services.database.database import get_db, update_file_comparison_task
from src.services.call_agent.diff_agent import setup_and_execute_orchestrator
from src.services.folders_upload.folders_comparison_service import DirectoryComparisonService
from src.services.response import wrap_response, wrap_error_response
from src.services.convert.excel_coordinates_convert_service import batch_calculate_coordinates


router = APIRouter(
    prefix="/task",
    tags=["task"],
    responses={404: wrap_error_response(msg="Not found", code=404)},
)

file_conversion_service = FileConversionService()

# 创建线程池
executor = ThreadPoolExecutor(max_workers=4)


# ====================== 优先级任务队列 ======================
class TaskQueue:
    def __init__(self):
        self.queue = queue.PriorityQueue()
        self._start_worker()

    def _start_worker(self):
        def worker():
            while True:
                priority, task = self.queue.get()
                try:
                    asyncio.run(task())
                except Exception as e:
                    print(f"任务执行失败: {str(e)}")
                finally:
                    self.queue.task_done()

        threading.Thread(target=worker, daemon=True).start()

    def add_task(self, priority: int, task_func):
        self.queue.put((priority, task_func))

task_queue = TaskQueue()


async def run_conversion_task(
    session_factory: sessionmaker,
    task_id: str,
    file_paths: List[str],
    job_name: Optional[str] = None
):
    """
    优化后的文件转换任务（全异步版本）
    功能增强：
    1. 自动会话生命周期管理
    2. 完善的状态追踪
    3. 增强的错误处理
    4. 支持进度报告
    """

    try:
        # 结构化日志记录
        logger.info(
            f"[CONV][{task_id}] 开始转换任务",
            extra={
                "task_id": task_id,
                "file_count": len(file_paths),
                "job_name": job_name
            }
        )

        async with session_factory() as session:
            # 实际转换处理
            result = await handle_file_conversion(
                session=session,
                task_id=task_id,
                file_paths=file_paths,
                job_name=job_name
            )

        logger.info(f"[CONV][{task_id}] 任务成功完成")
        return result  # 返回转换结果

    except Exception as e:
        error_msg = f"文件转换失败: {str(e)}"
        logger.error(
            f"[CONV][{task_id}] {error_msg}",
            exc_info=True,
            stack_info=True
        )
        print(f"[任务 {task_id}] 文件转换失败: {str(e)}")
        traceback.print_exc()



# ====================== 工具函数 ======================
def extract_file_name(file_path: str) -> str:
    """从文件路径中提取文件名"""
    return os.path.basename(file_path) if file_path else ""


def generate_response(task: Any, request: Any, paired_file_info: List[List[str]] = None):
    """
    生成标准化的 API 响应。
    :param task: 当前任务对象（单文件差分任务时使用）
    :param request: 请求对象，可以是 CompareFilesRequest 或 CompareDirectoriesRequest
    :param paired_file_info: 文件夹差分任务的配对文件信息（仅在文件夹差分任务时使用）
    :return: 标准化的响应数据
    """
    if hasattr(request, 'file_before') and hasattr(request, 'file_after'):
        # 单文件差分任务
        return wrap_response(
            data={
                "task_id": task.task_id,
                "status": "processing",
                "file_paths": {
                    "file_before": request.file_before,
                    "file_after": request.file_after
                }
            },
            msg="文件比较任务已开始"
        )

    elif hasattr(request, 'dir_before') and hasattr(request, 'dir_after') and paired_file_info is not None:
        # 文件夹差分任务
        print(paired_file_info)
        data = {
            "folder_id": task["folder_id"],  # 假设任务对象包含文件夹 ID
            "task_id": paired_file_info[0][2],
            "status": "processing",
            "paired_file_info": paired_file_info,  # 文件配对信息
            "directory_paths": {
                "dir_before": request.dir_before,
                "dir_after": request.dir_after
            }
        }
        print(data)
        return wrap_response(
            data={
                "folder_id": task["folder_id"],  # 假设任务对象包含文件夹 ID
                "task_id": paired_file_info[0][2],
                "status": "processing",
                "paired_file_info": paired_file_info,  # 文件配对信息
                "directory_paths": {
                    "dir_before": request.dir_before,
                    "dir_after": request.dir_after
                }
            },
            msg="文件夹差分任务已开始"
        )

    else:
        raise ValueError("无法生成响应，未知的请求类型或缺少必要参数")


def generate_job_name(request: Any) -> str:
    """
    根据请求类型生成有意义的任务名称。
    :param request: 请求对象，可以是 CompareFilesRequest 或 CompareDirectoriesRequest
    :return: 任务名称
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if hasattr(request, 'file_before') and hasattr(request, 'file_after'):
        # 单文件差分请求
        file1 = os.path.splitext(os.path.basename(request.file_before))[0][:10]
        file2 = os.path.splitext(os.path.basename(request.file_after))[0][:10]
        return f"compare_{file1}_vs_{file2}_{timestamp}"

    elif hasattr(request, 'dir_before') and hasattr(request, 'dir_after'):
        # 文件夹差分请求
        dir1 = os.path.basename(request.dir_before)[:10]
        dir2 = os.path.basename(request.dir_after)[:10]
        return f"compare_dirs_{dir1}_vs_{dir2}_{timestamp}"

    else:
        raise ValueError("无法生成任务名称，未知的请求类型")


# ================== 后台任务运行逻辑 ==================
async def run_diff_task(
        session_factory: sessionmaker,
        task_id: str,
        file_before_path: str,
        file_after_path: str,
        output_result: Optional[object] = None,  # 设置默认值为 None
        ai_judge: Optional[bool] = True
):
    """
    适配后的文件差异分析任务（异步版本）
    优化点：
    1. 完全异步化处理
    2. 自动会话管理
    3. 增强错误处理
    4. 与优先级队列兼容
    """
    try:
        print(f"[DIFF][{task_id}] 开始分析 {file_before_path} vs {file_after_path}")

        async with session_factory() as session:
            # 实际差异分析逻辑
            await run_diff_in_background(
                session=session,
                task_id=task_id,
                file_before_path=file_before_path,
                file_after_path=file_after_path,
                output_result=output_result,
                ai_judge=ai_judge,
            )

        print(f"[DIFF][{task_id}] 分析完成")

    except Exception as e:
        error_msg = f"[DIFF][{task_id}] 分析失败: {str(e)}"
        print(error_msg)
        traceback.print_exc()

        # 记录到数据库（如果需要）
        async with session_factory() as session:
            await _update_task_status(
                session=session,
                task_id=task_id,
                status="failed",
                error=error_msg
            )
        raise  # 重新抛出以便队列处理

def file_conversion_task(session_factory, task_id: str, file_paths: List[str], job_name: str):
    """
    文件转换任务
    """
    try:
        print(f"[任务 {task_id}] 开始文件转换")
        # 显式创建会话
        session = session_factory()
        try:
            # 使用 asyncio.run 执行异步协程
            asyncio.run(handle_file_conversion(
                session=session,
                task_id=task_id,
                file_paths=file_paths,
                job_name=job_name
            ))
        finally:
            # 使用 asyncio.run 执行异步关闭
            asyncio.run(session.close())
        logger.info(f"[任务 {task_id}] 文件转换完成")
    except Exception as e:
        logger.error(f"[任务 {task_id}] 文件转换失败: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"[任务 {task_id}] 文件转换失败: {str(e)}")

async def handle_file_conversion(
    session: Session,
    task_id: str,
    file_paths: List[str],
    job_name: str = None
):
    """同步处理文件转换并存储结果，增强错误处理和日志记录"""
    try:
        # 1. 验证输入文件
        for file_path in file_paths:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

        # 2. 同步执行转换
        logger.info(f"开始转换任务 {task_id}，文件数: {len(file_paths)}")
        result = file_conversion_service.batch_convert(
            file_paths=file_paths,
            job_name=job_name,
        )

        # 3. 检查转换结果是否完整
        if not result.get("converted_files"):
            raise RuntimeError("转换结果为空，可能所有文件均失败")

        # 4. 记录成功状态
        conversion_info = {
            "conversion": {
                "job_id": result["job_id"],
                "output_dir": result["output_dir"],
                "converted_files": result["converted_files"],
                "status": "completed",
                "timestamp": datetime.now().isoformat()
            }
        }
        asyncio.create_task(update_file_comparison_task(
            session=session,
            task_id=task_id,
            extra_info=conversion_info,
            merge_extra_info=True
        ))
        logger.info(f"任务 {task_id} 转换完成，输出目录: {result['output_dir']}")
        return conversion_info

    except Exception as e:
        logger.error(
            f"任务 {task_id} 转换失败: {str(e)}\n"
            f"输入文件列表: {file_paths}\n"
            f"跟踪信息: {traceback.format_exc()}"
        )
        # session.rollback()

        # 记录详细的错误信息
        error_info = {
            "conversion": {
                "status": "failed",
                "error": str(e),
                "failed_files": file_paths,  # 记录所有失败文件
                "timestamp": datetime.now().isoformat()
            }
        }
        try:
            asyncio.create_task(update_file_comparison_task(
                session=session,
                task_id=task_id,
                extra_info=error_info,
                merge_extra_info=True
            ))
        except Exception as update_error:
            logger.critical(f"更新任务状态失败: {str(update_error)}")
            raise RuntimeError("无法更新任务状态") from update_error

        raise  # 重新抛出原始异常




# 执行器：按顺序执行任务
async def execute_tasks(session_factory: sessionmaker, task_id: str, file_before: str, file_after: str, job_name: Optional[str] = None, ai_judge: Optional[bool] = True):
    """
    按顺序执行任务：先转换文件，再计算差异。
    """
    try:
        # 执行文件转换任务
        logger.info(f"[TASK][{task_id}] 开始执行转换任务")
        conversion_result = await run_conversion_task(
            session_factory=session_factory,
            task_id=task_id,
            file_paths=[file_before, file_after],
            job_name=job_name
        )

        # 执行差异计算任务
        logger.info(f"[TASK][{task_id}] 开始执行差异计算任务")
        diff_result = await run_diff_task(
            session_factory=session_factory,
            task_id=task_id,
            output_result=conversion_result,
            file_before_path=file_before,
            file_after_path=file_after,
            ai_judge=ai_judge
        )

        logger.info(f"[TASK][{task_id}] 所有任务完成: {diff_result}")
        return diff_result

    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"[TASK][{task_id}] 任务执行失败: {e}")
        raise

# ===================== 文件比较API ======================
@router.post("/compare-files", response_model=Dict[str, Any])
async def compare_files(
        request: CompareFilesRequest,
        db_session_factory: sessionmaker = Depends(get_db),
):

    """比较两个文件"""
    try:
        # 创建文件比较任务
        async with db_session_factory() as session:
            service = FileComparisonService(session)
            task = await service.upload_files(
                user_id=request.user_id,
                session_id=request.session_id,
                task_type=request.task_type,
                file_before=request.file_before,
                file_after=request.file_after
            )

        # 生成任务名称
        job_name = generate_job_name(request)

        # 添加任务到队列
        task_queue.add_task(
            priority=1,
            task_func=lambda: execute_tasks(
                session_factory=db_session_factory,
                task_id=task.task_id,
                file_before=request.file_before,
                file_after=request.file_after,
                job_name=job_name,
                ai_judge=request.ai_judge,
            )
        )

        return generate_response(task, request)


    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"任务提交失败: {str(e)}")
        return wrap_error_response(
                    error=e,
                    msg="文件对比失败",
                    code=500
                )

# ====================== 文件夹比较API ======================

@router.post("/compare-directories", response_model=Dict[str, Any])
async def compare_directories(
        request: CompareDirectoriesRequest,
        db_session_factory: sessionmaker = Depends(get_db)
):
    """
    比较两个文件夹中的文件。
    """
    try:
        # 创建文件夹比较服务
        async with db_session_factory() as session:
            print("========进到文件夹对比=========")
            service = DirectoryComparisonService(session)
            paired_file_info, folder_id = await service.upload_directories(
                user_id=request.user_id,
                session_id=request.session_id,
                task_type=request.task_type,
                dir_before=request.dir_before,
                dir_after=request.dir_after
            )

        # 检查是否有配对成功的文件
        if not paired_file_info:
            logger.warning("未找到匹配的文件对，任务终止")
            raise wrap_error_response(
                    error="未找到匹配的文件对，任务终止",
                    msg="未找到匹配文件",
                    code=400)

        # 生成任务名称
        # job_name = generate_job_name(request)

        count_priority = 0
        # 提交任务到线程池
        for file_pair in paired_file_info:
            file_before_path, file_after_path, task_id = file_pair
            count_priority +=1
            # 为每个文件对生成独立的任务名称
            job_name = generate_job_name(request)

            # 使用execute_tasks函数按顺序执行转换和差异分析
            task_queue.add_task(
                priority=count_priority,
                task_func=lambda fb=file_before_path, fa=file_after_path: execute_tasks(
                    session_factory=db_session_factory,
                    task_id=task_id,
                    file_before=fb,
                    file_after=fa,
                    job_name=job_name,
                    ai_judge=request.ai_judge
                )
            )

        # 返回响应
        response = generate_response(
            task={"folder_id": folder_id},  # 假设任务对象包含文件夹 ID
            request=request,
            paired_file_info=paired_file_info
        )
        return response

    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"文件夹差分比较任务失败: {e}")
        return wrap_error_response(
            error=e,
            msg="文件夹差分对比失败",
            code=500
        )

# ====================== 任务管理API ======================
# 定义返回数据结构
class TaskResponse(BaseModel):
    id: int
    task_type: str
    status: str
    relative_id: str

class TaskListResponse(BaseModel):
    data: List[TaskResponse]
    msg: str
    code: int

@router.get("/task-list", response_model=Dict[str, Any])
async def display_task_list(
    task_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    relative_id: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    session_factory: sessionmaker = Depends(get_db)  # 保持 session_factory 是 sessionmaker
) -> TaskListResponse:
    """获取任务列表"""
    async with session_factory() as session:  # 显式创建 AsyncSession 实例
        try:
            # 动态构建查询条件
            conditions = []
            if task_type:
                conditions.append(FileComparisonTask.task_type == task_type)
            if status:
                conditions.append(FileComparisonTask.status == status)
            if relative_id:
                conditions.append(FileComparisonTask.relative_id == relative_id)

            # 构建查询
                # 构建查询，默认按创建时间倒序排列
            query = (
                select(FileComparisonTask)
                .where(*conditions)
                .order_by(FileComparisonTask.create_time.desc())  # 按 created_at 倒序
                .offset((page - 1) * page_size)
                .limit(page_size)
            )

            # 执行查询
            result = await session.execute(query)
            tasks = result.scalars().all()

            if not tasks:
                print("没有符合条件的任务")
                return wrap_response(
                    data=[],
                    msg="没有符合条件的任务",
                    code=404
                )

            return wrap_response(
                data=[task.to_dict() for task in tasks],
                msg="任务列表获取成功"
            )
        except Exception as e:
            logger.error(traceback.format_exc())
            logger.error(
                f"获取任务列表失败: {str(e)}, 查询参数: task_type={task_type}, status={status}, relative_id={relative_id}"
            )
            return wrap_error_response(
                error=e,
                msg="获取任务列表失败",
                code=500
            )

@router.get("/task-detail/{task_id}")
async def get_task_detail(
        task_id: str,
        session_factory: sessionmaker = Depends(get_db)  # 保持 session_factory 是 sessionmaker
) -> Dict[str, Any]:
    """
    获取任务详情，包括比较结果和文件转换信息

    参数:
    - task_id: 任务ID

    返回:
    - 标准化的响应对象，包含:
      - code: 状态码
      - success: 是否成功
      - msg: 消息
      - data: 任务详情数据
    """
    async with session_factory() as session:  # 显式创建 AsyncSession 实例
        try:
            logger.info(f"開始查询任务详情，任务ID: {task_id}")

            # 查询任务基本信息
            task = await _get_task_by_id(session, task_id)
            if not task:
                logger.warning(f"未找到任务ID: {task_id}")
                return wrap_error_response(
                    msg=f"未找到ID为 {task_id} 的任务",
                    code=404
                )

            # 构建响应数据
            response_data = _build_task_detail_response(task)
            logger.success(f"成功获取任务详情，任务ID: {task_id}")

            return wrap_response(
                data=response_data,
                msg="任务详情获取成功"
            )

        except Exception as e:
            logger.error(f"查询任务详情失败，任务ID: {task_id}, 错误: {str(e)}")
            logger.error(traceback.format_exc())
            return wrap_error_response(
                error=e,
                msg="查询任务详情失败",
                code=500
            )

# 定义返回的意图识别结构体模型
class IntentRecognitionResponse(BaseModel):
    intent: str  # 用户意图
    workflow: Any  # 工作流信息
    ai_judge_content: str  # AI判定内容
    ai_validate_reason: str  # AI验证原因
    ai_score: float  # AI评分

# 示例数据（模拟的 status_json）
status_json = {
    "intent": "ユーザーはアップロードされたファイルを比較したいと希望しています。まず、データ解析ツールを呼び出し、その後、差分分析ツールを使用して比較を行います。比較が終了した後、AIを使用して判定と検証を行います。最後に、結果をExcel形式のスプレッドシートに変換して提示します。",
    "workflow": "result_sendToFront",  # 替换为实际的工作流データ
    "ai_judge_content": "",
    "ai_validate_reason": "",
    "ai_score": 1.0,
    "task_status": "processing",
    "task_msg":""
}

@router.get("/cot")
async def get_intent_recognition(task_id: str) -> Dict[str, Any]:
    try:
        cot_info = await get_status()

        # 假设返回内容中可能缺失字段，使用默认值防止错误
        status_json = {
            "intent": cot_info.get("intent", "未识别意图"),
            "workflow": cot_info.get("workflow", []),
            "ai_judge_content": cot_info.get("ai_judge_content", "暂无内容"),
            "ai_validate_reason": cot_info.get("ai_validate_reason", "暂无验证原因"),
            "ai_score": cot_info.get("ai_score", 0.0),
            "task_status": cot_info.get("task_status", "failed"),
            "task_msg": cot_info.get("task_msg", "TBD"),
            "task_progress": cot_info.get("task_progress", 0.00)
        }
        return wrap_response(
                data=status_json,
                msg="cot调用成功",
            )
    except Exception as e:
        logger.error(f"cot任务详情失败,错误: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="获取意图识别結果失败",
            code=500
        )

async def _get_task_by_id(session: AsyncSession, task_id: str) -> Optional[FileComparisonTask]:
    """根据ID获取任务"""
    result = await session.execute(
        select(FileComparisonTask)
        .where(FileComparisonTask.task_id == task_id)
    )
    return result.scalars().first()


def _build_task_detail_response(task: FileComparisonTask) -> Dict[str, Any]:
    """构建任务详情响应数据"""
    # 初始化默认值
    default_result = {
        "total_diff": 0,
        "new_items": 0,
        "modified_items": 0,
        "deleted_items": 0,
        "report_name": "",
        "report_path": "",
        "report_detail": {}
    }

    # 检查task.result是否是错误字符串或非预期类型
    if not isinstance(task.result, dict):  # 如果result不是字典类型
        if isinstance(task.result, str) and task.result.startswith("Unexpected error:"):
            result = default_result
        else:
            # 对于其他非字典类型或非预期字符串，统一设置为default_result
           result = default_result
    else:
        result = task.result

    # 获取转换结果并组织页面数据
    conversion_result = _get_conversion_result(task.extra_info)

    original_pages = []
    compared_pages = []
    # 直接从extra_info获取原始文件名判断文档类型
    doc_type = "unknown"
    if hasattr(task, 'extra_info') and task.extra_info:
        # 尝试从extra_info获取fileBefore和fileAfter
        file_before = task.extra_info.get("file_before", "")
        file_after = task.extra_info.get("file_after", "")

        # 优先使用fileBefore判断，如果没有则使用fileAfter
        doc_type = _determine_doc_type_by_extension(
            file_before) if file_before else _determine_doc_type_by_extension(file_after)
    print("=======看看正版的conversion_result结构========")
    print(conversion_result)
    original_pages = []
    compared_pages = []

    if conversion_result and "convertedFiles" in conversion_result:
        converted_files = conversion_result["convertedFiles"]

        # 动态获取两个比较文件的标识（假设只有两个文件比较）
        file_identifiers = list(converted_files.keys())
        if len(file_identifiers) == 2:
            # 获取文件列表和输出目录
            files_a = converted_files[file_identifiers[0]]["files"]
            files_b = converted_files[file_identifiers[1]]["files"]
            output_dir_a = converted_files[file_identifiers[0]]["output_dir"]
            output_dir_b = converted_files[file_identifiers[1]]["output_dir"]

            # 匹配文件
            matched, unmatched_a, unmatched_b = match_files(files_a, files_b)

            # 构建匹配的页面数组
            original_pages = build_pages(list(matched.keys()), output_dir_a)
            compared_pages = build_pages(list(matched.values()), output_dir_b)

            # 处理未匹配的页面
            original_pages.extend(build_pages(unmatched_a, output_dir_a))
            compared_pages.extend(build_pages(unmatched_b, output_dir_b))

    response_data = {
        "taskId": task.task_id,
        "status": task.status,
        "docType": doc_type,
        "taskType": task.task_type,
        "fileBefore": file_before,
        "fileAfter": file_after,
        "createTime": task.create_time,
        "originalPages": original_pages,
        "comparedPages": compared_pages,
        "totalDifferences": result.get("total_diff", 0),
        "newItems": result.get("new_items", 0),
        "modifiedItems": result.get("modified_items", 0),
        "deletedItems": result.get("deleted_items", 0),
        "reportName": result.get("report_name", ""),
        "reportPath": result.get("report_path", ""),
        "diff": extract_diff_from_report(result.get("report_detail", {})),
        "conversionResult": conversion_result
    }
    return response_data

def extract_page_name_2(filename: str) -> str:
    """从文件名中提取页面名称（去掉扩展名）"""
    # 去掉文件扩展名
    base_name = os.path.splitext(filename)[0]

    # 按下划线分割并获取最后一个部分
    if "_" in base_name:
        return base_name.split("_")[-1]
    return base_name  # 如果没有下划线，则返回完整的文件名

def extract_page_name(filename: str) -> str:
    """从文件名中提取页面名称（去掉扩展名）"""
    return os.path.splitext(filename)[0]

def match_files(file_a_list: List[str], file_b_list: List[str]) -> Tuple[Dict[str, str], List[str], List[str]]:
    """
    基于文件名的模糊匹配，找到文件 A 和文件 B 中最相似的页面对。
    返回匹配的文件对、未匹配的文件 A 和未匹配的文件 B。
    """
    matched_pages = {}
    unmatched_a = set(file_a_list)
    unmatched_b = set(file_b_list)

    for file_a in file_a_list:
        # 提取文件 A 的基础页面名称
        page_name_a = extract_page_name(file_a)

        # 在文件 B 中寻找最相似的页面
        close_matches = get_close_matches(page_name_a, [extract_page_name(b) for b in file_b_list], n=1, cutoff=0.6)
        if close_matches:
            matched_page = close_matches[0]
            # 找到对应的文件 B
            file_b = next(b for b in file_b_list if extract_page_name(b) == matched_page)
            matched_pages[file_a] = file_b
            unmatched_a.discard(file_a)
            unmatched_b.discard(file_b)

    # 添加新的调试日志
    logger.debug(f"成功匹配 {len(matched_pages)} 个文件")
    logger.debug(f"未匹配文件 A: {list(unmatched_a)}")
    logger.debug(f"未匹配文件 B: {list(unmatched_b)}")

    return matched_pages, list(unmatched_a), list(unmatched_b)


def build_pages(file_list: List[str], output_dir: str) -> List[Dict[str, str]]:
    """
    构建页面数组，包含页面的 URL 和页面名称。
    """
    return [
        {"url": os.path.join(output_dir, file), "pageName": extract_page_name_2(file)}
        for file in file_list
    ]

def _determine_doc_type_by_extension(filename: str) -> str:
    """
    通过文件扩展名判断文档类型
    :param filename: 文件名（如"Report.xlsx"、"Document.docx"）
    :return: "excel" | "word" | "unknown"
    """
    if not filename:
        return "unknown"

    # 转换为小写方便比较
    filename = filename.lower()

    # 检查常见Excel扩展名
    if filename.endswith(('.xlsx', '.xls', '.xlsm')):
        return "excel"

    # 检查常见Word扩展名
    if filename.endswith(('.docx', '.doc')):
        return "word"

    return "unknown"

def extract_diff_from_report(report_detail: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    从任务结果中提取 report_detail 的 diff 列表并处理

    :param task_result: 包含任务结果的字典
    :return: 处理后的 diff 列表
    """

    # 确保 report_detail 是字典
    if not isinstance(report_detail, dict):
        raise ValueError("report_detail 应该是字典类型，但接收到的类型是: {}".format(type(report_detail)))

    # 提取 diff 列表
    diff_items = report_detail.get("diff", [])

    # 确保 diff 是列表
    if not isinstance(diff_items, list):
        raise ValueError("diff 应该是列表类型，但接收到的类型是: {}".format(type(diff_items)))

    # 使用 _process_diff_items 处理 diff 列表
    return _process_diff_items(diff_items)

def _process_diff_items(diff_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    处理差异项数据

    :param diff_items: 差异项列表，期望是一个包含字典的列表
    :return: 处理后的差异项列表
    """
    processed_diffs = []
    for diff_item in diff_items:
        # 检查列表中的每个元素是否为字典
        if not isinstance(diff_item, dict):
            raise ValueError("diff_item 应该是字典类型，但接收到的类型是: {}".format(type(diff_item)))

        # 处理每个差异项
        processed_diffs.append({
            "type": diff_item.get("type", ""),
            "contentType": diff_item.get("contentType", ""),
            "originText": diff_item.get("originText", ""),
            "originKeyword": diff_item.get("originKeyword", ""),
            "compareText": diff_item.get("compareText", ""),
            "compareKeyword": diff_item.get("compareKeyword", ""),
            "aiJudgeType": diff_item.get("aiJudgeType", ""),
            "aiJudge": diff_item.get("aiJudge", ""),
            "originRegion": diff_item.get("originRegion", []),
            "compareRegion": diff_item.get("compareRegion", [])
        })

    return processed_diffs


def _get_conversion_result(ext_info: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """从ext_info中提取转换结果"""
    if not ext_info or "conversion" not in ext_info:
        return None

    conversion_result = ext_info.get("conversion", {})
    return {
        "jobId": conversion_result.get("job_id", ""),
        "outputDirectory": conversion_result.get("output_dir", ""),
        "convertedFiles": conversion_result.get("converted_files", {}),
        "status": conversion_result.get("status", ""),
        "createdTime": conversion_result.get("timestamp", "")
    }


async def run_diff_in_background(
        session: AsyncSession,
        task_id: str,
        file_before_path: str,
        file_after_path: str,
        output_result:object,
        ai_judge:bool
) -> None:
    """后台执行文件比较并更新数据库"""
    start_time = datetime.now()
    logger.info(f"開始后台比较任务，任务ID: {task_id}")

    try:
        # 更新任务状态为处理中
        await _update_task_status(
            session=session,
            task_id=task_id,
            status="processing",
            start_time=start_time
        )

        # 执行比较
        result = await setup_and_execute_orchestrator(
            file_before_path,
            file_after_path,
            ai_judge=ai_judge,
        )

        # region excel坐标转换
        print("=========这里看conversion_result===========")
        print("=========这里看conversion_result===========")
        print(output_result)

        conversion_result=output_result['conversion']

        original_pages = []
        compared_pages = []

        # 抽取文件拼接
        if conversion_result and "converted_files" in conversion_result:
            converted_files = conversion_result["converted_files"]

            # 动态获取两个比较文件的标识
            file_identifiers = list(converted_files.keys())
            if len(file_identifiers) == 2:
                # 获取文件列表和输出目录
                files_a = converted_files[file_identifiers[0]]["files"]
                files_b = converted_files[file_identifiers[1]]["files"]
                output_dir_a = converted_files[file_identifiers[0]]["output_dir"]
                output_dir_b = converted_files[file_identifiers[1]]["output_dir"]

                # 匹配文件
                matched, unmatched_a, unmatched_b = match_files(files_a, files_b)

                # 构建匹配的页面数组
                original_pages = build_pages(list(matched.keys()), output_dir_a)
                compared_pages = build_pages(list(matched.values()), output_dir_b)

                # 处理未匹配的页面
                original_pages.extend(build_pages(unmatched_a, output_dir_a))
                compared_pages.extend(build_pages(unmatched_b, output_dir_b))

        # print(result)
        print("========这里是page list==========")
        print(original_pages)

        doc_type = _determine_doc_type_by_extension(file_before_path)
        print("========这里是doc_type==========")
        print(doc_type)
        if doc_type=="excel":
            logger.info("======路由到excel坐标，这之前都没问题======")
            updated_diff = batch_calculate_coordinates(
                result['report_detail']['diff'],
                original_pages,  # original 截图路径列表
                compared_pages,  # compared 截图路径列表
                result
            )
            result['report_detail']['diff'] = updated_diff
        else:
            logger.info("======路由到word,到此之前都对======")
            print("result before update:", result)
            # word坐标不准确，暂时注释掉，目前坐标值会出现空字符串的情况
            # before_size = extract_size(original_pages)
            # after_size = extract_size(compared_pages)
            # result = update_result_regions(result, after_size, before_size)
            print("result after update:", result)

        await update_cot({
            "task_status": "success"
        })
        print("=======如果到这里说明update cot了========")
        # 更新任务状态为已完成
        await _update_task_status(
            session=session,
            task_id=task_id,
            status="completed",
            result=result
        )

        logger.success(f"后台比较任务完成，任务ID: {task_id}")

    except Exception as e:
        logger.error(traceback.format_exc())
        logger.error(f"后台比较任务失败，任务ID: {task_id}, 错误: {str(e)}")
        await _update_task_status(
            session=session,
            task_id=task_id,
            status="failed",
            error=str(e)
        )
        raise


async def _update_task_status(
        session: AsyncSession,
        task_id: str,
        status: str,
        start_time: Optional[datetime] = None,
        result: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
) -> None:
    """更新任务状态"""
    update_data = {
        "status": status,
        "update_time": datetime.now()
    }

    if start_time:
        update_data["create_time"] = start_time
    if result:
        update_data["result"] = result
    if error:
        update_data["result"] = error

    await update_file_comparison_task(
        session=session,
        task_id=task_id,
        **update_data
    )
