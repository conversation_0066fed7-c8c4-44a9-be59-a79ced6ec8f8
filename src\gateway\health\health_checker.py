import asyncio
import aiohttp
import logging
from typing import Dict, List
from ..registry.service_registry import service_registry, ServiceInfo

class HealthChecker:
    """健康检查器"""
    def __init__(self, check_interval: int = 30):
        self.check_interval = check_interval
        self._running = False
        self._session = None
    
    async def start(self):
        """启动健康检查"""
        if self._running:
            return
        
        self._running = True
        self._session = aiohttp.ClientSession()
        
        while self._running:
            try:
                await self._check_all_services()
            except Exception as e:
                logging.error(f"Health check failed: {str(e)}")
            
            await asyncio.sleep(self.check_interval)
    
    async def stop(self):
        """停止健康检查"""
        self._running = False
        if self._session:
            await self._session.close()
    
    async def _check_all_services(self):
        """检查所有服务"""
        services = service_registry.get_all_services()
        for service in services:
            await self._check_service(service)
    
    async def _check_service(self, service: ServiceInfo):
        """检查单个服务"""
        try:
            health_url = f"http://{service.host}:{service.port}/health"
            async with self._session.get(health_url, timeout=5) as response:
                if response.status == 200:
                    service.status = "active"
                    service_registry.update_heartbeat(service.service_id)
                else:
                    service.status = "inactive"
                    logging.warning(f"Service health check failed: {service.name} ({service.service_id})")
        except Exception as e:
            service.status = "inactive"
            logging.error(f"Service health check error: {service.name} ({service.service_id}) - {str(e)}")

# 创建全局健康检查器实例
health_checker = HealthChecker() 