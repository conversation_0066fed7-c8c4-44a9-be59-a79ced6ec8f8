import asyncio
from src.services.llm.connect_model import get_intent_from_model
from src.core.agents.worker.manager_worker import ManagerWorker
from src.core.agents.base import AgentContext

# 手动将项目的根目录加入 sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../src")))

from core.agents.worker.manager_worker import load_all_llm_configs

async def test_manager_worker():
    """Test the ManagerWorker agent with intent inference."""
    
    # Config for ManagerWorker
    config = {
        "parameters": {
            "llm_provider": "openai",  # Replace with actual provider
        }
    }
    
    # Create and initialize the ManagerWorker agent
    worker = ManagerWorker(config)
    await worker._setup()

    # Prepare test context (AgentContext)
    context = AgentContext(
        agent_name="manager_worker_test",
        input_data={
            "temperature": 0.7,
            "max_tokens": 100,
        }
    )

    try:
        # Test intent inference by calling the Azure-connected function
        user_message = "1+2+3"
        prompt = f"Attempt to guess the user's intent and return as JSON: {user_message}"
        
        # Call the intent model function
        intent_response = get_intent_from_model(prompt)
        
        print("Intent Response:")
        print(intent_response)

        # Optionally, proceed with ManagerWorker task processing
        response = await worker._process_task(context)
        
        print("Task Processing Response:")
        print(f"Prompt: {response['prompt']}")
        print(f"Generated Response: {response['response']}")
        
    except Exception as e:
        # Print error details
        print(f"Error during testing: {e}")
    
    # Cleanup resources
    await worker._teardown()

# Run the test function
if __name__ == "__main__":
    asyncio.run(test_manager_worker())