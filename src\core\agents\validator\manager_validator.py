from typing import Any, Dict, List
from ..base import AgentContext
from .base_validator import ValidatorAgent
from loguru import logger
from ...usual_func import list_agent

import json

class ListJsonValidator:
    """Class to validate list[json] format and JSON keys."""
    
    def __init__(self, available_agents: List[str]) -> None:
        self.available_agents = available_agents
    
    def validate(self, data: str) -> Dict[str, Any]:
        try:
            parsed_data = json.loads(data)
            if not isinstance(parsed_data, list):
                return {"valid_format": False, "error": "Output is not a list"}
            
            for index, item in enumerate(parsed_data):
                if not isinstance(item, dict):
                    return {"valid_format": False, "error": f"Item at index {index} is not a json"}
                if item["agent_name"] not in self.available_agents:
                    return {"valid_format": False, "error": f"'agent_name' at index {index} is invalid"}
            
            return {"valid_format": True, "details": parsed_data}
        except json.JSONDecodeError:
            return {"valid_format": False, "error": "Invalid JSON format"}


class TextValidatorAgent(ValidatorAgent):
    """A validator agent that validates text processing results."""
    
    async def _setup(self) -> None:
        """Setup text validation resources."""
        await super()._setup()
        self.logger.info("Initializing text validator resources")
    
    async def _teardown(self) -> None:
        """Cleanup text validation resources."""
        await super()._teardown()
        self.logger.info("Cleaning up text validator resources")
    
    async def _initialize_rules(self) -> None:
        """Initialize validation rules."""
        self.validation_rules = [
            {
                "name": "json_format",
                "description": "Check if text is in json format",
            }
        ]
        self.logger.info("Initialized text validation rules")
    
    async def _validate_rule(self, rule: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Validate a single rule against the input data."""
        rule_name = rule.get("name")
        text = context.input_data.get("text", "")
        
        if not text:
            return {
                "rule": rule_name,
                "passed": False,
                "confidence": 0.0,
                "error": "No text provided for validation"
            }
        
        try:
            if rule_name == "list_json_format":
                available_agents = await list_agents()  # Fetch available agent names dynamically
                validator = ListJsonValidator(available_agents)
                validation_result = validator.validate(text)
                passed = validation_result["valid_format"]
                return {
                    "rule": rule_name,
                    "passed": passed,
                    "confidence": 1.0,
                    "details": validation_result
                }
            else:
                raise ValueError(f"Unknown validation rule: {rule_name}")
            
            return {
                "rule": rule_name,
                "passed": passed,
                "confidence": confidence,
                "details": {
                    "text_length": len(text),
                    "word_count": len(text.split())
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error validating rule {rule_name}: {str(e)}")
            return {
                "rule": rule_name,
                "passed": False,
                "confidence": 0.0,
                "error": str(e)
            } 