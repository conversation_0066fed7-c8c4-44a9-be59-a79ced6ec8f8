from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Optional, Any
from datetime import datetime
from loguru import logger

from src.gateway.registry.service_registry import service_registry, ServiceInfo
from src.gateway.registry.agent_registry import agent_registry
from src.gateway.models.agent import AgentInfo, AgentCapability
from src.gateway.router.load_balancer import service_router
from src.common.exceptions import NotFoundError, ValidationError
from src.services.response import wrap_response, wrap_error_response

router = APIRouter(
    prefix="/gateway",
    tags=["gateway"],
    responses={404: wrap_error_response(msg="Not found", code=404)},
)


# ====================== 服务注册相关接口 ======================
@router.post("/register", response_model=Dict[str, Any])
async def register_service(service: ServiceInfo) -> Dict[str, Any]:
    """
    注册新服务

    参数:
    - service: 服务信息

    返回:
    - 标准化响应，包含操作结果
    """
    try:
        logger.info(f"开始注册服务: {service.service_id}")
        service_registry.register(service)
        logger.success(f"服务注册成功: {service.service_id}")
        return wrap_response(
            data={"service_id": service.service_id},
            msg="服务注册成功"
        )
    except ValidationError as e:
        logger.warning(f"服务注册验证失败: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="服务注册验证失败",
            code=400
        )
    except Exception as e:
        logger.error(f"服务注册失败: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="服务注册失败",
            code=500
        )


@router.delete("/unregister/{service_id}", response_model=Dict[str, Any])
async def unregister_service(service_id: str) -> Dict[str, Any]:
    """
    注销服务

    参数:
    - service_id: 服务ID

    返回:
    - 标准化响应，包含操作结果
    """
    try:
        logger.info(f"开始注销服务: {service_id}")
        service_registry.unregister(service_id)
        logger.success(f"服务注销成功: {service_id}")
        return wrap_response(
            msg="服务注销成功"
        )
    except NotFoundError as e:
        logger.warning(f"服务未找到: {service_id}")
        return wrap_error_response(
            error=e,
            msg=f"服务未找到: {service_id}",
            code=404
        )
    except Exception as e:
        logger.error(f"服务注销失败: {service_id}, 错误: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="服务注销失败",
            code=500
        )


@router.get("/services", response_model=Dict[str, Any])
async def list_services() -> Dict[str, Any]:
    """获取所有注册服务列表"""
    try:
        services = service_registry.get_all_services()
        logger.info(f"获取到 {len(services)} 个服务")
        return wrap_response(
            data={"services": services},
            msg="服务列表获取成功"
        )
    except Exception as e:
        logger.error(f"获取服务列表失败: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="获取服务列表失败",
            code=500
        )


@router.get("/services/{service_name}", response_model=Dict[str, Any])
async def get_services_by_name(service_name: str) -> Dict[str, Any]:
    """根据服务名称获取服务信息"""
    try:
        services = service_registry.get_services_by_name(service_name)
        if not services:
            logger.warning(f"未找到服务: {service_name}")
            return wrap_error_response(
                msg=f"未找到服务: {service_name}",
                code=404
            )
        return wrap_response(
            data={"services": services},
            msg="服务查询成功"
        )
    except Exception as e:
        logger.error(f"查询服务失败: {service_name}, 错误: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="服务查询失败",
            code=500
        )


@router.get("/route/{service_name}", response_model=Dict[str, Any])
async def route_to_service(
        service_name: str,
        strategy: str = "round_robin"
) -> Dict[str, Any]:
    """
    路由到指定服务

    参数:
    - service_name: 服务名称
    - strategy: 路由策略 (默认: round_robin)

    返回:
    - 标准化响应，包含路由结果
    """
    try:
        logger.info(f"开始路由请求，服务: {service_name}, 策略: {strategy}")
        service = service_router.route(service_name, strategy)
        if not service:
            logger.warning(f"没有可用的服务: {service_name}")
            return wrap_error_response(
                msg=f"没有可用的服务: {service_name}",
                code=404
            )
        logger.success(f"路由成功，服务: {service_name}")
        return wrap_response(
            data={"service": service},
            msg="路由成功"
        )
    except Exception as e:
        logger.error(f"路由失败: {service_name}, 错误: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="路由失败",
            code=500
        )


# ====================== Agent注册相关接口 ======================
@router.post("/agents/register", response_model=Dict[str, Any])
async def register_agent(agent: AgentInfo) -> Dict[str, Any]:
    """
    注册新Agent

    参数:
    - agent: Agent信息

    返回:
    - 标准化响应，包含操作结果
    """
    try:
        logger.info(f"开始注册Agent: {agent.agent_id}")
        agent_registry.register(agent)
        logger.success(f"Agent注册成功: {agent.agent_id}")
        return wrap_response(
            data={"agent_id": agent.agent_id},
            msg="Agent注册成功"
        )
    except ValidationError as e:
        logger.warning(f"Agent注册验证失败: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="Agent注册验证失败",
            code=400
        )
    except Exception as e:
        logger.error(f"Agent注册失败: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="Agent注册失败",
            code=500
        )


@router.delete("/agents/unregister/{agent_id}", response_model=Dict[str, Any])
async def unregister_agent(agent_id: str) -> Dict[str, Any]:
    """
    注销Agent

    参数:
    - agent_id: Agent ID

    返回:
    - 标准化响应，包含操作结果
    """
    try:
        logger.info(f"开始注销Agent: {agent_id}")
        agent_registry.unregister(agent_id)
        logger.success(f"Agent注销成功: {agent_id}")
        return wrap_response(
            msg="Agent注销成功"
        )
    except NotFoundError as e:
        logger.warning(f"Agent未找到: {agent_id}")
        return wrap_error_response(
            error=e,
            msg=f"Agent未找到: {agent_id}",
            code=404
        )
    except Exception as e:
        logger.error(f"Agent注销失败: {agent_id}, 错误: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="Agent注销失败",
            code=500
        )


@router.get("/agents", response_model=Dict[str, Any])
async def list_agents() -> Dict[str, Any]:
    """获取所有注册Agent列表"""
    try:
        agents = agent_registry.get_all_agents()
        logger.info(f"获取到 {len(agents)} 个Agent")
        return wrap_response(
            data={"agents": agents},
            msg="Agent列表获取成功"
        )
    except Exception as e:
        logger.error(f"获取Agent列表失败: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="获取Agent列表失败",
            code=500
        )


@router.get("/agents/capability/{capability}", response_model=Dict[str, Any])
async def get_agents_by_capability(capability: AgentCapability) -> Dict[str, Any]:
    """
    获取支持指定能力的Agent列表

    参数:
    - capability: 需要查询的能力类型

    返回:
    - 标准化响应，包含:
      - code: 状态码
      - success: 是否成功
      - msg: 消息
      - data: Agent列表数据
    """
    try:
        logger.info(f"开始查询支持能力 {capability.value} 的Agent")
        agents = agent_registry.get_agents_by_capability(capability)

        if not agents:
            logger.warning(f"未找到支持能力 {capability.value} 的Agent")
            return wrap_error_response(
                msg=f"未找到支持能力 {capability.value} 的Agent",
                code=404
            )

        logger.success(f"成功找到 {len(agents)} 个支持能力 {capability.value} 的Agent")
        return wrap_response(
            data={"agents": agents},
            msg=f"成功获取支持 {capability.value} 能力的Agent列表"
        )

    except Exception as e:
        logger.error(f"查询Agent能力失败: {capability.value}, 错误: {str(e)}")
        return wrap_error_response(
            error=e,
            msg=f"查询支持 {capability.value} 能力的Agent失败",
            code=500
        )


@router.get("/agents/{agent_id}", response_model=Dict[str, Any])
async def get_agent(agent_id: str) -> Dict[str, Any]:
    """
    获取指定Agent的详细信息

    参数:
    - agent_id: 需要查询的Agent ID

    返回:
    - 标准化响应，包含Agent详细信息
    """
    try:
        logger.info(f"开始查询Agent详情: {agent_id}")
        agent = agent_registry.get_agent(agent_id)
        logger.success(f"成功获取Agent详情: {agent_id}")

        return wrap_response(
            data={"agent": agent},
            msg="Agent详情获取成功"
        )

    except NotFoundError as e:
        logger.warning(f"Agent未找到: {agent_id}")
        return wrap_error_response(
            error=e,
            msg=f"Agent未找到: {agent_id}",
            code=404
        )
    except Exception as e:
        logger.error(f"获取Agent详情失败: {agent_id}, 错误: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="获取Agent详情失败",
            code=500
        )


@router.post("/agents/{agent_id}/heartbeat", response_model=Dict[str, Any])
async def update_agent_heartbeat(agent_id: str) -> Dict[str, Any]:
    """
    更新Agent心跳时间

    参数:
    - agent_id: 需要更新心跳的Agent ID

    返回:
    - 标准化响应，包含操作结果
    """
    try:
        logger.info(f"开始更新Agent心跳: {agent_id}")
        agent_registry.update_heartbeat(agent_id)
        logger.success(f"Agent心跳更新成功: {agent_id}")

        return wrap_response(
            data={"agent_id": agent_id, "last_heartbeat": datetime.now().isoformat()},
            msg="Agent心跳更新成功"
        )

    except NotFoundError as e:
        logger.warning(f"Agent未找到，无法更新心跳: {agent_id}")
        return wrap_error_response(
            error=e,
            msg=f"Agent未找到: {agent_id}",
            code=404
        )
    except Exception as e:
        logger.error(f"更新Agent心跳失败: {agent_id}, 错误: {str(e)}")
        return wrap_error_response(
            error=e,
            msg="更新Agent心跳失败",
            code=500
        )
