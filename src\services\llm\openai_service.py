import os
from typing import Any, Dict, Optional
import openai
from .base import BaseLLMService, LLMConfig, LLMResponse
from loguru import logger

class OpenAIService(BaseLLMService):
    """OpenAI LLM service implementation."""

    async def _setup(self) -> None:
        """Setup OpenAI client."""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable not set")

        openai.api_key = api_key
        self.logger.info("OpenAI client initialized")

    async def _teardown(self) -> None:
        """Cleanup OpenAI resources."""
        # OpenAI client doesn't require cleanup
        pass

    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate text using OpenAI."""
        try:
            # Merge default config with kwargs
            params = {
                "model": self.config.model,
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
                "top_p": self.config.top_p,
                "frequency_penalty": self.config.frequency_penalty,
                "presence_penalty": self.config.presence_penalty,
                **kwargs
            }

            # Make API call
            response = await openai.ChatCompletion.acreate(
                messages=[{"role": "user", "content": prompt}],
                **params
            )

            # Extract response
            text = response.choices[0].message.content
            usage = {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }

            return LLMResponse(
                text=text,
                usage=usage,
                model=response.model,
                metadata={
                    "finish_reason": response.choices[0].finish_reason
                }
            )

        except Exception as e:
            self.logger.error(f"Error generating text: {str(e)}")
            raise