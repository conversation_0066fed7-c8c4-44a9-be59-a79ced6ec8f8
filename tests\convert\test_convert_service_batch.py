from word_convert_service import WordConvertService
from excel_convert_service import ExcelConvertService
import os


def batch_convert_files():
    """批量转换文件示例"""
    # 配置输入输出目录
    input_dir = "D:/input_files"
    output_dir = "D:/converted_images"
    temp_dir = "D:/temp_conversion"  # 自定义临时目录

    # 要处理的文件列表
    files_to_convert = [
        ("word", "contract.docx"),
        ("word", "proposal.docx"),
        ("excel", "data.xlsx"),
        ("excel", "results.xlsx")
    ]

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(temp_dir, exist_ok=True)

    for file_type, filename in files_to_convert:
        input_path = os.path.join(input_dir, filename)
        output_prefix = os.path.join(output_dir, os.path.splitext(filename)[0])

        try:
            if file_type == "word":
                with WordConvertService(temp_dir=temp_dir) as converter:
                    print(f"\n正在转换Word文件: {filename}")
                    png_files = converter.convert_to_images(
                        input_path,
                        output_prefix=output_prefix,
                        format="png"
                    )
                    print(f"生成 {len(png_files)} 张图片")

            elif file_type == "excel":
                with ExcelConvertService(temp_dir=temp_dir) as converter:
                    print(f"\n正在转换Excel文件: {filename}")
                    # 转换所有工作表
                    for sheet_idx in range(3):  # 假设最多3个工作表
                        try:
                            jpg_files = converter.convert_to_images(
                                input_path,
                                output_prefix=f"{output_prefix}_sheet{sheet_idx + 1}",
                                format="jpg",
                                sheet_index=sheet_idx
                            )
                            print(f"工作表 {sheet_idx + 1} 生成 {len(jpg_files)} 张图片")
                        except Exception as e:
                            print(f"工作表 {sheet_idx + 1} 转换失败: {str(e)}")
                            continue

        except Exception as e:
            print(f"\n文件 {filename} 转换失败: {str(e)}")
            continue


if __name__ == "__main__":
    batch_convert_files()
