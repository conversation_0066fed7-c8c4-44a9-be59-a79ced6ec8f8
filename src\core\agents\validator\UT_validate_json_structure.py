import unittest
from typing import Dict, Any, <PERSON><PERSON>, Optional, Union
from src.core.agents.validator.test_validate import validate_json_structure


class TestValidateJsonStructure(unittest.TestCase):
    def assert_result(
        self,
        result: Tuple[bool, Optional[Dict], str],
        expected_success: bool,
        expected_data: Optional[Dict] = None,
        expected_error: str = ""
    ):
        success, data, error = result
        self.assertEqual(success, expected_success)
        if expected_data is not None:
            self.assertEqual(data, expected_data)
        else:
            self.assertIsNone(data)
        self.assertEqual(error, expected_error)

    def test_valid_json_str(self):
        """输入为合法 JSON 字符串"""
        json_input = '{"name": "Alice", "age": 30}'
        schema = {
            "name": {"type": str, "required": True},
            "age": {"type": int}
        }
        result = validate_json_structure(json_input, schema)
        self.assert_result(result, True, {"name": "<PERSON>", "age": 30})

    def test_valid_json_bytes(self):
        """输入为合法 JSON bytes"""
        json_input = b'{"key": "value"}'
        schema = {"key": {"type": str}}
        result = validate_json_structure(json_input, schema)
        self.assert_result(result, True, {"key": "value"})

    def test_invalid_utf8_bytes(self):
        """输入为非 UTF-8 编码的 bytes"""
        invalid_bytes = "你好".encode("gbk")
        result = validate_json_structure(invalid_bytes, {})
        self.assert_result(result, False, None, "输入为 bytes 类型，但无法使用 UTF-8 解码")

    def test_invalid_json_format(self):
        """输入为非法 JSON 字符串"""
        result = validate_json_structure("{name: \"Alice\"}", {})
        self.assert_result(result, False, None, "JSON 解析失败: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)")

    def test_missing_required_field(self):
        """缺少必要字段"""
        schema = {"name": {"type": str, "required": True}}
        result = validate_json_structure("{}", schema)
        self.assert_result(result, False, None, "缺少必要字段: name")

    def test_field_type_mismatch(self):
        """字段类型不匹配"""
        schema = {"age": {"type": int}}
        result = validate_json_structure('{"age": "thirty"}', schema)
        self.assert_result(result, False, None, "字段 'age' 类型错误，期望 int。")

    def test_union_type_match_int_or_str(self):
        """联合类型匹配成功"""
        schema = {"value": {"type": (int, str)}}
        result1 = validate_json_structure('{"value": 123}', schema)
        self.assert_result(result1, True, {"value": 123})
        result2 = validate_json_structure('{"value": "abc"}', schema)
        self.assert_result(result2, True, {"value": "abc"})

    def test_explicit_required_fields(self):
        """显式提供 required_fields 参数"""
        schema = {
            "name": {"type": str, "required": False},
            "age": {"type": int, "required": False}
        }
        required_fields = ["name"]
        result = validate_json_structure('{"age": 20}', schema, required_fields)
        self.assert_result(result, False, None, "缺少必要字段: name")


if __name__ == '__main__':
    unittest.main()