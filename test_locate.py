import json
from PIL import Image, ImageDraw
import os


def batch_calculate_coordinates(diff, original_pages, compared_pages, result):
    """
    批量计算 diff 数据里 origin 和 compare 坐标的 xywh。

    :param diff: `result['report_detail']['diff']` 列表
    :param original_pages: 保存 original 的截图路径列表
    :param compared_pages: 保存 compared 的截图路径列表
    :param result: 数据结构，包含 old_sheet 和 new_sheet
    :param process_and_draw_rectangles: 用于计算新 xywh 的函数
    :return: 更新的 diff 列表
    """

    print("===这段调用了==")
    for entry in diff:
        # 处理 originRegion
        if 'originRegion' in entry and entry['originRegion']:
            for region in entry['originRegion']:
                page_name = region['page']  # 获取页面名称
                # 匹配 original_image_path，根据 pageName 查找
                original_entry = next((item for item in original_pages if item['pageName'] == page_name), None)

                if original_entry:
                    old_image_path = original_entry['url']
                    print(old_image_path)

                    # 从 result 中找到对应的表单 sheet_info
                    sheet_info = next(
                        (sheet[page_name] for sheet in result['report_detail']['old_sheet'] if page_name in sheet),
                        None)
                    if sheet_info:
                        sheet_info_2 = [sheet_info['x'], sheet_info['y']]

                        # 调用处理函数重新计算 xywh
                        x, y, w, h = region['x'], region['y'], region['width'], region['height']

                        region['x'], region['y'], region['width'], region['height'] = process_and_draw_rectangles(
                            old_image_path, [x, y, w, h], sheet_info_2
                        )

        # 处理 compareRegion
        if 'compareRegion' in entry and entry['compareRegion']:
            for region in entry['compareRegion']:
                page_name = region['page']  # 获取页面名称

                # 匹配 compare_image_path，根据 pageName 查找
                compared_entry = next((item for item in compared_pages if item['pageName'] == page_name), None)
                if compared_entry:
                    new_image_path = compared_entry['url']
                    print(new_image_path)

                    # 从 result 中找到对应的表单 sheet_info
                    sheet_info = next(
                        (sheet[page_name] for sheet in result['report_detail']['new_sheet'] if page_name in sheet),
                        None)
                    if sheet_info:
                        sheet_info_2 = [sheet_info['x'], sheet_info['y']]

                        # 调用处理函数重新计算 xywh
                        x, y, w, h = region['x'], region['y'], region['width'], region['height']
                        region['x'], region['y'], region['width'], region['height'] = process_and_draw_rectangles(
                            new_image_path, [x, y, w, h], sheet_info_2
                        )

    return diff

def process_and_draw_rectangles(old_image_path, origin_position, sheet_info):
    """
    从 JSON 文件中提取位置信息，并在指定图片上绘制矩形框，分别保存标注完成后的图片。
    注意这里的处理以一个表单为单位

    Args:
        json_file_path (str): JSON 文件的路径。
        old_image_path (str): 文件截图的路径。
        origin_position : [x,y,w,h]
        sheet_info (List[x_start,y_start]): sheet_info中的左上角相对位置坐标，每个表单只有一对
    """

    # 定义函数，找到左上角和右下角第一个非空白像素
    def find_non_blank_bounds(image: Image.Image) -> tuple:
        """
        找到图像中非白色区域的边界（以左上角为 (0,0) 坐标系开始计算）
        :param image: 输入的 RGBA 模式图像
        :return: (left, top, right, bottom) 非白色区域的边界，从左上角 (0,0) 起计算
        """
        # 获取图像宽度、高度
        width, height = image.size
        pixels = image.load()  # 加载像素数据

        # 初始化边界值
        top, left = height, width  # 初始顶部和左侧定义为最大值
        bottom, right = 0, 0  # 初始底部和右侧定义为最小值

        # 遍历图像的每个像素点，寻找非白色区域边界
        for y in range(height):  # 从顶部到底部
            for x in range(width):  # 从左侧到右侧
                r, g, b, a = pixels[x, y]  # 获取像素的 RGBA 值
                # 判断像素是否为白色区域，白色定义为 RGB(255,255,255)
                if not (r == 255 and g == 255 and b == 255):  # 如果不是白色
                    top = min(top, y)  # 确定顶部坐标
                    left = min(left, x)  # 确定左侧坐标
                    bottom = max(bottom, y)  # 确定底部坐标
                    right = max(right, x)  # 确定右侧坐标

        # 检查是否找到边界（避免空图像或全白图像）
        if top == height or left == width:
            raise ValueError("The image does not contain any non-blank area.")

        # 返回边界，使用左上角为 (0,0) 的坐标系
        return left, top, right, bottom

    # 返回两组 x y w h
    # 这里的新旧文件地址是要求 截图的两个文件 算上下左右边距
    def draw_rectangles_on_images(old_image_path, position_data, x_start, y_start):

        #result_position = []

        try:
            x_start = 0.01824817518248175
            y_start = 0.015775635407537247

            # 打开图片
            old_image = Image.open(old_image_path).convert("RGBA")  # 将图片转换为支持透明层的 RGBA 模式

            # 获取图片宽度和高度
            old_width, old_height = old_image.size

            # 获取两个图片的非空白边界
            old_left, old_top, old_right, old_bottom = find_non_blank_bounds(old_image)

            # 捕捉最大宽度和高度, 当前页面左上角第一个非空像素
            max_width = old_right - old_left
            max_height = old_bottom - old_top

            x_0, y_0, w_0, h_0 = [0.017919306054899276,0.9388067312595615,0.9820806939451008,0.2447730749617542]

            x = int((x_0 - x_start) / (1 - x_start) * max_width) + old_left
            w = int(w_0 / (1 - x_start) * max_width)
            y = int((y_0 - y_start) / (1 - y_start) * max_height) + old_top
            h = int(h_0 / (1 - y_start) * max_height)

            # 在图片上绘制矩形框
            draw = ImageDraw.Draw(old_image)
            rectangle_coords = (x, y, x + w, y + h)  # 左上角和右下角的绝对坐标
            draw.rectangle(rectangle_coords, outline="red", width=3)  # 绘制矩形框，红色边框，边框宽度为3像素

            # 保存修改后的图片（可选）
            old_image.save("output_with_rectangle.png")  # 保存绘制了矩形的图片到文件

            # 将绝对坐标转换为相对坐标
            relative_x = x / old_width
            relative_y = y / old_height
            relative_w = w / old_width
            relative_h = h / old_height

            print("==========excel新相对坐标========")
            print(relative_x,relative_y,relative_w,relative_h)
            return [relative_x,relative_y,relative_w,relative_h]
            #return [x,y,w,h]
        except Exception as e:
            print(f"Error occurred: {e}")

    x_start, y_start = sheet_info[0], sheet_info[1]

    positions = origin_position

    transfer_position = draw_rectangles_on_images(old_image_path, positions,
                                                  x_start=x_start, y_start=y_start)
    if not transfer_position:
        transfer_position = positions

    return transfer_position
def find_non_blank_bounds(image: Image.Image) -> tuple:
    """
    找到图像中非白色区域的边界（以左上角为 (0,0) 坐标系开始计算）
    :param image: 输入的 RGBA 模式图像
    :return: (left, top, right, bottom) 非白色区域的边界，从左上角 (0,0) 起计算
    """
    # 获取图像宽度、高度
    width, height = image.size
    pixels = image.load()  # 加载像素数据

    # 初始化边界值
    top, left = height, width  # 初始顶部和左侧定义为最大值
    bottom, right = 0, 0  # 初始底部和右侧定义为最小值

    # 遍历图像的每个像素点，寻找非白色区域边界
    for y in range(height):  # 从顶部到底部
        for x in range(width):  # 从左侧到右侧
            r, g, b, a = pixels[x, y]  # 获取像素的 RGBA 值
            # 判断像素是否为白色区域，白色定义为 RGB(255,255,255)
            if not (r == 255 and g == 255 and b == 255):  # 如果不是白色
                top = min(top, y)  # 确定顶部坐标
                left = min(left, x)  # 确定左侧坐标
                bottom = max(bottom, y)  # 确定底部坐标
                right = max(right, x)  # 确定右侧坐标

    # 检查是否找到边界（避免空图像或全白图像）
    if top == height or left == width:
        raise ValueError("The image does not contain any non-blank area.")

    # 返回边界，使用左上角为 (0,0) 的坐标系
    return left, top, right, bottom

def draw_rectangles_on_images(old_image_path, position_data, x_start, y_start):

    #result_position = []

    try:
        #x_start = 0.01824817518248175
        x_start = 0.017919306054899276
        y_start = 0.015775635407537247

        # 打开图片
        old_image = Image.open(old_image_path).convert("RGBA")  # 将图片转换为支持透明层的 RGBA 模式

        # 获取图片宽度和高度
        old_width, old_height = old_image.size

        # 获取两个图片的非空白边界
        old_left, old_top, old_right, old_bottom = find_non_blank_bounds(old_image)

        # 捕捉最大宽度和高度, 当前页面左上角第一个非空像素
        max_width = old_right - old_left
        max_height = old_bottom - old_top

        draw = ImageDraw.Draw(old_image)
        rectangle_coords = (old_left, old_top, old_left + max_width, old_top + max_height)  # 左上角和右下角的绝对坐标
        draw.rectangle(rectangle_coords, outline="blue", width=3)

        draw = ImageDraw.Draw(old_image)
        rectangle_coords = (x_start*old_width+old_left, y_start*old_height+old_top, x_start*old_width + max_width+old_left, y_start*old_height + max_height+old_top)  # 左上角和右下角的绝对坐标
        draw.rectangle(rectangle_coords, outline="orange", width=3)
        #x_0, y_0, w_0, h_0 = [0.017919306054899276, 0.9388067312595615, 0.9820806939451008, 0.2447730749617542] # 32-A-C1 ADD
        #x_0, y_0, w_0, h_0 = [0.017919306054899276,0.8944416114227435,0.9820806939451008,0.05507394186639469] # 28-A-C1 ADD
        x_0, y_0, w_0, h_0 = [0.017919306054899276,0.7942376338602753,0.08366147349198197,0.07266700662927078] # 27-A-C1 update
        x = int((x_0 - x_start) / (1 - x_start) * max_width) + old_left
        w = int(w_0 / (1 - x_start) * max_width)
        y = int((y_0 - y_start) / (1 - y_start) * max_height) + old_top
        h = int(h_0 / (1 - y_start) * max_height)

        # 在图片上绘制矩形框
        draw = ImageDraw.Draw(old_image)
        rectangle_coords = (x, y, x + w, y + h)  # 左上角和右下角的绝对坐标
        draw.rectangle(rectangle_coords, outline="red", width=3)  # 绘制矩形框，红色边框，边框宽度为3像素
        old_image.show()

        # 将绝对坐标转换为相对坐标
        relative_x = x / old_width
        relative_y = y / old_height
        relative_w = w / old_width
        relative_h = h / old_height

        print("==========excel新相对坐标========")
        print(relative_x,relative_y,relative_w,relative_h)
        return [relative_x,relative_y,relative_w,relative_h]
        #return [x,y,w,h]
    except Exception as e:
        print(f"Error occurred: {e}")
if __name__ == "__main__":
    sheet_position = {
        "x": 0.01824817518248175,
        "y": 0.015775635407537247,
        "width": 0.08394160583941605,
        "height": 0.022787028921998246
    }

    #result = json.loads(result_2)
    result = [{'page': '改定履歴', 'x': 0.017919306054899276, 'y': 0.9388067312595615, 'width': 0.9820806939451008, 'height': 0.2447730749617542},
     {'page': '改定履歴', 'x': 0.017919306054899276, 'y': 0.9082100968893422, 'width': 0.9820806939451008, 'height': 0.1223865374808771},
     {'page': '改定履歴', 'x': 0.017919306054899276, 'y': 0.8806731259561448, 'width': 0.9820806939451008, 'height': 0.05507394186639469},
     {'page': '改定履歴', 'x': 0.017919306054899276, 'y': 0.8944416114227435, 'width': 0.9820806939451008, 'height': 0.05507394186639469},
     {'page': '改定履歴', 'x': 0.10158077954688124, 'y': 0.7942376338602753, 'width': 0.12018489984591679, 'height': 0.07266700662927078},
     {'page': '改定履歴', 'x': 0.017919306054899276, 'y': 0.8669046404895462, 'width': 0.9820806939451008, 'height': 0.05507394186639469}]

    compared_pages = "D:\\Users\\kotei\\Desktop\\test_file\\before_excel_pic\\MET-M_DEVILST-CORG-0-27-A-C1-XXX_改定履歴.png"
    draw_rectangles_on_images(compared_pages,[],0,0)