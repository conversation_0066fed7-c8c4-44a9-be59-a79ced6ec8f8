import asyncio
import websockets

async def websocket_client():
    uri = "ws://localhost:8000/ws"
    async with websockets.connect(uri,ping_timeout=120) as websocket:
        print("Connected to server")
        while True:
            try:
                await websocket.send("Hello Server!")  # 主动发送消息
                message = await websocket.recv()
                if message!="Message received: Hello Server!":
                    print(f"Received: {message}")
            except Exception as e:
                print(f"WebSocket connection error: {e}")
                break

asyncio.run(websocket_client())