# Bibrain API 参考文档

## 1. 概述

本文档详细说明 Bibrain 平台的 API 接口，包括 Agent 管理、任务调度、监控等功能的接口定义。

## 2. 基础信息

### 2.1 基础 URL

```
开发环境：http://localhost:8000
测试环境：https://test-api.bibrain.com
生产环境：https://api.bibrain.com
```

### 2.2 认证方式

所有 API 请求都需要在 Header 中包含 API Key：

```
Authorization: Bearer your_api_key
```

### 2.3 响应格式

所有 API 响应都遵循以下格式：

```json
{
  "code": 0,           // 状态码，0 表示成功
  "message": "success", // 状态描述
  "data": {            // 响应数据
    // 具体数据字段
  }
}
```

## 3. Agent 管理 API

### 3.1 注册 Agent

```http
POST /api/v1/agents

请求体：
{
  "name": "weather_agent",
  "version": "1.0.0",
  "description": "天气查询 Agent",
  "capabilities": [
    {
      "name": "get_weather",
      "description": "获取天气信息",
      "input_schema": "schemas/input.json",
      "output_schema": "schemas/output.json"
    }
  ],
  "resources": {
    "max_memory": "256Mi",
    "max_cpu": "0.2"
  }
}

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "agent_id": "agent_123",
    "status": "registered"
  }
}
```

### 3.2 更新 Agent

```http
PUT /api/v1/agents/{agent_id}

请求体：
{
  "version": "1.0.1",
  "description": "更新后的描述",
  "capabilities": [
    // 更新的能力定义
  ]
}

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "agent_id": "agent_123",
    "status": "updated"
  }
}
```

### 3.3 删除 Agent

```http
DELETE /api/v1/agents/{agent_id}

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "agent_id": "agent_123",
    "status": "deleted"
  }
}
```

### 3.4 获取 Agent 列表

```http
GET /api/v1/agents?page=1&size=10

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "size": 10,
    "agents": [
      {
        "agent_id": "agent_123",
        "name": "weather_agent",
        "version": "1.0.0",
        "status": "running"
      }
    ]
  }
}
```

## 4. 任务管理 API

### 4.1 创建任务

```http
POST /api/v1/tasks

请求体：
{
  "agent_id": "agent_123",
  "capability": "get_weather",
  "input": {
    "city": "Beijing",
    "date": "2024-03-20"
  }
}

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "task_id": "task_456",
    "status": "created"
  }
}
```

### 4.2 获取任务状态

```http
GET /api/v1/tasks/{task_id}

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "task_id": "task_456",
    "status": "completed",
    "result": {
      "temperature": 25,
      "humidity": 60,
      "weather": "sunny"
    }
  }
}
```

### 4.3 取消任务

```http
POST /api/v1/tasks/{task_id}/cancel

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "task_id": "task_456",
    "status": "cancelled"
  }
}
```

## 5. 监控 API

### 5.1 获取 Agent 状态

```http
GET /api/v1/agents/{agent_id}/status

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "agent_id": "agent_123",
    "status": "healthy",
    "metrics": {
      "cpu_usage": 0.2,
      "memory_usage": "128Mi",
      "request_count": 1000,
      "error_count": 5
    }
  }
}
```

### 5.2 获取系统指标

```http
GET /api/v1/metrics?type=system&start_time=2024-03-20T00:00:00Z&end_time=2024-03-20T23:59:59Z

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "metrics": [
      {
        "timestamp": "2024-03-20T00:00:00Z",
        "cpu_usage": 0.3,
        "memory_usage": "1Gi",
        "request_count": 100
      }
    ]
  }
}
```

## 6. 配置管理 API

### 6.1 更新配置

```http
PUT /api/v1/config

请求体：
{
  "agent_id": "agent_123",
  "config": {
    "weather_api": {
      "base_url": "https://api.weather.com",
      "timeout": 5
    }
  }
}

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "agent_id": "agent_123",
    "status": "updated"
  }
}
```

### 6.2 获取配置

```http
GET /api/v1/config/{agent_id}

响应：
{
  "code": 0,
  "message": "success",
  "data": {
    "agent_id": "agent_123",
    "config": {
      "weather_api": {
        "base_url": "https://api.weather.com",
        "timeout": 5
      }
    }
  }
}
```

## 7. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 认证失败 |
| 1003 | 权限不足 |
| 2001 | Agent 不存在 |
| 2002 | Agent 已存在 |
| 2003 | Agent 状态错误 |
| 3001 | 任务创建失败 |
| 3002 | 任务不存在 |
| 3003 | 任务状态错误 |
| 4001 | 配置更新失败 |
| 4002 | 配置不存在 |
| 5001 | 系统内部错误 |

## 8. 限流说明

- 普通用户：100 请求/分钟
- 高级用户：1000 请求/分钟
- 企业用户：10000 请求/分钟

## 9. 版本控制

- 当前版本：v1
- 版本更新：每年发布 2 个主要版本
- 兼容性：保证向后兼容 2 个主要版本

## 10. 最佳实践

### 10.1 错误处理

- 实现重试机制
- 处理超时情况
- 记录错误日志

### 10.2 性能优化

- 使用连接池
- 实现缓存机制
- 批量处理请求

### 10.3 安全建议

- 定期更新 API Key
- 使用 HTTPS
- 实现请求签名 