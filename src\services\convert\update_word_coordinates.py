from PIL import Image
import json


def transfer_path2size(image_path):
    """
    从图片路径中提取宽度和高度
    """
    img = Image.open(image_path).convert("RGBA")
    return img.size  # 返回宽度和高度作为元组 (img_width, img_height)


def extract_size(data):
    """
    提取图片宽度和高度映射，返回字典结构
    """
    return {item['pageName']: transfer_path2size(item['url']) for item in data}


def update_result_regions(json_data, after_size, before_size):
    """
    更新 JSON 数据中的区域坐标，使用页面对应的尺寸 (宽度和高度) 进行转换
    """
    # 检索 `report_detail` 中的 diff 列表
    if 'report_detail' in json_data and 'diff' in json_data['report_detail']:
        for diff_item in json_data['report_detail']['diff']:
            for region_type, size_map in [('originRegion', before_size), ('compareRegion', after_size)]:
                if region_type in diff_item:
                    for region in diff_item[region_type]:
                        page = region.get('page')
                        if page in size_map:
                            width, height = size_map[page]
                            # region['x'] *= width / 100
                            # region['width'] *= width / 100
                            # region['y'] *= height / 100
                            # region['height'] *= height / 100
                            region['x'] = parse_to_float(region['x']) * 1 / 100
                            region['width'] = parse_to_float(region['width']) * 1 / 100
                            region['y'] = parse_to_float(region['y']) * 1 / 100
                            region['height'] *= parse_to_float(region['height']) * 1 / 100
    return json_data

# AI generation start
def parse_to_float(value) -> float:
    try:
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):  # 字符串尝试转换
            return float(value)
        else:
            raise TypeError(f"Invalid type for {value} (Expected int/float/string)")
    except ValueError:
        raise ValueError(f"Cannot convert {value} to float")
# AI generation end

if "__main__" == __name__:
    # 数据输入
    data_after = [
        {
            'url': 'C:\\Users\\<USER>\\bibrain\\src\\converted_files\\compare_Word_b_vs_Word_a_20250618_135441\\Word_a\\Word_a_1.png',
            'pageName': '1'
        },
        {
            'url': 'C:\\Users\\<USER>\\bibrain\\src\\converted_files\\compare_Word_b_vs_Word_a_20250618_135441\\Word_a\\Word_a_2.png',
            'pageName': '2'
        },
        {
            'url': 'C:\\Users\\<USER>\\bibrain\\src\\converted_files\\compare_Word_b_vs_Word_a_20250618_135441\\Word_a\\Word_a_3.png',
            'pageName': '3'
        }
    ]

    data_before = [
        {
            'url': 'C:\\Users\\<USER>\\bibrain\\src\\converted_files\\compare_Word_b_vs_Word_a_20250618_135441\\Word_b\\Word_b_1.png',
            'pageName': '1'
        },
        {
            'url': 'C:\\Users\\<USER>\\bibrain\\src\\converted_files\\compare_Word_b_vs_Word_a_20250618_135441\\Word_b\\Word_b_2.png',
            'pageName': '2'
        },
        {
            'url': 'C:\\Users\\<USER>\\bibrain\\src\\converted_files\\compare_Word_b_vs_Word_a_20250618_135441\\Word_b\\Word_b_3.png',
            'pageName': '3'
        }
    ]

    # 构建数据结构：提取图片尺寸
    data_after_dict = extract_size(data_after)
    data_before_dict = extract_size(data_before)

    # 原始 JSON 数据
    json_data = {
        "total_diff": 11,
        "new_items": 1,
        "modified_items": 8,
        "deleted_items": 2,
        "report_path": "D:/Users/<USER>/Desktop/locate/file\\output",
        "report_detail": {
            "originPage": [],
            "comparePage": [],
            "diff": [
                {
                    "type": "update",
                    "contentType": "table",
                    "originRegion": [
                        {
                            "page": "2",
                            "x": 15.160423626849933,
                            "y": 34.392443211352436,
                            "width": 6.517722162385507,
                            "height": 1187789.3704730207
                        }
                    ],
                    "compareRegion": [
                        {
                            "page": "2",
                            "x": 15.160423626849933,
                            "y": 38.33590576560732,
                            "width": 6.853687228908497,
                            "height": 1187789.3704730207
                        }
                    ]
                },
                {
                    "type": "delete",
                    "contentType": "table",
                    "originRegion": [
                        {
                            "page": "3",
                            "x": 28.641021921084914,
                            "y": 33.03836391857436,
                            "width": 9.826978195797462,
                            "height": 1187789.3704730207
                        }
                    ],
                    "compareRegion": [
                        {
                            "page": "3",
                            "x": 0,
                            "y": 0,
                            "width": 0,
                            "height": 0
                        }
                    ]
                }
            ]
        }
    }

    # 更新 JSON 数据的区域坐标
    updated_data = update_result_regions(json_data, data_after_dict, data_before_dict)

    # 输出更新后的数据
    print(json.dumps(updated_data, indent=4))
