import os
import yaml
from typing import Any, Dict, Optional, List
from pathlib import Path
import logging
from dotenv import load_dotenv

class ConfigManager:
    _instance = None
    _config: Dict[str, Any] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._config:
            self._load_config()
            #self._load_agent_config()
    
    def _load_config(self):
        """加载所有配置文件"""
        # 加载环境变量
        load_dotenv()
        
        # 获取配置目录
        config_dir = Path(__file__).parent.parent / "config"
        
        # 加载全局配置
        global_config = self._load_yaml(config_dir / "global.yaml")
        self._config.update(global_config)
        
        # 加载LLM配置
        llm_config = self._load_yaml(config_dir / "llm" / "llm_config.yaml")
        if llm_config:
            self._config.update(llm_config)
        
        # 处理环境变量替换
        self._process_env_vars()
        
        # 合并通用配置到各个模型配置中
        # self._merge_common_config()
    
    def _load_yaml(self, file_path: Path) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logging.error(f"Error loading config file {file_path}: {str(e)}")
            return {}
    
    def _process_env_vars(self):
        """处理配置中的环境变量替换"""
        def replace_env_vars(value: Any) -> Any:
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                env_var = value[2:-1]
                return os.getenv(env_var, value)
            elif isinstance(value, dict):
                return {k: replace_env_vars(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [replace_env_vars(item) for item in value]
            return value
        
        self._config = replace_env_vars(self._config)
    
    def _merge_common_config(self):
        """将通用配置合并到各个模型配置中"""
        if 'llm' in self._config and 'common' in self._config['llm']:
            common_config = self._config['llm']['common']
            for model in self._config['llm']:
                if model != 'common' and isinstance(self._config['llm'][model], dict):
                    # 使用通用配置作为默认值
                    for key, value in common_config.items():
                        if key not in self._config['llm'][model]:
                            self._config['llm'][model][key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        for k in keys:
            if isinstance(value, dict):
                value = value.get(k)
            else:
                return default
        return value if value is not None else default
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def get_llm_config(self, model_name: str) -> Dict[str, Any]:
        """获取特定LLM模型的配置"""
        llm_config = self._config.get('llm', {})
        if model_name in llm_config:
            return llm_config[model_name].copy()
        return {}

    def _load_agent_configs(self):
        """自动发现并加载所有 Agent 配置"""
        agent_registry_path = self._config_dir / "agents" / "agent_registry.yaml"
        registry = self._load_yaml(agent_registry_path)

        self._config["agents"] = {}
        for agent_id, agent_info in registry.get("agents", {}).items():
            if agent_info.get("enabled", False):
                config_file = agent_info.get("config_file")
                if config_file:
                    agent_config_path = self._config_dir / "agents" / config_file
                    agent_config = self._load_yaml(agent_config_path)
                    if agent_config:
                        # 合并注册表中的基础信息与具体配置
                        merged_config = {**agent_info, **agent_config, "id": agent_id}
                        self._config["agents"][agent_id] = merged_config

    def get_agent_config(self, agent_id: str) -> Dict[str, Any]:
        """获取特定 Agent 的完整配置"""
        return self._config.get("agents", {}).get(agent_id, {})

    def get_enabled_agents(self) -> List[Dict[str, Any]]:
        """获取所有已启用的 Agent 配置"""
        return [config for config in self._config.get("agents", {}).values()
                if config.get("enabled", False)]

# 创建全局配置管理器实例
config_manager = ConfigManager() 


