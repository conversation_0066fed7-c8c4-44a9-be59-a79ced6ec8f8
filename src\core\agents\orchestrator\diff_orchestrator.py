import subprocess
from typing import Any, Dict, List
from ..base import Agent<PERSON>ontext
from .base_orchestrator import OrchestratorAgent
from loguru import logger
import subprocess
from src.core.agents.base import AgentConfig
from src.core.agents.worker.diff_worker import DiffWorker, prompt_ai_judge
from src.core.agents.validator.test_validate import validate_json_structure, validate_ai_judge_content, validate_generate_format
from src.services.llm.connect_model import query_model,async_call_qwen_vl
from src.services.llm.general_llm_connect import model_configs, ModelAPI
from src.prompt.config import PROMPT_INTENT

import asyncio
from threading import Thread
from req_diff.api import DiffTool

import websockets
import json
from fastapi import FastAPI
from fastapi.responses import JSONResponse

import sys
import io
# 设置标准输入输出为 UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

app = FastAPI()
result_sendToFront = [{
    "info": "Document check",
    "status": "loading"
}]

async def smooth_update_task_progress(target=0.20, step=0.01, interval=0.1):
    """
    异步逐步更新 status_json['task_progress'] 到目标值。
    只更新 task_progress 字段，不修改 workflow 或其他内容。
    """
    current = status_json["task_progress"]
    while current < target:
        current = round(current + step, 2)
        status_json["task_progress"] = min(current, target)
        await asyncio.sleep(interval)

import requests
import time

status_json = {
    "intent": "",
    "workflow": result_sendToFront,
    "ai_judge_content": "",
    "ai_validate_reason": "",
    "ai_score": 0.0,
    "task_progress":0.00,
    "task_status": "processing",
    "task_msg":""
}


async def update_cot(update_info):
    status_json.update(update_info)

async def get_status():
    return status_json


# def merge_ai_result(diff_result, ai_results):
#     # 将 ai_results 转换为字典，以 id 为键，方便快速查找
#     if not ai_results:
#         return diff_result
#     ai_results_dict = {ai_result['idx']: ai_result for ai_result in ai_results}
#
#     # 遍历 diff_result.diff_items，根据 diff_item.id 匹配 ai_results_dict 中的 id
#     for diff_item in diff_result.diff_items:
#         diff_item_id = diff_item.id  # 假设 diff_item 中有唯一的 id 属性
#
#         # 检查是否存在匹配的 ai_result
#         if diff_item_id in ai_results_dict:
#             ai_result = ai_results_dict[diff_item_id]
#             diff_item._ai_judge = ai_result  # 将 ai_result 的内容赋值给 diff_item 的 _ai_judge 属性
#
#     return diff_result

class DiffOrchestrator(OrchestratorAgent):
    """An orchestrator that manages a text processing pipeline."""

    async def _setup(self) -> None:
        """Set up the text processing pipeline."""
        await super()._setup()
        self.logger.info("Initializing text processing pipeline")

    async def _teardown(self) -> None:
        """Cleanup the text processing pipeline."""
        await super()._teardown()
        self.logger.info("Cleaning up text processing pipeline")

    async def _initialize_workflow(self) -> None:
        """Initialize the workflow steps."""
        self.workflow_steps = [
            {
                "name": "diff_worker",
                "worker": "diff_worker",
                "description": "Analyze the difference between worker"
            },
            {
                "name": "validation",
                "worker": "text_validator",
                "description": "Validate the processed text"
            }
        ]
        self.logger.info("Initialized text processing workflow")

    async def process_single_result(self, single_result, llm_config: dict = None, ai_judge: bool =True):
        global refill_ans
        global result_sendToFront
        global status_json


        #single_result_str = json.dumps(single_result, ensure_ascii=False)
        iterations = 0
        confidence = 0

        # workflow_step = {
        #     "name": "diff_worker",
        #     "worker": "diff_worker",
        #     "description": "Analyze the difference between worker"
        # }
        if not llm_config:
            llm_config = model_configs
        worker = self.workers["diff_worker"]
        # region 对于单个差异分析的运行和校验
        while confidence < 0.7 and iterations < 1:
            iterations += 1

            context = AgentContext(
                session_id=str(12345),
                input_data={
                    "temperature": 0.7,  # Example temperature setting
                    "max_tokens": 1500  # Example limit for token generation
                },
                metadata={
                    # Provide mock file paths for testing
                    "file_path_list": [
                        "mock/path/to/before.docx",
                        "mock/path/to/after.docx"
                    ],
                    "ai_judge": ai_judge,
                    "single_json": single_result,
                    "prompt": prompt_ai_judge,
                    "llm_config": llm_config
                }
            )

            bad_case_answer = {
                "idx": single_result.get('data', {}).get('id', None),
                "judge_result": single_result.get('data', {}).get('type', None),
                "diff_content": "",
                "judge_reason": ""
            }
            if ai_judge == False:
                single_result['data']['ai_judge'] = bad_case_answer
                refill_ans.append(single_result)
                return bad_case_answer

            logger.info("=======single_result=======")
            data_type = single_result.get('data_type', {})
            # 这里加多模态，如果路由发现判断的是graphic或者picture，那么就摇到多模态去做，这里直接跳过然后返回一个answer，避免时间长
            if data_type == "graphic" or data_type == "picture":
                #cvanswer=async_call_qwen_vl ()
                cv_answer = {
                    "idx": single_result.get('data', {}).get('id', None),
                    "judge_result": single_result.get('data', {}).get('type', None),
                    "diff_content": "", # 失败的情况不需要内容
                    "judge_reason": "" # 失败的情况不需要内容
                }
                single_result['data']['ai_judge'] = cv_answer
                refill_ans.append(single_result)
                return cv_answer

            # 调用大模型
            test_answer = await worker.execute(context)

            # 检查 test_answer 是否有效
            if not test_answer or not isinstance(test_answer, dict):
                bad_case_answer["judge_reason"] = f"Invalid test_answer returned:"
                return bad_case_answer

            print("============这里测试中间产物是否正确============result=============")
            result = test_answer.get('result')
            print(result)

            if isinstance(result, str) and result.strip().startswith("```json") and result.strip().endswith("```"):
                # 移除 `json` 和尾部的三引号
                cleaned_content = result.strip()[7:-3].strip()
                result = cleaned_content
                test_answer['result'] = result

            ai_generate_schema = {
                "idx": {"type": str},
                "judge_result": {"type": str},
                "diff_content": {"type": str},
                "judge_reason": {"type": str},
            }

            if not validate_json_structure(result,ai_generate_schema):
                bad_case_answer["judge_reason"] = f"Invalid test_answer returned:"
                logger.info(bad_case_answer)
                single_result['data']['ai_judge'] = bad_case_answer
                refill_ans.append(single_result)
                return bad_case_answer

            # region 确保不行的时候也可以继续用
            if result is None or result == "None" or (not isinstance(result, str)):

                bad_case_answer = {
                    "idx": single_result.get('data', {}).get('id', None),
                    "judge_result": single_result.get('data', {}).get('type', None),
                    "diff_content": "",
                    "judge_reason": ""
                }
                logger.info(bad_case_answer)
                single_result['data']['ai_judge'] = bad_case_answer
                refill_ans.append(single_result)
                return bad_case_answer

            try:
                cleaned_result = result.replace("\\", "\\\\")  # 替换非法反斜杠
                result_json = json.loads(cleaned_result)
                #result_json = json.loads(result)
            except (json.JSONDecodeError, TypeError):
                # 解析 JSON 失败时返回 bad_case_answer
                logger.info(f"Json转义有问题：{cleaned_result}")
                bad_case_answer = {
                    "idx": single_result.get('data', {}).get('id', None),
                    "judge_result": single_result.get('data', {}).get('type', None),
                    "diff_content": "",
                    "judge_reason": ""
                }
                single_result['data']['ai_judge'] = bad_case_answer
                refill_ans.append(single_result)
                return bad_case_answer

            # AI generation start
            # 这里是ai_judge的输出进行二次校验, 是否符合ai中三个内容的部分，已经是标准json格式，但不一定符合我们的模块化要求
            # if validate_generate_format(result_json) == False:
            #     bad_case_answer = {
            #         "idx": single_result.get('data', {}).get('id', None),
            #         "judge_result": single_result.get('data', {}).get('type', None),
            #         "diff_content": "No AI Judge",
            #         "judge_reason": "AI judge生成嵌套"
            #     }
            #     single_result['data']['ai_judge'] = bad_case_answer
            #     refill_ans.append(single_result)
            #     return bad_case_answer
            # AI generation end

            # region 这里看回填是否正确
            # print("========这里观看ai_judge回填是否成功=========")
            single_result['data']['ai_judge'] = result_json
            # print(single_result)
            result_json["idx"] = single_result.get('data', {}).get('id', None)

            confidence_list, validate_answer = await validate_ai_judge_content(single_result)

            confidence = confidence_list[0]
            print("==========当前评分==========")
            print(confidence)
            if confidence < 0.7:
                result_sendToFront.append({
                    "info": "Validate Fail",
                    "status": "fail"
                })
                result_sendToFront.append({
                    "info": "AI judge rerun",
                    "status": "processing"
                })
                status_json.update({
                    "workflow": result_sendToFront,
                    "ai_judge_content": "Fail",
                    "ai_validate_reason": validate_answer[0],
                    "ai_score": confidence
                })

            else:
                status_json.update({
                    "ai_judge_content": "Success",
                    "ai_validate_reason": validate_answer[0],
                    "ai_score": confidence
                })
        logger.info(result_json)
        refill_ans.append(single_result)
        return result_json

    async def process_results_in_batches(self, mid_result, batch_size=5, llm_config: dict = None, ai_judge: bool = True):
        # 将结果按照每 batch_size 分组
        self.total_count = len(mid_result)  # 设置总数
        self.processed_count = 0

        all_results = []
        total = len(mid_result)
        for i in range(0, len(mid_result), batch_size):
            batch = mid_result[i:i + batch_size]
            # 并发处理当前批次
            batch_results = await asyncio.gather(
                *[self.process_single_result(single_result, llm_config, ai_judge) for single_result in batch])

            # 按照批次更新workflow
            current = min(i + batch_size, total)  # 确保不会超过总数
            # 使用 f-string 动态生成 info 字段
            ai_judge_info = f"AI judge {current}/{total}"
            result_sendToFront = [{
                "info": "Document check",
                "status": "success"
            },
                {
                    "info": "Document parse and difference analyze",
                    "status": "success"
                },
                {
                    "info": ai_judge_info,
                    "status": "loading"
                }]
            status_json.update({
                "workflow": result_sendToFront
            })

            # 打印当前批次结果或其他处理
            logger.info("==========单个 batch 结束==========")
            #logger.info(batch_results)
            # 累积最终结果
            if i == 0:
                all_results = batch_results
            else:
                all_results.extend(batch_results)
            await asyncio.sleep(1)
            current = 3



        return all_results

    async def _execute_step(self, step: Dict[str, Any], context: AgentContext) -> Dict[str, Any]:
        """Execute a workflow step with additional pipeline-specific logic."""
        global refill_ans
        # 初始化status_json
        global status_json

        status_json.update({
            "task_status": "processing",
        })

        user_input = "現在、2つのファイルに関して比較および差分分析を実施する予定でございます。操作手順についてご案内いただけますと幸いです。"
        intent_ans = await query_model(user_input, PROMPT_INTENT)
        if intent_ans:
            intent_ans = intent_ans[7:]
            status_json.update({
                "intent": intent_ans,
            })

        refill_ans = []
        time_account = []
        worker_name = step.get("worker")
        if worker_name not in self.workers:
            raise ValueError(f"Worker not found: {worker_name}")

        worker = self.workers[worker_name]

        # 从 context.metadata 中提取 file_path_list
        print("======这里是context======")
        print(context)
        file_path_list = context.metadata.get("file_path_list", [])
        custom_output_dir = context.metadata.get("scl_output_path", [])
        BATCH_SIZE = context.metadata.get("batch_size",[])
        user_input = context.input_data.get("user_input")
        if not file_path_list:
            raise ValueError("file_path_list must contain exactly two file paths.")

        file_path_before, file_path_after = file_path_list

        user_input = context.input_data.get("user_input")
        # region 省略的意图识别
        # # 如果检测到传入是点击进来的话我们的intent就用这种默认输入吧，或者干脆生成个固定的intent
        # if len(user_input)<2:
        #     user_input = "Help me to analyze the files I upload, thank you."

        # intentToFront = await query_model(user_input, prompt_intent)
        # result_sendToFront = {
        #     "chain_of_thoughts": "",
        #     "answer": None  # 如果没有答案部分，则为 None
        # }

        # # 找到 COT 部分
        # cot_start_idx = intentToFront.find("##COT##")
        # if cot_start_idx == -1:
        #     print("字符串中缺少 ##COT## 部分！")
        #     result_sendToFront = {
        #         "chain_of_thoughts": intentToFront,
        #         "answer": None  # 如果没有答案部分，则为 None
        #     }

        # cot_end_idx = intentToFront.find("#Answer#")  # 定位 Answer 部分的开始
        # if cot_end_idx == -1:
        #     # 没有 Answer 部分时，COT 部分占据整个剩余内容
        #     result_sendToFront["chain_of_thoughts"] = intentToFront[cot_start_idx + len("##COT##"):].strip()
        # else:
        #     # 分割出 COT 和 Answer 部分
        #     result_sendToFront["chain_of_thoughts"] = intentToFront[cot_start_idx + len("##COT##"):cot_end_idx].strip()
        #     result_sendToFront["answer"] = intentToFront[cot_end_idx + len("#Answer#"):].strip()

        # # region 发送给前端COT
        # print("================给前端的COT部分对应右上角================")
        # #send_to_front_end_via_fastapi(result_sendToFront)
        # print(result_sendToFront)
        # endregion

        # 在 orchestrator 里显示我的intent，在worker的时候实时更新我的diff吧，validator 应该在每个文件后调用 但是我这里是拆开来的
        file_path_pair_list = [[file_path_before, file_path_after]]

        result_sendToFront = [{
            "info": "Document check",
            "status": "success"
        }, {
            "info": "Document parse and difference analyze",
            "status": "loading"
        }]
        status_json.update({
            "workflow": result_sendToFront
        })

        # 这里file_path_pair是一组文件
        for file_path_pair in file_path_pair_list:
            file_path_before, file_path_after = file_path_pair[0], file_path_pair[1]

            # region 这里是差分调用 如果后面抽mcp这里改成tool calling就好
            start_time = time.time()
            diff_obj = DiffTool(file_path_before, file_path_after)

            progress_task = asyncio.create_task(
                smooth_update_task_progress(target=0.20, step=0.01, interval=0.05)
            )

            # region diff 调用
            test = diff_obj.diff()
            logger.info("===========看到这里表示差分结束=============")

            # 等待进度更新完成
            await progress_task

            time_account.append({"解析差分": (time.time() - start_time)})
            result_sendToFront = [{
                "info": "Document check",
                "status": "success"
            },
                {
                    "info": "Document parse and difference analyze",
                    "status": "success"
                }]
            status_json.update({
                "task_progress": 0.20,
                "workflow": result_sendToFront
            })
            diff_jsonfile = test.to_diff_dict()

            # region 格式校验先mock掉
            # IsValidJson = validate_json_structure(test)
            # if IsValidJson == False:
            #     print("Json File Valid Format!")
            # else:
            #     print("==========Correct Format!=========")

            # 提取旧文件名和新文件名
            old_file_name = diff_jsonfile.get("old_file_name", "")
            new_file_name = diff_jsonfile.get("new_file_name", "")

            # print(diff_jsonfile["base_sheets_positions"])

            result_sendToFront.append({
                "info": "AI judge start",
                "status": "loading"
            })
            status_json.update({
                "workflow": result_sendToFront
            })
            # 提取 data 列表并构造 result_list
            data_list = diff_jsonfile["data"]
            mid_result = [
                {
                    "old_file_name": old_file_name,
                    "new_file_name": new_file_name,
                    "data_type": item.get("data_type", {}),
                    "block_name": item.get("block_name", {}),
                    "data": {
                        "id": item.get("id", {}),
                        "type": item.get("type", {}),
                        "ai_judge": item.get("ai_judge", {}),
                        "old": item.get("old", {}),
                        "new": item.get("new", {}),
                    }
                }
                for item in data_list
            ]

            start_time = time.time()
            ai_judge2 = context.metadata.get("ai_judge")
            files_analysis = await self.process_results_in_batches(mid_result, batch_size=BATCH_SIZE, llm_config=model_configs, ai_judge=ai_judge2)
            time_account.append({"AI判定": (time.time() - start_time)})

        result_sendToFront = [{
            "info": "Document check",
            "status": "success"
        },
            {
                "info": "Document parse and difference analyze",
                "status": "success"
            },
            {
                "info": "AI judge Finish",
                "status": "success"
            }]
        status_json.update({
            "workflow": result_sendToFront
        })

        print("==================files_refill====================")
        print(refill_ans)

        result_sendToFront = [{
            "info": "Document check",
            "status": "success"
        },
            {
                "info": "Document parse and difference analyze",
                "status": "success"
            },
            {
                "info": "AI judge Finish",
                "status": "success"
            },
            {
                "info": "SCL generate start",
                "status": "loading"
            }]
        status_json.update({
            "workflow": result_sendToFront,
            "task_progress": 0.80,
        })

        # time.sleep(2)
        logger.info("===========看到这里表示AI部分结束=============")
        start_time = time.time()
        # region 执行scl生成
        # scl生成的路径调用方法 path = diff_obj.exporter(test,files_analysis)
        if custom_output_dir:
            scl_out_path = diff_obj.exporter(test, files_analysis, custom_output_dir=custom_output_dir)
        else:
            scl_out_path = diff_obj.exporter(test, files_analysis)
        logger.info("===========看到这里表示SCL生成部分结束============")
        print(scl_out_path)
        print("==========这里是差分的结果路径名称===========")
        time_account.append({"生成SCL文件": (time.time() - start_time)})

        result_sendToFront = [{
            "info": "Document check",
            "status": "success"
        },
        {
            "info": "Document parse and difference analyze",
            "status": "success"
        },
        {
            "info": "AI judge Finish",
            "status": "success"
        },
        {
            "info": "SCL generate Finish",
            "status": "success"
        }]
        status_json.update({
             "workflow": result_sendToFront,
             "task_progress": 0.90,
        })

        print("==========时间统计=============")
        print(time_account)

        sheet_info = diff_jsonfile.get("base_sheets_positions", [])  # 如果键不存在，返回一个空列表
        target_info = diff_jsonfile.get("target_sheets_positions", [])  # 如果键不存在，返回一个空列表

        return refill_ans, sheet_info, target_info
        # return refill_ans, sheet_info


import uuid

if __name__ == "__main__":
    print("hhh")
# AI generation end