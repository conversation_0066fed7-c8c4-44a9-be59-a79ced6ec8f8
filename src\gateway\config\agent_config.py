import os
import yaml
import glob
from typing import Dict, List, Optional
from pathlib import Path
from ..models.agent import AgentInfo, AgentMetadata, AgentCapability
from src.common.exceptions import ConfigError, ServiceDependencyError
import logging

logger = logging.getLogger(__name__)

class AgentConfigLoader:
    """Agent配置加载器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "version": "1.0.0",
        "service": {
            "base_path": "/api"
        },
        "rate_limit": {
            "requests_per_minute": 60,
            "concurrent_requests": 10
        },
        "auth": {
            "required": False,
            "type": "none"
        },
        "dependencies": {
            "python": [
                "fastapi>=0.68.0",
                "uvicorn>=0.15.0",
                "pydantic>=1.8.0"
            ],
            "system": [
                "python>=3.8",
                "pip>=21.0"
            ]
        },
        "resources": {
            "memory_limit": "1G",
            "cpu_limit": "1",
            "gpu_limit": "0"
        },
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file": "agent.log"
        },
        "monitoring": {
            "enabled": True,
            "metrics_port": 9090,
            "health_check_interval": 30
        }
    }
    
    def __init__(self, config_dir: str = "config/"):
        self._config_dir = config_dir
        self._configs: Dict[str, dict] = {}
        self._agents: Dict[str, AgentInfo] = {}
        self._mcp_services: Dict[str, dict] = {}  # MCP服务注册表
        self._agent_configs: Dict[str, dict] = {}

    
    def register_mcp_service(self, service_name: str, service_info: dict):
        """注册MCP服务"""
        self._mcp_services[service_name] = service_info
        logger.info(f"MCP service registered: {service_name}")

    def load_configs(self) -> bool:
        """加载所有Agent配置
        Returns:
            bool: 是否成功加载至少一个有效配置
        """
        try:
            os.makedirs(self._config_dir, exist_ok=True)
            self._agent_configs = {}

            registry_path = os.path.join(self._config_dir, "agent_registry.yaml")
            
            if not os.path.exists(registry_path):
                return False  # 明确返回False表示文件不存在

            with open(registry_path, "r", encoding="utf-8") as f:
                registry = yaml.safe_load(f) or {}

            has_valid_config = False
            for agent_id, agent_info in registry.get("agents", {}).items():
                if agent_info.get("enabled", False):
                    config_file = agent_info.get("config_file")
                    if config_file:
                        config_path = os.path.join(self._config_dir, "agents", config_file)
                        if os.path.exists(config_path):
                            with open(config_path, "r", encoding="utf-8") as f:
                                config = yaml.safe_load(f) or {}
                                self._agent_configs[agent_id] = {**agent_info, **config, "id": agent_id}
                                has_valid_config = True

            return has_valid_config  # 返回是否加载到有效配置

        except Exception as e:
            logging.error(f"Config loading failed: {str(e)}")
            return False

    def _validate_mcp_dependencies(self, config: dict):
        """验证MCP服务依赖"""
        mcp_services = config.get('mcp_services', [])
        missing_services = []
        
        for service in mcp_services:
            service_name = service['name']
            required = service.get('required', False)
            version = service.get('version')
            
            # 检查服务是否存在
            if service_name not in self._mcp_services:
                if required:
                    missing_services.append(f"{service_name} (required)")
                continue
            
            # 检查版本兼容性
            if version:
                registered_version = self._mcp_services[service_name].get('version')
                if registered_version and not self._is_version_compatible(version, registered_version):
                    logger.error(f"Version mismatch for service {service_name}: required {version}, but found {registered_version}")
                    raise ServiceDependencyError(
                        f"Version mismatch for service {service_name}: "
                        f"required {version}, but found {registered_version}"
                    )
        
        if missing_services:
            logger.error(f"Missing required MCP services: {', '.join(missing_services)}")
            raise ServiceDependencyError(
                f"Missing required MCP services: {', '.join(missing_services)}"
            )
    
    def _is_version_compatible(self, required_version: str, actual_version: str) -> bool:
        """检查版本兼容性"""
        # 这里可以实现更复杂的版本兼容性检查逻辑
        return required_version == actual_version
    
    def _merge_with_defaults(self, config: dict) -> dict:
        """合并用户配置和默认配置"""
        merged = self.DEFAULT_CONFIG.copy()
        
        # 递归合并配置
        def merge_dict(d1: dict, d2: dict) -> dict:
            for key, value in d2.items():
                if key in d1 and isinstance(d1[key], dict) and isinstance(value, dict):
                    d1[key] = merge_dict(d1[key], value)
                else:
                    d1[key] = value
            return d1
        
        return merge_dict(merged, config)
    
    def get_agent_config(self, agent_id: str) -> Optional[dict]:
        """获取指定Agent的配置"""
        return self._configs.get(agent_id)
    
    def get_all_agent_configs(self) -> Dict[str, dict]:
        """获取所有Agent配置"""
        return self._agent_configs

    def get_agent_info(self, agent_id: str) -> Optional[AgentInfo]:
        """将配置转换为 AgentInfo 对象"""
        config = self._agent_configs.get(agent_id)
        if not config:
            return None

        # 确保配置包含必要的字段
        if "name" not in config:
            raise ConfigError(f"Missing 'name' in agent config: {agent_id}")

        if "endpoints" not in config:
            raise ConfigError(f"Missing 'endpoints' in agent config: {agent_id}")

        # 创建 AgentInfo 对象
        return AgentInfo(
            agent_id=agent_id,
            enabled=config['enabled'],
            name=config["name"],
            metadata=config.get("metadata", {}),
            endpoints=config["endpoints"],
            rate_limit=config.get("rate_limit", {}),
        )
    
    def get_all_agent_infos(self) -> List[AgentInfo]:
        """获取所有Agent信息"""
        return list(self._agents.values())
    
    def _create_agent_info(self, config: dict) -> AgentInfo:
        """从配置创建AgentInfo对象"""
        # 创建元数据
        metadata = AgentMetadata(
            description=config.get('description', ''),
            capabilities=[AgentCapability(cap) for cap in config.get('capabilities', [])],
            version=config.get('version', '1.0.0'),
            author=config.get('author', {}).get('name', ''),
            tags=config.get('tags', []),
            parameters=config.get('env', {}),
            examples=config.get('examples', [])
        )
        
        # 创建AgentInfo
        return AgentInfo(
            agent_id=config['id'],
            name=config['name'],
            host=config['service']['host'],
            port=config['service']['port'],
            metadata=metadata,
            endpoints=self._process_endpoints(config),
            rate_limit=config.get('rate_limit', {}),
            auth_required=config.get('auth', {}).get('required', False),
            auth_type=config.get('auth', {}).get('type')
        )
    
    def _process_endpoints(self, config: dict) -> Dict[str, str]:
        """处理API端点配置"""
        base_path = config['service'].get('base_path', '/api')
        endpoints = config.get('endpoints', {})
        
        # 添加基础路径
        return {
            key: f"{base_path}{path}" if not path.startswith('/') else f"{base_path}{path}"
            for key, path in endpoints.items()
        }

    def validate_dependencies(self, agent_id: str) -> bool:
        """验证 Agent 的依赖关系"""
        config = self._agent_configs.get(agent_id)
        if not config:
            return False

        dependencies = config.get("dependencies", [])
        for dep in dependencies:
            if dep not in self._agent_configs:
                return False

        return True

# 创建全局配置加载器实例
agent_config_loader = AgentConfigLoader() 