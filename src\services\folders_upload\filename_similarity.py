import numpy as np
from fuzzywuzzy import fuzz


def calculate_similarity(list1, list2):
    """计算两个列表中每对字符串的字符级相似度矩阵"""
    # 初始化相似度矩阵
    similarity_matrix = np.zeros((len(list1), len(list2)))
    
    # 使用fuzzywuzzy计算每对字符串的相似度
    for i, str1 in enumerate(list1):
        for j, str2 in enumerate(list2):
            similarity_matrix[i][j] = fuzz.ratio(str1, str2) / 100.0
            
    return similarity_matrix


def get_top_similar_pairs(list1, list2, threshold=0.5):
    """提取相似度最高的字符串对，确保每个 i 和 j 只能被添加一次"""
    # 获取 list1 和 list2 的长度
    
    len1, len2 = len(list1), len(list2)

    # 如果任一列表为空，直接返回空列表
    if len1 == 0 or len2 == 0:
        logger.warning("无法进行文件名配对，其中一个文件夹为空")
        return []

    # 计算相似度矩阵
    similarity_matrix = calculate_similarity(list1, list2)

    # 获取所有相似度对的索引和相似度值
    pairs = []

    # 使用集合记录已经匹配过的 i 和 j 索引
    used_indices_i = set()
    used_indices_j = set()

    # 先按相似度从高到低排序后再匹配
    all_pairs = []
    for i in range(len1):
        for j in range(len2):
            similarity_value = similarity_matrix[i, j]
            if similarity_value >= threshold:
                all_pairs.append((i, j, similarity_value))

    # 按相似度排序（从高到低）
    all_pairs.sort(key=lambda x: x[2], reverse=True)

    # 按相似度顺序选择最佳匹配
    for i, j, similarity_value in all_pairs:
        # 只有当 i 和 j 从未被添加时，才加入 pairs
        if i not in used_indices_i and j not in used_indices_j:
            pairs.append((i, j, similarity_value))
            used_indices_i.add(i)  # 将 i 标记为已匹配
            used_indices_j.add(j)  # 将 j 标记为已匹配

    # 提取匹配对，只返回字符串内容
    top_pairs = [[list1[i], list2[j]] for i, j, _ in pairs]

    return top_pairs


def get_files_from_directory(directory: str):
    """
    读取指定目录中的所有文件路径和文件名。
    :param directory: 文件夹路径
    :return: (file_names) - 文件名列表
    """
    file_names = []  # 存储文件的文件名（不包含路径）
    # 遍历文件夹及其子文件夹
    for _, _, filenames in os.walk(directory):
        for filename in filenames:
            # 获取文件的完整路径
            # 将路径和文件名分别添加到列表中
            file_names.append(filename)
    return file_names

def clean_file_names(file_list):
    # 定义优先级规则（分别为 Word 和 Excel 文件设定）
    priority_word = {
        '.docx': 1,  # docx 优先级高
        '.doc': 2    # doc 优先级低
    }
    priority_excel = {
        '.xlsx': 1,  # xlsx 优先级高
        '.xls': 2,
        '.XLSX': 3,
    }

    # 分开保存去重后的文件
    unique_word_files = {}   # 存储 Word 文件
    unique_excel_files = {}  # 存储 Excel 文件

    # 单次循环同时处理筛选和去重
    for file in file_list:
        # 跳过以~$开头的文件名
        if file.startswith('~$'):
            continue

        # 分离文件名和扩展名
        name, ext = file.rsplit('.', 1)
        ext = '.' + ext  # 补上点

        # 处理 Word 文件
        if ext in priority_word:
            # 如果文件名还没有出现在 unique_word_files 中，直接保存
            if name not in unique_word_files:
                unique_word_files[name] = file
            else:
                # 如果已经出现，按优先级选择
                existing_file = unique_word_files[name]
                _, existing_ext = existing_file.rsplit('.', 1)
                existing_ext = '.' + existing_ext

                # 按优先级比较，保留优先级更高的文件
                if priority_word[ext] < priority_word[existing_ext]:
                    unique_word_files[name] = file

        # 处理 Excel 文件
        elif ext in priority_excel:
            # 如果文件名还没有出现在 unique_excel_files 中，直接保存
            if name not in unique_excel_files:
                unique_excel_files[name] = file
            else:
                # 如果已经出现，按优先级选择
                existing_file = unique_excel_files[name]
                _, existing_ext = existing_file.rsplit('.', 1)
                existing_ext = '.' + existing_ext

                # 按优先级比较，保留优先级更高的文件
                if priority_excel[ext] < priority_excel[existing_ext]:
                    unique_excel_files[name] = file

    # 返回去重后的文件列表（两个部分类别合并到一起）
    return list(unique_word_files.values()) + list(unique_excel_files.values())

if __name__ == '__main__':
    # dir_before=r"D:\Users\kotei\Desktop\Req_Diff_Test_Doc-master-fb19739f86f2226ceadeae7f33ec04624595b7fc\DHCP188源文件\before"
    # dir_after=""r"D:\Users\kotei\Desktop\Req_Diff_Test_Doc-master-fb19739f86f2226ceadeae7f33ec04624595b7fc\DHCP188源文件\after"
    dir_before=r"D:/Users/<USER>/Desktop/before"
    dir_after = r"D:/Users/<USER>/Desktop/after"

    file_before_names = get_files_from_directory(dir_before)
    file_after_names = get_files_from_directory(dir_after)
    file_before_names_cleaned = clean_file_names(file_before_names)
    file_after_names_cleaned = clean_file_names(file_after_names)
    # 获取相似度最高的
    top_pairs = get_top_similar_pairs(file_before_names_cleaned, file_after_names_cleaned)
    # 打印结果
    n = 0
    for pair in top_pairs:
        print(f"Pair: {pair}")
        n += 1
    print(f"匹配到{n}对文件")

