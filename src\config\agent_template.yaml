# Agent配置文件模板
agent:
  # 基本信息
  id: "my-agent-001"  # Agent唯一标识
  name: "My Custom Agent"  # Agent名称
  
  # 服务配置
  service:
    host: "localhost"  # 服务主机
    port: 8000  # 服务端口
  
  # 能力配置（至少选择一个）
  capabilities:
    - "chat"  # 对话能力
  
  # API端点配置（根据capabilities配置对应的端点）
  endpoints:
    chat: "/chat"  # 聊天接口
  
  # MCP服务依赖（可选）
  mcp_services:
    - name: "knowledge-base"  # 知识库服务
      version: "1.0.0"  # 服务版本
      required: true  # 是否必需
    - name: "vector-store"  # 向量存储服务
      version: "1.0.0"
      required: true
    - name: "llm-service"  # LLM服务
      version: "1.0.0"
      required: true
  
  # 环境变量（可选）
  env:
    OPENAI_API_KEY: "your-api-key"  # OpenAI API密钥
  
  # 描述（可选）
  description: "这是一个自定义Agent"
  
  # 开发者信息（可选）
  author:
    name: "开发者名称"
    email: "<EMAIL>"
  
  # 标签（可选）
  tags:
    - "chat"
  
  # 速率限制
  rate_limit:
    requests_per_minute: 60
    concurrent_requests: 10
  
  # 依赖配置
  dependencies:
    python:
      - "fastapi>=0.68.0"
      - "uvicorn>=0.15.0"
      - "pydantic>=1.8.0"
    system:
      - "python>=3.8"
      - "pip>=21.0"
  
  # 资源限制
  resources:
    memory_limit: "1G"  # 内存限制
    cpu_limit: "1"  # CPU限制
    gpu_limit: "0"  # GPU限制
  
  # 日志配置
  logging:
    level: "INFO"  # 日志级别
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: "agent.log"  # 日志文件
  
  # 监控配置
  monitoring:
    enabled: true  # 是否启用监控
    metrics_port: 9090  # 指标端口
    health_check_interval: 30  # 健康检查间隔（秒）
  
  # 示例
  examples:
    - input: "你好"
      output: "你好！我是你的AI助手。"
    - input: "执行任务"
      output: "任务已开始执行..." 