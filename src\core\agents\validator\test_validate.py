# AI generation start
import unittest
from typing import Any, Dict, List, Optional
from loguru import logger

import json
from typing import Dict, List, Tuple, Any, Optional, Union

def validate_json_structure(
    json_input: Union[str, bytes],
    schema: Dict[str, Dict[str, Any]],
    required_fields: Optional[List[str]] = None
) -> Tuple[bool, Dict[str, Any], str]:
    """
    验证输入是否为合法 JSON，并符合指定 schema。

    :param json_input: 输入数据（str 或 bytes）
    :param schema: 字段定义 schema
    :param required_fields: 显式指定必须字段
    :return: (是否成功, 数据, 错误信息)
    """
    # 第一步：处理输入类型
    if isinstance(json_input, bytes):
        try:
            json_str = json_input.decode("utf-8")
        except UnicodeDecodeError:
            return False, None, "输入为 bytes 类型，但无法使用 UTF-8 解码"
    elif isinstance(json_input, str):
        json_str = json_input
    else:
        return False, None, f"不支持的输入类型: {type(json_input)}"

    # 第二步：尝试解析 JSON
    try:
        data = json.loads(json_str)
    except json.JSONDecodeError as e:
        return False, None, f"JSON 解析失败: {e}"

    # 第三步：校验字段结构
    required = required_fields or [field for field, info in schema.items() if info.get("required", False)]

    missing_fields = [field for field in required if field not in data]
    if missing_fields:
        return False, None, f"缺少必要字段: {', '.join(missing_fields)}"

    for field, info in schema.items():
        if field in data:
            expected_type = info.get("type", Any)
            value = data[field]

            # 处理联合类型（如 Union[int, str]）
            if isinstance(expected_type, tuple):
                if not any(isinstance(value, t) for t in expected_type):
                    expected_types_names = ", ".join([t.__name__ for t in expected_type])
                    return False, None, f"字段 '{field}' 类型错误，期望 {expected_types_names}。"
            else:
                if not isinstance(value, expected_type):
                    return False, None, f"字段 '{field}' 类型错误，期望 {expected_type.__name__}。"

    return True, data, ""


import unittest
from src.services.llm.connect_model import query_model, connect_V3, query_llama
import json
import re
from src.services.llm.general_llm_connect import ModelAPI

# region prompt_ai_validate
prompt_ai_validate = """
# role
You are an expert in the field of difference analysis, specializing in analyzing data related to the automotive industry. You now need to judge whether the AI-generated diff analysis results are reasonable.

# task
The user will provide you with a JSON-formatted input, and you need to focus on the "data" list within the JSON file.
This is a dict list only contain one json file, you have to analyze the difference between new content and the old one, then you should judge the 
"ai_judge" part and give me a confidence if it is reasonable.
important part: 
   1. I will show you what is functional changes and non-functional changes
       Functional changes:Changes such as numerical modifications, additions, deletions, or edits of descriptive information 
       Non-Functional changes:Headers, footers, font colors, or text sizes 
       ** Attention ** :  If it involves symbol changes, contextual information in "new" or "old" is required for judgment.
   2. Return you confidence for each difference pair (old and new) as a list, each confidence between 0 to 1:
       0 means that it must be wrong, and 1 means that it is definitely true
       You should pay attention on ai_judge part, functional or non-functional_change mentioned in "diff_content" and the reason it gives
   3. You should attach a brief words to explain why you think so in another list
# input   format:
{
"old_file_name": (old filename), 
"new_file_name": (new filename), 
"data_type": (data_type),
"data": [
     {
        "type": (change type here), 
        "ai_judge":
              { 
                "judge_result": "(AI judge change type)",
                "diff_content":" (Conclude change briefly here)",
                "judge_reason": "(You should judge whether this change will influnce function result)",,
              },
        "old": { 
              (old content here), 
        }
        "new": (new content here), 
    }
    ]
}

# output  format (notice that the first line is confidence not in double quotes and the second line is reason in double quotes):
[0.8] 
[\"(Your reason here, no more than 3 sentences)\"]

# requirement
- Attention! If the data type is graphic or picture, ignore the content in data and judge with other info 
- If the "old" part is blank or "new" part is blank, just leave it there because that means add or delete occurs
- ONLY ANSWER WITH ONE LIST,  do not give any other answer
- Keep your response brief and within 3 sentences
- Your reason should be in Japanese, NEVER USE symbols such as '」s' or '「'
- All the text result should be in Japanese text but not unicode, and the symbols should still be in english version
- you must wrap them entirely in english version double quotes \"reason\"
"""
# endregion

import re


async def validate_ai_judge_content(data: Dict[str, Any]) -> (List[float], List[str]):
    # result_ai = extract_content(data)
    result_ai = data
    return [1], ["No reason"]
    if result_ai['data_type'] == "graphic" or result_ai['data_type'] == "picture":
        # 保留 old 中 data 的前 10 位内容
        if 'old' in result_ai and 'data' in result_ai['old']:
            result_ai['old']['data'] = result_ai['old']['data'][:5]

        # 保留 new 中 data 的前 10 位内容
        if 'new' in result_ai and 'data' in result_ai['new']:
            result_ai['new']['data'] = result_ai['new']['data'][:5]


    result_ai_str = json.dumps(result_ai, ensure_ascii=False)
    validate_result = await query_llama(result_ai_str, prompt_ai_validate)
    #validate_result = await query_model(result_ai_str, prompt_ai_validate)
    if validate_result:
        print("以下是result_ai")
        print(result_ai)
        print("这里是validate直接结果")
        print(validate_result)
        if validate_result == "{'error': '连接 API 过程中发生错误: Server disconnected'}":
            return [0], ["API连接失败"]
        # 这里我是规则匹配出第一个confidence list和第二个原因的list，可以考虑只要第一个
        confidence_match = re.search(r"\[\s*([\d.,\s]*)\s*\]", validate_result)
        if confidence_match:
            # Extract the confidence list
            confidence_list_str = confidence_match.group(1).strip()
            confidence_list = [float(num) for num in confidence_list_str.split(",")]
        else:
            confidence_list=[1]
            #raise ValueError("No confidence list found at the beginning of the validate_result.")

        print("以下是confidence_match:", confidence_list)
        # Match result_list
        result_match = re.search(r"\[(\".*?\"\s*(?:,\s*\".*?\")*)\]", validate_result, re.DOTALL)
        if result_match:
            # Extract the result list (strings within brackets)
            result_list_raw = result_match.group(1)
            # Split by ",\"" to separate individual entries and remove quotes
            result_list = [item.strip('"') for item in result_list_raw.split("\",\"")]
        else:
            result_list = ["No reason"]
            #raise ValueError("No result list found in the validate_result.")

        return confidence_list, result_list
    else:
        return [1], ["No reason"]


def extract_content(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates the ai judge part of a JSON file.

    :param data: JSON object to validate.
    :return: confident list about the ai judge.
    """
    # Extract old and new file names
    result = {
        "old_file_name": data.get("old_file_name"),
        "new_file_name": data.get("new_file_name"),
        "data": []
    }

    # 以下为不需要的键，提取部分重要内容，不重要内容不提取，减少大模型负担
    keys_to_exclude = {
        "data_type", "content_type", "sub_type", "head_type", "belong_to",
        "diff_point", "diff_point_str", "rule", "block_name", "extend"
    }

    # Filter the "data" field in the input JSON
    for item in data.get("data", []):
        # Create a shallow copy and exclude specified keys
        filtered_item = {key: value for key, value in item.items() if key not in keys_to_exclude}

        # Process nested "new" and "old" fields
        if "new" in filtered_item:
            filtered_item["new"] = {key: value for key, value in filtered_item["new"].items() if
                                    key not in keys_to_exclude}

        if "old" in filtered_item:
            filtered_item["old"] = {key: value for key, value in filtered_item["old"].items() if
                                    key not in keys_to_exclude}

        # Append the filtered item to the result
        result["data"].append(filtered_item)

    return result


def validate_generate_format(result):
    # 检查是否是字典
    if not isinstance(result, dict):
        return False, "result 必须是一个字典"

    # 必须包含的键
    required_keys = ["idx", "judge_result", "diff_content", "judge_reason"]

    # 检查键是否齐全
    for key in required_keys:
        if key not in result:
            return False

    # 检查 judge_reason 是否为字符串
    if not isinstance(result["judge_reason"], str):
        return False

    # 如果所有校验都通过
    return True

class TestValidateJsonStructure(unittest.TestCase):
    def test_valid_data(self):
        """
        Test case: valid JSON structure
        """
        valid_data = {
            "old_path": "old\\path",
            "new_path": "new\\path",
            "old_file_name": "old_file.txt",
            "new_file_name": "new_file.txt",
            "data": [
                {
                    "type": "example",
                    "data_type": "text",
                    "content_type": "example",
                    "new": {
                        "content": "some content",
                        "index": "1"
                    },
                    "old": {
                        "content": "content here",
                        "index": "2"
                    }
                }
            ]
        }
        self.assertTrue(validate_json_structure(valid_data))

    def test_missing_required_field(self):
        """
        Test case: missing required field
        """
        invalid_data = {
            "new_path": "new\\path",
            "old_file_name": "old_file.txt",
            "new_file_name": "new_file.txt",
            "data": []
        }
        with self.assertRaises(ValueError) as context:
            validate_json_structure(invalid_data)
        self.assertEqual(str(context.exception), "'old_path' is a required field and is missing.")

    def test_wrong_type_for_required_field(self):
        """
        Test case: required field has wrong type
        """
        invalid_data = {
            "old_path": 123,  # Should be a string
            "new_path": "new\\path",
            "old_file_name": "old_file.txt",
            "new_file_name": "new_file.txt",
            "data": []
        }
        with self.assertRaises(ValueError) as context:
            validate_json_structure(invalid_data)
        self.assertEqual(str(context.exception), "'old_path' must be of type str.")

    def test_data_field_not_list(self):
        """
        Test case: 'data' field is not a list
        """
        invalid_data = {
            "old_path": "old\\path",
            "new_path": "new\\path",
            "old_file_name": "old_file.txt",
            "new_file_name": "new_file.txt",
            "data": "not a list"  # Should be a list
        }
        with self.assertRaises(ValueError) as context:
            validate_json_structure(invalid_data)
        self.assertEqual(str(context.exception), "'data' must be a list of dictionaries.")

    def test_data_item_not_dict(self):
        """
        Test case: 'data' contains an item that is not a dictionary
        """
        invalid_data = {
            "old_path": "old\\path",
            "new_path": "new\\path",
            "old_file_name": "old_file.txt",
            "new_file_name": "new_file.txt",
            "data": ["not a dict"]  # Should be a dictionary
        }
        with self.assertRaises(ValueError) as context:
            validate_json_structure(invalid_data)
        self.assertEqual(str(context.exception), "Each element in 'data' must be a dictionary.")

    def test_optional_field_with_wrong_type(self):
        """
        Test case: optional field has wrong type
        """
        invalid_data = {
            "old_path": "old\\path",
            "new_path": "new\\path",
            "old_file_name": "old_file.txt",
            "new_file_name": "new_file.txt",
            "data": [
                {
                    "type": 123,  # Should be string
                    "new": {},
                    "old": {}
                }
            ]
        }
        with self.assertRaises(ValueError) as context:
            validate_json_structure(invalid_data)
        self.assertEqual(str(context.exception), "'type' in 'data' must be of type str.")

    def test_nested_field_with_wrong_type(self):
        """
        Test case: nested field within 'new' has wrong type
        """
        invalid_data = {
            "old_path": "old\\path",
            "new_path": "new\\path",
            "old_file_name": "old_file.txt",
            "new_file_name": "new_file.txt",
            "data": [
                {
                    "new": {
                        "content": 123  # Should be string or None
                    },
                    "old": {}
                }
            ]
        }
        with self.assertRaises(ValueError) as context:
            validate_json_structure(invalid_data)
        self.assertEqual(str(context.exception), "'content' in 'new' must be of type str.")

    def test_all_fields_valid(self):
        """
        Test case: all fields present and valid
        """
        valid_data = {
            "old_path": "old\\path",
            "new_path": "new\\path",
            "old_file_name": "old_file.txt",
            "new_file_name": "new_file.txt",
            "data": [
                {
                    "type": "example",
                    "data_type": "text",
                    "content_type": "example",
                    "belong_to": "section1",
                    "diff_point": "point1",
                    "diff_point_str": "string",
                    "rule": None,
                    "block_name": "block1",
                    "extend": [],
                    "ai_judge": {},
                    "chapter": None,
                    "old": {},
                    "new": {"content": "some content", "index": "1"}
                }
            ]
        }
        self.assertTrue(validate_json_structure(valid_data))


import asyncio

input_data_2 = {'old_file_name': '3130_ユーザーカスタマイズ制御(オフボード通信用)-a.docx',
                'new_file_name': '3130_ユーザーカスタマイズ制御(オフボード通信用)-b.docx', 'data': [{'type': 'update',
                                                                                 'ai_judge': {'judge_result': '更新',
                                                                                              'diff_content': 'ページ番号が「PAGE 13」から「PAGE 1」に変更されました。',
                                                                                              'judge_reason': 'ページ番号の変更はドキュメントの構造や参照情報を変更するため、機能変更に該当します。'},
                                                                                 'old': {
                                                                                     'content': 'text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用)\u3000–( PAGE 13)',
                                                                                     'index': '', 'id': None,
                                                                                     'row_index': '', 'col_index': '',
                                                                                     'diff_context': '',
                                                                                     'diff_content': ''}, 'new': {
            'content': 'text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用)\u3000–( PAGE 1)', 'index': '', 'id': None,
            'row_index': '', 'col_index': '', 'diff_context': '', 'diff_content': ''}}, {'type': 'update', 'ai_judge': {
        'judge_result': '更新', 'diff_content': 'ページ番号が「PAGE 13」から「PAGE 1」に変更されました。',
        'judge_reason': 'ページ番号の変更はドキュメントの構造や参照情報を変更するため、機能変更に該当します。'}, 'old': {
        'content': 'text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通信用)\u3000–( PAGE 13)', 'index': '', 'id': None,
        'row_index': '', 'col_index': '', 'diff_context': '', 'diff_content': ''}, 'new': {
        'content': 'text:3-1.各機能仕様／30.ﾕｰｻﾞｰｶｽﾀﾏｲｽﾞ制御(ｵﾌﾎﾞｰﾄﾞ通 信用)\u3000–( PAGE 1)', 'index': '', 'id': None,
        'row_index': '', 'col_index': '', 'diff_context': '', 'diff_content': ''}}]}


async def main():
    # 调用你需要运行的异步方法
    confidence_list, result_list = await validate_ai_judge_content(input_data_2)
    print("Confidence List:", confidence_list)
    print("Result List:", result_list)


# 使用 asyncio 来运行异步函数
if __name__ == "__main__":
#    asyncio.run(main())
    result = {
        "idx": None,
        "judge_result": None,
        "diff_content": "No AI Judge",
        "judge_reason": "Json 解析失败, 非法转义"
    }

    is_valid= validate_generate_format(result)
    print(is_valid)

    result = {
                'old_file_name': 'Word_a.docx', 'new_file_name': 'Word_b.docx', 'data_type': None, 'block_name': None, 'data': {'type': 'update',
                'ai_judge':
                {
                    'judge_result': '更新', 'diff_content': '前方车辆突然加速修改为前方车辆突然减速', 'judge_reason': '内容的更新，涉及测试用例输入的具体描述，这属于功能性变化，因为它会影响测试目的和预期输出'
                },
                'old': {'content': '前方车辆突然加速', 'index': '', 'id': 24, 'row_index': 3, 'col_index': 3, 'diff_context': '', 'diff_content': '', 'position': {'x': 44.616161859537115, 'y': 83.69758926001671, 'width': 18.562069925395207, 'height': 5.796418507302977, 'page_num': 2
                            }, 'context': {'header_list': '测试用例编号|测试用例名称|测试目的|输入|预期输出|实际输出|测试结果', 'current_row': 'TC-ID-003|AEB功能测试|验证AEB功能能够在检测到碰撞风险时自动制动|前方车辆突然加速|本车自动制动，避免碰撞||', 'cell_header': '输入', 'pre_context': 'TC-ID-002|LKA功能测试|验证LKA功能能够识别车道线并纠正车辆偏离|车辆偏离车道线|方向盘自动纠正，使车辆回到车道中央||', 'next_context': ''
                            }, 'data': '', 'head_content': ''
                        }, 'new': {'content': '前方车辆突然减速', 'index': '', 'id': 24, 'row_index': 3, 'col_index': 3, 'diff_context': '', 'diff_content': '', 'position': {'x': 44.50697423820116, 'y': 79.75412670576183, 'width': 18.788846088977223, 'height': 5.79641125761127, 'page_num': 2
                            }, 'context': {'header_list': '测试用例编号|测试用例名称|测试目的|输入|预期输出|实际输出|测试结果', 'current_row': 'TC-ID-003|AEB功能测试|验证AEB功能能够在检测到碰撞风险时自动制动|前方车辆突然减速|本车自动制动，避免碰撞||', 'cell_header': '输入', 'pre_context': 'TC-ID-002|LKA功能测试|验证LKA功能能够识别车道线并纠正车辆偏离|车辆偏离车道线|方向盘自动纠正，使车辆回到车道中央||', 'next_context': 'TC-ID-004|TSR功能测试|验证TSR功能能够识别交通标志|道路上的交通标志|仪表盘显示识别到的交通标志||'
                            }, 'data': '', 'head_content': ''
                        }
                    },
                'idx': 'e159de20-a136-459b-842d-42ae9a29d0fd'
                }

    is_valid = validate_generate_format(result)
    print(is_valid)
# AI generation end