from fastapi import APIRouter, HTTPException, Depends
from src.common.config_manager import config_manager
from src.common.exceptions import PermissionError

router = APIRouter(
    prefix="/config",
    tags=["config"],
    responses={404: {"description": "Not found"}},
)

@router.get("")
async def get_config():
    """获取系统配置（仅开发环境可用）"""
    if not config_manager.get("app.debug", False):
        raise PermissionError("Configuration endpoint is only available in development environment")
    return config_manager.get_all()


