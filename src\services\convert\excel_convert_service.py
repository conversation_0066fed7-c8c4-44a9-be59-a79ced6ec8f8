from .base_converter_service import BaseConverterService
from typing import Optional, List
import os
import logging
import win32com.client as win32
import pythoncom
import fitz  # PyMuPDF

logger = logging.getLogger(__name__)


class ExcelConvertService(BaseConverterService):
    """修正版Excel转换服务 - 解决ReleaseObject问题"""

    def convert_to_images(self, excel_path: str, output_prefix: Optional[str] = None,
                          format: str = "png", dpi: int = 300) -> List[str]:
        """
        将Excel每个Sheet转换为一张完整图片
        :param excel_path: Excel文件路径
        :param output_prefix: 输出文件前缀
        :param format: 图片格式(png/jpg)
        :param dpi: 输出图片分辨率
        :return: 生成的图片路径列表
        """
        # 处理输出路径
        if output_prefix is None:
            output_prefix = os.path.splitext(excel_path)[0]

        # 确保输出目录存在
        output_dir = os.path.dirname(output_prefix)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        excel = None
        wb = None
        output_files = []

        try:
            # 初始化Excel应用
            pythoncom.CoInitialize()
            excel = win32.DispatchEx("Excel.Application")
            excel.Visible = False
            excel.DisplayAlerts = False

            # 打开工作簿
            wb = excel.Workbooks.Open(os.path.abspath(excel_path))

            # 处理每个工作表
            for sheet in wb.Worksheets:
                try:
                    # 清理工作表名作为文件名
                    sheet_name = "".join(
                        c if c.isalnum() or c in ('_', '-') else '_'
                        for c in sheet.Name
                    )
                    output_path = f"{output_prefix}_{sheet_name}.{format.lower()}"

                    # 设置页面布局确保不拆分
                    sheet.PageSetup.Zoom = False
                    sheet.PageSetup.FitToPagesTall = 1
                    sheet.PageSetup.FitToPagesWide = 1

                    # 临时PDF路径
                    temp_pdf = output_path.replace(f".{format}", ".pdf")

                    # 导出为PDF
                    sheet.ExportAsFixedFormat(
                        Type=0,  # xlTypePDF
                        Filename=temp_pdf,
                        Quality=1,  # xlQualityStandard
                        IncludeDocProperties=False,
                        IgnorePrintAreas=False,
                        OpenAfterPublish=False
                    )

                    # 将PDF转换为图片
                    if os.path.exists(temp_pdf):
                        try:
                            doc = fitz.open(temp_pdf)
                            page = doc.load_page(0)
                            pix = page.get_pixmap(
                                dpi=dpi,
                                colorspace="rgb",
                                alpha=False,
                                matrix=fitz.Matrix(dpi / 72, dpi / 72)
                            )
                            pix.save(output_path)
                            output_files.append(output_path)
                            logger.info(f"已生成工作表 [{sheet.Name}] 图片: {output_path}")
                        finally:
                            doc.close()
                            os.remove(temp_pdf)
                except Exception as sheet_error:
                    logger.error(f"工作表 [{sheet.Name}] 转换失败: {str(sheet_error)}")
                    continue

            return output_files

        except Exception as e:
            # 清理已生成的文件
            for f in output_files:
                try:
                    os.remove(f)
                except:
                    pass
            raise RuntimeError(f"Excel转换失败: {str(e)}") from e
        finally:
            # 正确释放Excel资源
            try:
                if wb:
                    wb.Close(SaveChanges=False)
                if excel:
                    excel.Quit()
            except:
                pass
            pythoncom.CoUninitialize()
