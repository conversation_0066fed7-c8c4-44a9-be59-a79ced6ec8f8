"""Context object passed to agents during execution."""
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from pydantic import BaseModel
from loguru import logger


class AgentContext(BaseModel):
    """Context object passed to agents during execution."""
    session_id: str
    input_data: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None


class AgentConfig(BaseModel):
    """Configuration for an agent."""
    name: str
    type: str
    description: str
    priority: int = 1
    parameters: Dict[str, Any] = {}


class BaseAgent(ABC):
    """Base class for all agents in the system."""

    def __init__(self, config: AgentConfig):
        self.config = config
        self.logger = logger.bind(agent_name=config.name)
        self.initialized = False

    async def initialize(self) -> None:
        """Initialize the agent with necessary resources."""
        if not self.initialized:
            self.logger.info(f"Initializing agent: {self.config.name}")
            await self._setup()
            self.initialized = True

    async def cleanup(self) -> None:
        """Cleanup resources used by the agent."""
        if self.initialized:
            self.logger.info(f"Cleaning up agent: {self.config.name}")
            await self._teardown()
            self.initialized = False

    @abstractmethod
    async def execute(self, context: AgentContext) -> Dict[str, Any]:
        """Execute the agent's main logic."""
        pass

    @abstractmethod
    async def _setup(self) -> None:
        """Setup agent-specific resources."""
        pass

    @abstractmethod
    async def _teardown(self) -> None:
        """Cleanup agent-specific resources."""
        pass

    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the agent."""
        return {
            "name": self.config.name,
            "type": self.config.type,
            "initialized": self.initialized,
            "priority": self.config.priority
        }