from typing import Dict, Type, overload
import yaml
from .base import BaseLLMService, LLMConfig
from .openai_service import OpenAIService
import os

class LLMServiceFactory:
    """Factory for creating LLM service instances."""
    _services: Dict[str, Type[BaseLLMService]] = {
        "openai": OpenAIService,
        # Add other service implementations here
    }
    @classmethod
    def create_service(cls, provider: str) -> BaseLLMService:
        """Create an LLM service instance."""
        if provider not in cls._services:
            raise ValueError(f"Unsupported LLM provider: {provider}")
        # Load configuration
        current_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(current_dir, "models.yaml")
        
        print(f"Current working directory: {current_dir}")
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        
        #if provider not in config:
        #    raise ValueError(f"Configuration not found for provider: {provider}")
        # Create service instance
        service_class = cls._services[provider]
        service_config = LLMConfig(**config[provider])
        return service_class(service_config)
    
    @classmethod
    def register_service(cls, name: str, service_class: Type[BaseLLMService]) -> None:
        """Register a new LLM service implementation."""
        cls._services[name] = service_class




