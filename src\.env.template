# ==================== 日志配置 ====================
# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# ==================== 服务器配置 ====================
# 服务器主机地址
SERVER_HOST=0.0.0.0

# 服务器端口
SERVER_PORT=8000

# 工作进程数
WORKERS=4

# ==================== 应用配置 ====================
# 应用密钥，用于加密等
SECRET_KEY=your-secret-key-here

# 环境设置：development, testing, production
ENVIRONMENT=development

# 调试模式：true/false
DEBUG=true

# ==================== OpenAI配置 ====================
# OpenAI API密钥
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# ==================== 百度文心配置 ====================
# 百度文心API密钥
ERNIE_API_KEY=your-ernie-api-key
ERNIE_SECRET_KEY=your-ernie-secret-key

# ==================== 智谱AI配置 ====================
# 智谱AI API密钥
ZHIPU_API_KEY=your-zhipu-api-key

# ==================== 通义千问配置 ====================
# 通义千问API密钥
QIANWEN_API_KEY=your-qianwen-api-key

# ==================== 讯飞星火配置 ====================
# 讯飞星火配置
SPARK_APP_ID=your-spark-app-id
SPARK_API_KEY=your-spark-api-key
SPARK_API_SECRET=your-spark-api-secret

# ==================== 数据库配置 ====================
# 数据库URL（如果使用）
DATABASE_URL=sqlite:///./bibrain.db



# ==================== 安全配置 ====================
# 允许的主机列表（逗号分隔）
ALLOWED_HOSTS=localhost,127.0.0.1

# Token过期时间（分钟）
TOKEN_EXPIRE_MINUTES=60 