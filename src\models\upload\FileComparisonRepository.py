from sqlalchemy.orm import Session

from src.models.task.models import FileComparisonTask


class FileComparisonRepository:
    def __init__(self, db: Session):
        self.db = db

    async def create_task(self, task: FileComparisonTask) -> FileComparisonTask:
        """
        创建一个新的文件比对任务
        """
        try:
            self.db.add(task)
            await self.db.commit()
            await self.db.refresh(task)
            return task
        except Exception as e:
            await self.db.rollback()
            raise RuntimeError(f"数据库创建任务失败: {e}")

    async def get_task_by_id(self, task_id: str) -> FileComparisonTask:
        """
        根据任务 ID 查询任务
        """
        return await self.db.query(FileComparisonTask).filter_by(task_id=task_id).first()

    async def update_task_status(self, task_id: str, status: str) -> None:
        """
        更新任务的状态
        """
        task = await self.get_task_by_id(task_id)
        if task:
            task.status = status
            await self.db.commit()
        else:
            raise ValueError(f"任务 {task_id} 不存在")
